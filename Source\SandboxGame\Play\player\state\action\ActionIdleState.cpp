#include "ActionIdleState.h"
#include "PlayerControl.h"
#include "InputInfo.h"
#include "PermitsDef.h"
#include "ActorHorse.h"
#include "backpack.h"
#include "GameCamera.h"
#include "GunUseComponent.h"
#include "special_blockid.h"
#include "BlockMaterialMgr.h"
#include "TouchControl.h"
#include "ActorVillager.h"
#include "IClientGameManagerInterface.h"
#include "PlayerAttrib.h"
#include "ClientInfoProxy.h"
#include "BindActorComponent.h"
#include "CarryComponent.h"
#include "RiddenComponent.h"
#include "GameInfoProxy.h"
#include "AttackingTargetComponent.h"
#include "FishingComponent.h"
#include <luaConstProxy/LuaInterfaceProxy.h>
#include "SandboxActorObject.h"
#include "ActorVehicleAssemble.h"
#include "CommonUtil.h"
#include "PlayerFSM.h"
#include "ComboAttackState.h"
#include "ActorTrainCar.h"
#include "SkillComponent.h"
#include "PlotComponent.h"
#include "CustomGunUseComponent.h"
#include "PlayerInputHelper.h"
#include "PlayerAttrib.h"
#include "LockCtrlComponent.h"
#include "container_socdoor.h"
#include "BlockArchitecturalBase.h"
using namespace MINIW;
using namespace MNSandbox;


enum EnumActionResult
{
	SUCCESS,
	PASS,
	FAIL
};


ActionIdleState::ActionIdleState(PlayerControl* host)
	: PlayerState(host),
	ActionIdleStateGunAdvance(host),
	m_ActionStartMark(0),
	m_RMouseUpd(false),
	m_curItemdef(nullptr),
	m_curItemId(-1),
	m_curToolDef(nullptr),
	m_curToolId(-1)
{
	m_StateID = "ActionIdle";
	m_BuildCoolDown = -1;
	m_UseBlock = 0;
}

ActionIdleState::~ActionIdleState()
{

}

void ActionIdleState::doBeforeEntering()
{
	ActionIdleStateGunAdvance::Enter();

	m_ActionStartMark = -1;
	//中断连击，右键蓄力需要保持连贯
	if (ownerFSM)
	{
		ComboAttackState* comboState = dynamic_cast<ComboAttackState*>(ownerFSM->getLastState());
		if (comboState && comboState->LeaveByCharge())
		{
			triggerRightClickDownOrUseActionOnce = true;
		}
	}
}

bool ActionIdleState::canSkipSkillUse(const ItemSkillDef* skilldef)
{
	if (GetClientInfoProxy()->isMobile())
	{
		bool isWeaponTouchLeft = true;
		const ItemDef* def = GetDefManagerProxy()->getItemDef(m_Host->getCurToolID());
		if (def && def->UseTarget == ITEM_USE_GUN)
		{
			if (0 != m_Host->getTouchControl()->getShootPosition())
			{
				isWeaponTouchLeft = false;
			}
		}
		if (IsUseActionTrigger()/*m_Host->m_InputInfo->useActionTrigger*/ && !isWeaponTouchLeft && skilldef->Key == 2 && def->ID == 12311)//demon 沙虫炮点击右边使用按钮 需要正常开枪
		{
			return true;
		}
		if ((IsInputTap()/*m_Host->m_InputInfo->tap*/ && skilldef->Key != 2)	//tap屏幕
			|| (IsUseActionTrigger()/*m_Host->m_InputInfo->useActionTrigger*/ && isWeaponTouchLeft && skilldef->Key == 2)) //按下按钮
		{

		}
		else if (IsUseActionTrigger()/*m_Host->m_InputInfo->useActionTrigger*/ && skilldef->Key == 2) //按下按钮
		{
			const GunDef* gunDef = GetDefManagerProxy()->getGunDef(def->ID);
			if (gunDef)
				return true;
		}
		else
		{
			return true;
		}
	}
	else
	{
		if ((IsLeftClickDown()&& skilldef->Key == 0)//(m_Host->m_InputInfo->leftClickDown && skilldef->Key == 0)
			|| (IsRightClickDown() && skilldef->Key == 1)	//tap屏幕
			|| (IsRightClickDown() && skilldef->Key == 2)) //按下按钮
		{
		}
		else
		{
			return true;
		}
	}
	return false;
}

bool ActionIdleState::canActivateBlock(bool& handle)
{
	if ((GetClientInfoProxy()->isMobile() && IsInputTap()/*m_Host->m_InputInfo->tap*/) || (!GetClientInfoProxy()->isMobile() && IsRightClickDown()))
	{
		int itemid = m_Host->getCurToolID();
		bool pickliquid = itemid > 0 && GetDefManagerProxy()->getItemDef(itemid, true)->UseTarget == ITEM_USE_CLICKLIQUID;
		int picktype = m_Host->doPick(pickliquid);
		if (picktype == 1)
		{
			int blockid = m_Host->getWorld()->getBlockID(m_Host->m_PickResult.block);
			SandboxContext context;
			context.SetData_Number("blockid", blockid);
			context.SetData_UserObject("blockpos", m_Host->m_PickResult.block);
			Event().Emit("interactBlock", context);
			if (blockid == 758)
			{
				SandboxEventDispatcherManager::GetGlobalInstance().Emit("OPEN_BLOCK_BOX", SandboxContext(nullptr).SetData_Number("blockid", blockid).SetData_UserObject("blockpos", m_Host->m_PickResult.block));
			}
			BlockMaterial* pmtl = g_BlockMtlMgr.getMaterial(blockid);
			if (CanActivateBlock(blockid, itemid) && m_Host->checkActionAttrState(ENABLE_OPERATEBLOCK))
			{
				if (pmtl && pmtl->DoOnTrigger(m_Host->getWorld(), m_Host->m_PickResult.block, m_Host->m_PickResult.face, m_Host))
				{
					// 观察者事件接口
					WCoord& coordB = m_Host->m_PickResult.block;
					ObserverEvent_ActorBlock obevent((long long)m_Host->getObjId(), pmtl->getBlockResID(), coordB.x, coordB.y, coordB.z);
					GetObserverEventManager().OnTriggerEvent("Block.Trigger", &obevent);

					m_UseBlock = 0.0f;
					handle = true;
					m_Host->getBody()->playAttack();
					if (m_Host->getViewMode() == 0)
					{
						m_Host->m_PlayerAnimation->performDig();
					}
					return true;
				}
			}
		}
	}
	return false;
}

bool ActionIdleState::skillCostEnough(const ItemSkillDef* skilldef)
{
	if (!(GetWorldManagerPtr() && GetWorldManagerPtr()->isGodMode()))
	{
		for (int j = 0; j < (int)skilldef->ItemSkillCosts.size(); j++)
		{
			if (skilldef->ItemSkillCosts[j].CostType == 0)//消耗耐久度
			{
				if (m_Host->getEquipItemDuration(EQUIP_WEAPON) < skilldef->ItemSkillCosts[j].CostVal)
				{
					//ge GetGameEventQue().postInfoTips(6526);
					InfoTips(6526);
					return false;
				}
			}
			else if (skilldef->ItemSkillCosts[j].CostType == 1 || skilldef->ItemSkillCosts[j].CostType == 3)//消耗饥饿度或者体力值
			{
				if (m_Host->getPlayerAttrib())
				{
					if (m_Host->getPlayerAttrib()->strengthFoodShowState() != SFS_Empty)
					{
						if (!m_Host->getPlayerAttrib()->useCompatibleStrength())
						{
							if (m_Host->getPlayerAttrib()->getFoodLevel() < skilldef->ItemSkillCosts[j].CostVal)
							{
								//GetGameEventQue().postInfoTips(6527);
                                InfoTips(6527);
								return false;
							}
						}
						else
						{
							if (m_Host->getPlayerAttrib()->getStrength() < skilldef->ItemSkillCosts[j].CostVal)
							{
								//GetGameEventQue().postInfoTips(1571);
                                 InfoTips(1571);
								return false;
							}
						}
					}
				}
			}
			else if (skilldef->ItemSkillCosts[j].CostType == 2)//消耗道具
			{
				if (skilldef->ItemSkillCosts[j].CostTarget > 0)
				{
					int bulletCount = m_Host->getBackPack()->getItemCountInNormalPack(skilldef->ItemSkillCosts[j].CostTarget);
					//看数量是否够
					if (bulletCount < skilldef->ItemSkillCosts[j].CostVal)
					{
						const ItemDef* def = GetDefManagerProxy()->getItemDef(skilldef->ItemSkillCosts[j].CostTarget);
						if (def)
						{
							const char* info = GetDefManagerProxy()->getStringDef(6528);
							char str[256];
							sprintf(str, "%s%s", def->Name.c_str(), info);
							//ge GetGameEventQue().postInfoTips(str);
							CommonUtil::GetInstance().PostInfoTips(str);
						
							
						}
						return false;
					}
				}
			}
		}
	}
	return true;
}

bool ActionIdleState::doSkillUse(int skillId, Rainbow::Vector3f& currentEyePos, Rainbow::Vector3f& currentDir, std::vector<WCoord>& wCoordVec, std::vector<WORLD_ID>& idvec, WCoord& centerPos)
{
	m_Host->setCurItemSkillID(skillId);
	const ItemSkillDef* skilldef = GetDefManagerProxy()->getItemSkillDef(skillId);

	//判断一下有没有刷怪效果
	bool haveCallMob = false;
	for (int j = 0; j < (int)skilldef->SkillFuncions.size(); j++)
	{
		ItemSkillDef::SkillFuncionsDef* functiondef = (ItemSkillDef::SkillFuncionsDef*)&skilldef->SkillFuncions[j];
		if (functiondef->oper_id == 5) //召唤
		{
			haveCallMob = true;
		}
	}

	std::vector<WORLD_ID> idvecPhysic;
	if (skilldef->TargetType == 0)
	{
		idvec = m_Host->doPickActorByItemSkill(skillId, centerPos, currentEyePos, currentDir);
		if (idvec.size() == 0 && skilldef->SkillType == 0)
		{
			m_Host->setCurItemSkillID(0);
			return false;
		}
		//如果有召唤效果的话也要选择一下方块,以便刷怪
		if (haveCallMob)
		{
			wCoordVec = m_Host->doPickBlockByItemSkill(skillId, currentEyePos, currentDir);
		}

		// 102 击飞技能 要有物理属性
		bool isKnockSkill = false;
		for (int j = 0; j < (int)skilldef->SkillFuncions.size(); j++)
		{
			ItemSkillDef::SkillFuncionsDef* functiondef = (ItemSkillDef::SkillFuncionsDef*)&skilldef->SkillFuncions[j];
			if (functiondef->oper_id == 4)
			{
				isKnockSkill = true;
			}
		}
		if (isKnockSkill)
		{
			idvecPhysic = m_Host->doPickPhysicsActorByItemSkill(skillId, centerPos, currentEyePos, currentDir);
			if (idvecPhysic.size() > 0)
			{
				idvec.insert(idvec.end(), idvecPhysic.begin(), idvecPhysic.end());
			}
		}
	}
	else if (skilldef->TargetType == 1)
	{
		wCoordVec = m_Host->doPickBlockByItemSkill(skillId, currentEyePos, currentDir);
		if (wCoordVec.size() == 0 && skilldef->SkillType == 0)
		{
			m_Host->setCurItemSkillID(0);
			return false;
		}
	}
	else if (skilldef->TargetType == 2)
	{
		//判断重力手套抓取之前已经有物体绑定则continue
		if (skillId == 112)
		{
			auto bindAComponent = m_Host->getBindActorCom();
			if (bindAComponent && bindAComponent->hasBindChildren())//
				return false;
		}

		//判断重力手套投射之前没有抓取物体则continue
		if (skillId == 113)
		{
			auto bindAComponent = m_Host->getBindActorCom();
			if (bindAComponent && !bindAComponent->hasBindChildren())//
				return false;
		}
		idvec = m_Host->doPickPhysicsActorByItemSkill(skillId, centerPos, currentEyePos, currentDir);
		if (idvec.size() == 0 && skilldef->SkillType == 0)
		{
			m_Host->setCurItemSkillID(0);
			return false;
		}
	}
	return true;
}

const char* ActionIdleState::itemSkillUse(bool& handle)
{
	if (m_Host == NULL) return "";

	const ItemDef* def = m_curItemdef;
	if (m_curItemId != m_Host->getCurToolID())
	{
		def = GetDefManagerProxy()->getItemDef(m_Host->getCurToolID());
		m_curItemdef = (ItemDef*)def;
		m_curItemId = m_Host->getCurToolID();
	}

	if (def == NULL) return "";

	bool isFishingRod = false;

	const ToolDef* toolDef = m_curToolDef;
	if (m_curToolId != def->ID)
	{
		toolDef = GetDefManagerProxy()->getToolDef(def->ID);
		m_curToolDef = (ToolDef*)toolDef;
		m_curToolId = def->ID;
	}
	
	if (toolDef && toolDef->isFishingRod())
	{
		isFishingRod = true;
	}

	for(int i=0; i< (int)def->SkillID.size(); i++)
	{
		const ItemSkillDef* skilldef = GetDefManagerProxy()->getItemSkillDef(def->SkillID[i]);
		if (skilldef)
		{
			if (skilldef->SkillType == 2)
				return "";

			if (canSkipSkillUse(skilldef))
			{
				continue;
			}

			//判断有没有点击到可激活的东西
			if (canActivateBlock(handle))
			{
				return "";
			}

			if (skilldef->Key == 0)
			{
				SetLeftClickDown(false);//m_Host->m_InputInfo->leftClickDown = false;
				SetInputTap(false);//m_Host->m_InputInfo->tap = false;
			}
			else if (skilldef->Key == 1)
			{
				//m_Host->m_InputInfo->rightClickDown = false;
				SetRightClickDown(false);
				SetInputTap(false);//m_Host->m_InputInfo->tap = false;
			}
			else if (skilldef->Key == 2)
			{
				if (def->ID != ITEM_PAINTTANK)	//20211026 使用喷漆罐后还能继续执行其他使用逻辑 code by:keguanqiang
				{
					//m_Host->m_InputInfo->rightClickDown = false;
					SetRightClickDown(false);
					SetUseActionTrigger(false);//m_Host->m_InputInfo->useActionTrigger = false;
					SetRightClick(false);//m_Host->m_InputInfo->rightClick = false;
				}
			}

			//技能cd的判断
			if (m_Host->getSkillCD(def->SkillID[i] + 1000000) > 0)
			{
				if (!isFishingRod)
				{
					if (def->ID == ITEM_PAINTTANK)
						//ge GetGameEventQue().postInfoTips(30622);
						InfoTips(30622);
					else
						//ge GetGameEventQue().postInfoTips(4881);
						InfoTips(4881);
				}
				return "";
			}

			//消耗是否足够的判断
			if (!skillCostEnough(skilldef))
			{
				return "";
			}

			if (GetClientInfoProxy()->isMobile() && skilldef->Key == 2)
			{
				m_Host->m_CurMouseX = 0.5f;
				m_Host->m_CurMouseY = 0.5f;
			}
			else
			{
				m_Host->m_CurMouseX = m_Host->m_InputInfo->clickPosX;
				m_Host->m_CurMouseY = m_Host->m_InputInfo->clickPosY;
			}

			std::vector<WORLD_ID> idvec;
			std::vector<WCoord> wCoordVec;
			Rainbow::Vector3f currentEyePos, currentDir;
			WCoord centerPos;
			if (!doSkillUse(def->SkillID[i], currentEyePos, currentDir, wCoordVec, idvec, centerPos))
			{
				continue;
			}

			if (skilldef->ChargeTime > 0.001 && skilldef->ChargeType != 0)
			{
				handle = true;
				RiddenComponent* riddenComp = g_pPlayerCtrl->getRiddenComponent();
				bool isRiding = riddenComp ? riddenComp->isRiding() : false;
				if (isFishingRod && !m_Host->isInWater() && m_Host->getLocoMotion()->m_OnGround && !m_Host->getFlying() && !isRiding)
				{
					auto* attrib = m_Host->getLivingAttrib();
					if (!(attrib && attrib->hasHarmfulBuff()))
					{
						if (toolDef && toolDef->IsModFishRod())
						{
							//FISHMOD_TODO: 测试新鱼竿

						}
						else
							return "ToChargeToFishing";
					}
				}
				return "ToItemSkillUse";
			}
			else
			{
				m_Host->useItemSkill(m_Host->getCurToolID(), PLAYEROP_STATUS_BEGIN, def->SkillID[i], currentEyePos, currentDir, wCoordVec, idvec, centerPos);
				m_Host->setCurItemSkillID(def->SkillID[i]);
				m_Host->m_PlayerAnimation->performArrowAttackShoot();
				m_Host->setCurItemSkillID(0);
				handle = true;
			}
		}
	}
	return "";
}
//
//const char* ActionIdleState::footballerState(float dtime)
//{
//	if (GetClientInfoProxy()->isMobile())
//	{
//		if (m_Host->m_InputInfo->useActionTrigger)
//		{
//			auto ball = m_Host->getCatchBall();
//			if (ball)
//			{
//				return "ToShoot";
//			}
//			else
//			{
//				if(m_Host->doTackle())
//					return "ToTackle";
//			}
//		}
//
//		if (IsUsePassOrCatchBall()/*m_Host->m_InputInfo->usePassOrCatchBall*/)
//		{
//			auto ball = m_Host->getCatchBall();
//			if (ball)
//			{
//				return "ToPassBall";
//			}
//		}
//
//		if(IsUsePassOrCatchBallEnd()/*m_Host->m_InputInfo->usePassOrCatchBallEnd*/)
//		{
//			auto ball = m_Host->getCatchBall();
//			if (!ball)
//			{
//				int picktype = m_Host->doPick(false);
//
//				ActorBall *ball = NULL;
//				if (picktype == 2)
//				{
//					ball = dynamic_cast<ActorBall *>(m_Host->m_PickResult.actor);
//				}
//				FootballStateAction::doCatchBall(m_Host, ball);
//				//m_Host->doCatchBall(ball);
//			}
//		}
//	}
//	else
//	{
//		if (m_Host->m_InputInfo->rightClickDown)
//		{
//			auto ball = m_Host->getCatchBall();
//			if (ball)
//			{
//				return "ToShoot";
//			}
//			else
//			{
//				if(m_Host->doTackle())
//					return "ToTackle";
//			}
//		}
//
//		if (m_Host->m_InputInfo->leftClickDown)
//		{
//			//if(m_Host)
//			auto ball = m_Host->getCatchBall();
//			if (ball)
//			{
//				return "ToPassBall";
//			}
//		}
//
//		if (m_Host->m_InputInfo->leftClickUp)
//		{
//			auto ball = m_Host->getCatchBall();
//			if (!ball)
//			{
//				int picktype = m_Host->doPick(false);
//
//				ActorBall *ball = NULL;
//				if (picktype == 2)
//				{
//					ball = dynamic_cast<ActorBall *>(m_Host->m_PickResult.actor);
//				}
//
//				FootballStateAction::doCatchBall(m_Host, ball);
//				//m_Host->doCatchBall(ball);
//			}
//		}
//	}
//	
//	return "";
//}

const char* ActionIdleState::gravityGunState(float dtime)
{
	if (GetClientInfoProxy()->isMobile())
	{
		if (IsUseActionTrigger()/*m_Host->m_InputInfo->useActionTrigger*/)
		{
			auto actor = m_Host->getCatchGravityActor();
			if (actor)
			{
				return "ToGravityGunCharge";
			}
		}

		if (IsUsePassOrCatchBallEnd()/*m_Host->m_InputInfo->usePassOrCatchBallEnd*/)
		{
			auto actor = m_Host->getCatchGravityActor();
			if (!actor)
			{
				int picktype = m_Host->doPick(false);

				ClientActor* pActor = NULL;
				if (picktype == 2)
				{
					pActor = static_cast<ClientActor*>(m_Host->m_PickResult.actor);
				}

				m_Host->doCatchGravityActor(pActor);
			}
		}
	}
	else
	{
		if (IsRightClickDown())
		{
			auto actor = m_Host->getCatchGravityActor();
			if (actor)
			{
				return "ToGravityGunCharge";
			}
		}

		if (m_Host->m_InputInfo->leftClickUp)
		{
			auto actor = m_Host->getCatchGravityActor();
			if (!actor)
			{
				int picktype = m_Host->doPick(false);

				ClientActor* pActor = NULL;
				if (picktype == 2)
				{
					pActor = static_cast<ClientActor*>(m_Host->m_PickResult.actor);
				}

				m_Host->doCatchGravityActor(pActor);
			}
		}
	}

	return "";
}

//const char* ActionIdleState::basketballerState(float dtime)
//{
//	if (GetClientInfoProxy()->isMobile())
//	{
//		auto ball = m_Host->getCatchBall();
//		if (ball)
//		{
//			auto basketball = dynamic_cast<ActorBasketBall*>(ball);
//			if (basketball)
//				basketball->tryAimTarget(m_Host);
//		}
//		if (IsUseActionTrigger()/*m_Host->m_InputInfo->useActionTrigger*/)
//		{
//			if (ball)
//			{
//				return "ToShootBasketBall";
//			}
//			else
//			{
//				return "ToObstruct";
//			}
//		}
//
//		if (IsUsePassOrCatchBall()/*m_Host->m_InputInfo->usePassOrCatchBall*/)
//		{
//			if (ball)
//			{
//				return "ToPassBasketBall";
//			}
//			else
//			{
//				if (m_Host->checkBasketBallGrab())
//				{
//					return "ToGrab";
//				}
//			}
//		}
//
//		if (IsInputJump()/*m_Host->m_InputInfo->jump*/)
//		{
//			if (!ball)
//			{	//盖帽
//				return "ToBlockShot";
//			}
//		}
//
//		if(ball && !m_Host->isSkillCD())
//		{	
//			if (IsInputJump()/*m_Host->m_InputInfo->jump*/ != m_boldJump){
//				m_boldJump = true;
//				m_Host->setSkillCD(ITEM_BASKETBALLWEAR, (float)g_WorldMgr->m_SurviveGameConfig->basketballConfig.dribble_rush_cd);
//			}else{
//				m_boldJump = false;
//			}
//		}else{
//			m_boldJump = false;
//		}
//
//		if (ball != NULL  && m_boldJump && m_Host->doRunDribbleRunBasketBall(ball))
//		{
//			//带球冲刺
//			m_boldJump =false;
//			
//			return "ToDribbleRunBasketBall";
//			
//		}	
//		
//	}
//	else
//	{
//		auto ball = m_Host->getCatchBall();
//		if (ball)
//		{
//			auto basketball = dynamic_cast<ActorBasketBall*>(ball);
//			if (basketball)
//				basketball->tryAimTarget(m_Host);
//		}else
//		{
//			m_Host->setBasketBallLockState(false);
//		}
//
//		if (IsRightClickDown())
//		{
//			if (ball)
//			{
//				return "ToShootBasketBall";
//			}
//			else
//			{
//				return "ToObstruct";
//			}
//		}
//
//		if (m_Host->m_InputInfo->leftClickDown)
//		{
//			//if(m_Host)
//			if (ball)
//			{
//				return "ToPassBasketBall";
//			}
//			else
//			{
//				if (m_Host->checkBasketBallGrab())
//				{
//					return "ToGrab";
//				}
//			}
//		}
//
//		if (IsInputJump()/*m_Host->m_InputInfo->jump*/)
//		{
//			if (!ball)
//			{	//盖帽
//				return "ToBlockShot";
//			}
//		}
//		if(ball && !m_Host->isSkillCD())
//		{	
//			if (IsInputJump()/*m_Host->m_InputInfo->jump*/ != m_boldJump){
//				m_boldJump = true;
//				m_Host->setSkillCD(ITEM_BASKETBALLWEAR, (float)g_WorldMgr->m_SurviveGameConfig->basketballConfig.dribble_rush_cd);
//			}else{
//				m_boldJump = false;
//			}
//		}else{
//			m_boldJump = false;
//		}
//
//		if (ball != NULL  && m_boldJump && m_Host->doRunDribbleRunBasketBall(ball))
//		{
//			//带球冲刺
//			m_boldJump =false;
//			
//			return "ToDribbleRunBasketBall";
//		}	
//	}
//
//	return "";
//}

const char* ActionIdleState::carringState(float dtime)
{
	if (m_Host->m_InputInfo && (IsInputTap()/*m_Host->m_InputInfo->tap*/ || IsRightClickDown()))
	{
		if (IsRightClickDown())
		{
			SetSwitchTick(-1);//m_Host->m_SwitchTick = -1;
		}

		int itemid = m_Host->getCurToolID();
		bool pickliquid = itemid > 0 && GetDefManagerProxy()->getItemDef(itemid, true)->UseTarget == ITEM_USE_CLICKLIQUID;
		int picktype = m_Host->doPick(pickliquid, true);

		auto CarryComp = m_Host->getCarryComponent();

		if (picktype == 1 && CarryComp && CarryComp->isCarrying())
		{
			WCoord placepos = NeighborCoord(m_Host->m_PickResult.block, DIR_POS_Y);
			if (!m_Host->getWorld()->getBlockMaterial(placepos)->isSolid())
			{
				m_Host->tryCarryActor(NULL, BlockCenterCoord(placepos));
			}
		}
	}

	return "";
}

bool ActionIdleState::LogicToState(int logicType)
{
	m_nextState.clear();
	switch (logicType)
	{
	case Logic_UseGun: return logicToUseGun();
	case Logic_CheckItem: return logicToCheckItem();
	case Logic_ItemCanUse: return logicToItemCanUse();
	case Logic_BlockCanBreak: return logicToBlockCanBreak();
	case Logic_BlockCanPlace: return logicToBlockCanPlace();
	case Logic_RiderSpecialSkill: return logicToRiderSpecialSkill();
	case Logic_TriggerToolMode: return logicToTriggerToolMode();
	case Logic_UseMusicItem: return logicToUseMusicItem();
	default: break;
	}
	return false;
}

void ActionIdleState::InfoTips(int id) {

	MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
		SetData_String("info", GetDefManagerProxy()->getStringDef(id));
	if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
		MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GIE_INFO_TIPS", sandboxContext);
}

bool ActionIdleState::logicToUseMusicItem()
{
	int itemid = m_Host->getCurToolID();
	const ItemDef* def = GetDefManagerProxy()->getItemDef(itemid);
	if (def == NULL) return false;

	bool bContinue = false;
	if (m_Host->getWorld() && m_Host->getWorld()->IsUGCEditMode()) {
		bContinue = m_Host->m_InputInfo->leftClickDown;
	}
	else {
		bContinue = m_Host->m_InputInfo->rightClickDown;
	}
		
	if (def->UseTarget == ITEM_USE_MUSIC_ITEM && (bContinue || IsUseAction()/*m_Host->m_InputInfo->useAction*/))
	{
		if (ROOM_SERVER_RENT != GetGameInfoProxy()->GetRoomHostType())
		{

			SandboxResult resultcanUseItem = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_canUseItem",
				SandboxContext(nullptr).SetData_Number("uin", m_Host->getUin()).SetData_Number("itemid", itemid));
			bool canUseItemFlag = false;
			if (resultcanUseItem.IsExecSuccessed())
			{
				canUseItemFlag = resultcanUseItem.GetData_Bool();
			}
			if (!canUseItemFlag)
			{
				m_Host->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 451);
				return false;
			}
		}


		//变身皮肤
		if (!m_Host->isShapeShift()) {
			m_nextState = "ToMusic";
			return true;
		}
	}
	return false;
}

bool ActionIdleState::logicToUseGun()
{
	if (m_Host->getCustomGunDef())
	{
		return ProcessReloadAndLoad(m_nextState);
	}
	CustomGunDef* originCustomGunDef = GetDefManagerProxy()->getCustomGunDef(m_Host->getCurToolID());
	if (originCustomGunDef) return false; //新枪 数据异常

	GunUseComponent* gunLogical = m_Host->getGunLogical();
	const GunDef* gunDef = gunLogical->getGunDef();
	if (gunDef == NULL)
		return false;

	int itemid = m_Host->getCurToolID();
	const ItemDef* def = GetDefManagerProxy()->getItemDef(itemid);
	if (def == NULL) return false;

	if (def->UseTarget == ITEM_USE_GUN)
	{
		if (m_Host->m_InputInfo->reload)
		{
			//只要子弹不满就进入gunuse状态
			int maxMagazines = gunDef->Magazines;
			SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("WeaponSkin_System_magazines", SandboxContext(nullptr)
				.SetData_Number("itemid", gunDef->ID)
				.SetData_Number("uin", m_Host ? m_Host->getUin() : 0)
			);
			if (result.IsExecSuccessed())
			{
				maxMagazines = maxMagazines + (int)result.GetData_Number("nvalue");
			}
			if (gunLogical->getMagazine() < maxMagazines)
			{
				m_nextState = "ToGunUse";
				return true;
			}
			return true;
		}

		//手动枪支拉栓的逻辑
		if (gunDef->ContinuousFire == 2 &&
			gunLogical->getPulledGunId() != itemid &&
			((gunLogical->getBulletNum() > 0) || gunDef->NeedBullet == 0))
		{
			m_nextState = "ToGunUse";
			return true;
		}
	}

	return false;
}

bool ActionIdleState::logicToCheckItem()
{
	//道具未解锁
	if (!m_Host->isCurToolUnlocked()) return true;

	//没有定义道具
	int itemid = m_Host->getCurToolID();
	if (!GetDefManagerProxy()->checkItemCrc(itemid))
	{
		//ge GetGameEventQue().postInfoTips(165);
		InfoTips(165);
		return true;
	}
	return false;
}

bool ActionIdleState::logicToItemCanUse()
{

	int itemid = m_Host->getCurToolID();
	//道具权限使用
	if (ROOM_SERVER_RENT != GetGameInfoProxy()->GetRoomHostType())
	{

		SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_canUseItem",
			SandboxContext(nullptr).SetData_Number("uin", m_Host->getUin()).SetData_Number("itemid", itemid));
		bool canUseItemFlag = false;
		if (result.IsExecSuccessed())
		{
			canUseItemFlag = result.GetData_Bool();
		}
		if (!canUseItemFlag)
		{
			m_Host->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 451);
			return true;
		}
	}
	else
	{
		SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_canCSPermit",
			SandboxContext(nullptr).SetData_Number("uin", m_Host->getUin()).SetData_Number("blockid", itemid).SetData_Number("bit", CS_PERMIT_DANGER));
		bool canCSPermitFlag = false;
		if (result.IsExecSuccessed())
		{
			canCSPermitFlag = result.GetData_Bool();
		}
		if (!canCSPermitFlag)
		{
			MINIW::ScriptVM::game()->callFunction("notifyAuthorityGameInfo2Player", "iiii", m_Host->getUin(), int(PLAYER_NOTIFYINFO_TIPS), 9725, int(CS_PERMIT_DANGER));
			return true;
		}
	}

	return false;
}

bool ActionIdleState::logicToBlockCanBreak(bool showTip)
{
	int itemid = m_Host->getCurToolID();
	const char* funcname = "notifyAuthorityGameInfo2Player";

	// 租赁服房间权限 是否能破坏方块

	SandboxResult resultCanCSPermit = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_canCSPermit",
		SandboxContext(nullptr)
		.SetData_Number("uin", m_Host->getUin())
		.SetData_Number("blockid", itemid)
		.SetData_Number("bit", CS_PERMIT_DESTROY_BLOCK));
	bool canCSPermitFlag = false;
	if (resultCanCSPermit.IsExecSuccessed())
	{
		canCSPermitFlag = resultCanCSPermit.GetData_Bool();
	}
	if (!canCSPermitFlag)
	{
		if (showTip)
		{

			SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_isHost",
				SandboxContext(nullptr).SetData_Number("uin", m_Host->getUin()));
			bool isHostFlag = false;
			if (result.IsExecSuccessed())
			{
				isHostFlag = result.GetData_Bool();
			}
			if (isHostFlag)
				MINIW::ScriptVM::game()->callFunction(funcname, "iiii", m_Host->getUin(), int(PLAYER_NOTIFYINFO_TIPS), 9725, int(CS_PERMIT_DESTROY_BLOCK));
			else
				MINIW::ScriptVM::game()->callFunction(funcname, "iiii", m_Host->getUin(), int(PLAYER_NOTIFYINFO_TIPS), 9639, int(CS_PERMIT_DESTROY_BLOCK));
		}
		return true;
	}

	SandboxResult resultCanPermit = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_canPermit",
		SandboxContext(nullptr)
		.SetData_Number("uin", m_Host->getUin())
		.SetData_Number("itemid", itemid)
		.SetData_Number("bit", CS_PERMIT_DESTROY_BLOCK));
	bool canPermitFlag = false;
	if (resultCanPermit.IsExecSuccessed())
	{
		canPermitFlag = resultCanPermit.GetData_Bool();
	}
	if (!canPermitFlag)
	{
		if (showTip)
		{

			SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_isHost",
				SandboxContext(nullptr).SetData_Number("uin", m_Host->getUin()));
			bool isHostFlag = false;
			if (result.IsExecSuccessed())
			{
				isHostFlag = result.GetData_Bool();
			}
			if (isHostFlag)
				MINIW::ScriptVM::game()->callFunction(funcname, "iiii", m_Host->getUin(), int(PLAYER_NOTIFYINFO_TIPS), 9725, int(CS_PERMIT_DESTROY_BLOCK));
			else
				MINIW::ScriptVM::game()->callFunction(funcname, "iiii", m_Host->getUin(), int(PLAYER_NOTIFYINFO_TIPS), 9720, int(CS_PERMIT_DESTROY_BLOCK));
		}
		return true;
	}
	return false;
}

bool ActionIdleState::logicToBlockCanPlace(bool showTip)
{
	int itemid = m_Host->getCurToolID();
	const char* funcname = "notifyAuthorityGameInfo2Player";

	// 租赁服房间权限 是否能放置方块

	SandboxResult resultCanCSPermit = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_canCSPermit",
		SandboxContext(nullptr)
		.SetData_Number("uin", m_Host->getUin())
		.SetData_Number("blockid", itemid)
		.SetData_Number("bit", CS_PERMIT_PLACE_BLOCK));
	bool canCSPermitFlag = false;
	if (resultCanCSPermit.IsExecSuccessed())
	{
		canCSPermitFlag = resultCanCSPermit.GetData_Bool();
	}
	if (!canCSPermitFlag)
	{
		if (showTip)
		{

			SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_isHost",
				SandboxContext(nullptr).SetData_Number("uin", m_Host->getUin()));
			bool isHostFlag = false;
			if (result.IsExecSuccessed())
			{
				isHostFlag = result.GetData_Bool();
			}
			if (isHostFlag)
				MINIW::ScriptVM::game()->callFunction(funcname, "iiii", m_Host->getUin(), int(PLAYER_NOTIFYINFO_TIPS), 9725, int(CS_PERMIT_PLACE_BLOCK));
			else
				MINIW::ScriptVM::game()->callFunction(funcname, "iiii", m_Host->getUin(), int(PLAYER_NOTIFYINFO_TIPS), 9639, int(CS_PERMIT_PLACE_BLOCK));
		}
		return true;
	}

	SandboxResult resultCanPermit = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_canPermit",
		SandboxContext(nullptr)
		.SetData_Number("uin", m_Host->getUin())
		.SetData_Number("itemid", itemid)
		.SetData_Number("bit", CS_PERMIT_PLACE_BLOCK));
	bool canPermitFlag = false;
	if (resultCanPermit.IsExecSuccessed())
	{
		canPermitFlag = resultCanPermit.GetData_Bool();
	}
	if (!canPermitFlag)
	{
		if (showTip)
		{

			SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_isHost",
				SandboxContext(nullptr).SetData_Number("uin", m_Host->getUin()));
			bool isHostFlag = false;
			if (result.IsExecSuccessed())
			{
				isHostFlag = result.GetData_Bool();
			}
			if (isHostFlag)
				MINIW::ScriptVM::game()->callFunction(funcname, "iiii", m_Host->getUin(), int(PLAYER_NOTIFYINFO_TIPS), 9725, int(CS_PERMIT_PLACE_BLOCK));
			else
				MINIW::ScriptVM::game()->callFunction(funcname, "iiii", m_Host->getUin(), int(PLAYER_NOTIFYINFO_TIPS), 9720, int(CS_PERMIT_PLACE_BLOCK));
		}
		return true;
	}
	return false;
}

bool ActionIdleState::logicToRiderSpecialSkill()
{
	// 坐骑释放特有技能
	auto RidComp = m_Host->getRiddenComponent();
	ActorHorse* riding = NULL;
	if (RidComp)
	{
		riding = dynamic_cast<ActorHorse*>(RidComp->getRidingActor());
	}
	if (riding && (riding->getRiddenByActorID() == m_Host->getObjId()))
	{
		//变形坐骑使用技能
		if (riding->getObjType() == OBJ_TYPE_SHAPESHIFT_HORSE)
		{
			m_Host->doSpecialSkill();
			return true;
		}
		//技能坐骑使用技能
		if (riding->getObjType() == OBJ_TYPE_DOUDU_MOUNT)
		{
			m_Host->doSpecialSkill();
			return true;
		}
		//南瓜坐骑使用技能
		if (riding->getObjType() == OBJ_TYPE_PUMPKIN_HORSE)
		{
			m_Host->doSpecialSkill();
			return true;
		}
	}
	return false;
}

bool ActionIdleState::logicToTriggerToolMode()
{
	//如果是工具模式
	//如果是工具模式 20211025:对象库添加显示板判断 codeby caiqizhen
	if (m_Host->getWorld() && (GetWorldManagerPtr()->isAreaToolMode() || GetWorldManagerPtr()->isPositionToolMode() || GetWorldManagerPtr()->isDisplayBoardToolMode()))
	{
		if (GetWorldManagerPtr())
		{
			SandboxEventDispatcherManager::GetGlobalInstance().Emit("TriggerObjLibManager_UpdateCurToolMode");
		}

		return true;
	}

	// 右键进行生物选择和放置
	if (GetWorldManagerPtr()->isGameMakerToolMode() && GetWorldManagerPtr()->isActorToolMode())
	{
		SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("CTriggerLivingToolMgr_logicToTriggerToolMode", SandboxContext(nullptr)
			.SetData_Usertype("m_Host", m_Host));
		if (result.IsExecSuccessed())
		{
			return result.GetData_Bool();
		}
		return true;
	}

	return false;
}

int ActionIdleState::doHostToPick()
{
	int toolid = m_Host->getCurToolID();
	int picktype = 0;
	if (toolid == ITEM_WRENCH)
	{
		picktype = m_Host->doPickByType(PICK_METHOD_RAYBLOCK, true);
	}
	else
	{
		ItemDef* itemdef = GetDefManagerProxy()->getItemDef(toolid);
		bool isliquid = itemdef != NULL && itemdef->UseTarget == ITEM_USE_CLICKLIQUID;

		bool pickliquid = toolid > 0 && isliquid;
		picktype = m_Host->doPick(pickliquid, true);
	}

	return picktype;
}

bool ActionIdleState::logicToUseTarget(const ItemDef* def, int useType)
{
	if (def->UseTarget == useType)
	{
		//新加禁食效果,生效后玩家不能进食且不能有动作反馈也不能有声音 code by曹泽港
		if (m_Host->getPlayerAttrib() != NULL && !m_Host->getPlayerAttrib()->IsEnableEatFood())
		{
			return true;
		}
		m_nextState = "ToEat";
		if (useType == ITEM_USE_WATER) m_nextState = "ToDrinkWater";
		return true;
	}

	if (def->UseTarget == ITEM_USE_BOW)
	{
		const ToolDef* tooldef = GetDefManagerProxy()->getToolDef(m_Host->getCurToolID());
		bool isEnchantArrorwFree = m_Host->getLivingAttrib()->getEquipEnchantValue(EQUIP_WEAPON, ENCHANT_ARROW_FREE) > 0;
		if (tooldef != NULL)
		{
			if (!GetWorldManagerPtr()->isGodMode() && !isEnchantArrorwFree && def->Type != 24)
			{
				//看数量是否够
				if (m_Host->getBackPack()->getItemCountInNormalPack(tooldef->ConsumeID == -1 ? tooldef->ID : tooldef->ConsumeID) < tooldef->ConsumeCount)
				{
					//ge GetGameEventQue().postInfoTips(7);
					InfoTips(7);
					return true;
				}
			}
		}
		//第一人称在切换东西的时候不进chargeattack
		if (!(m_Host->getViewMode() == 0 && m_Host->getCamera()->m_IsNeedHideHand) )
		{
			m_nextState = "ToChargeAttack";
			return true;
		}
		return true;
	}

	return false;
}

const char* ActionIdleState::updateOnMobileForLongPress(float dtime)
{
	int itemid = m_Host->getCurToolID();

	if (LogicToState(Logic_BlockCanBreak)) return m_nextState.c_str();

	World* curWorld = m_Host->getWorld();
	if (curWorld)
	{
		int blockid = m_Host->GetExploitCheckBlockId(false);
		if (DoByProgress(m_Host->getCurToolID(), blockid, false))
		{
			//这里不包括铲 耙的开垦状态 code-by:liwentao
			return "ToExploit";
		}
	}

	SandboxResult homelandresult1 = SandboxEventDispatcherManager::GetGlobalInstance().
		Emit("Homeland_logicToHomeLand",
			SandboxContext(nullptr).SetData_Number("type", HomeLand_CheckIsCrops | HomeLand_CheckIsAnimal).
			SetData_UserObject("blockpos", m_Host->m_PickResult.block).
			SetData_Userdata("ClientActor", "clientactor", m_Host->m_PickResult.actor));

	SandboxResult homelandresult2 = SandboxEventDispatcherManager::GetGlobalInstance().
		Emit("Homeland_logicToHomeLand",
			SandboxContext(nullptr).SetData_Number("type", HomeLand_CheckIsFramOpen).
			SetData_UserObject("blockpos", m_Host->m_PickResult.block).
			SetData_Userdata("ClientActor", "clientactor", m_Host->m_PickResult.actor));
	if (homelandresult1.IsSuccessed())
	{
		return "ToRecover";
	}
	else if (homelandresult2.IsSuccessed())
	{
		return "ToFarmOpen";
	}
	else if (skillAllowDigAndAttack())
	{
			//return "ToDig";
	 	return ChangeToDig2AttackBlock();
	}
	return "";
}

const char* ActionIdleState::updateOnMobileForTab(float dtime)
{
    OPTICK_EVENT();
	m_Host->m_CurMouseX = m_Host->m_InputInfo->clickPosX;
	m_Host->m_CurMouseY = m_Host->m_InputInfo->clickPosY;

	int toolid = m_Host->getCurToolID();
	int picktype = this->doHostToPick(); //doPick操作

	//如果是工具模式
	if (LogicToState(Logic_TriggerToolMode)) return m_nextState.c_str();

	//如果是连线钳
	SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("VehicleMgr_makesureVehicleEndLink",
		SandboxContext(nullptr));
	if (result.IsExecSuccessed())
	{
		if (result.GetData_Bool("vehicleEndLink"))
		{
			return "";
		}
	}
	//年兽处理 吴德燊
	SandboxResult rightClickResult = SandboxEventDispatcherManager::GetGlobalInstance().
		Emit("DoPickActorForRightClickDown_Event",
			SandboxContext(nullptr).SetData_UserObject("blockpos", m_Host->m_PickResult.block).
			SetData_Userdata("ClientActor", "clientactor", m_Host->m_PickResult.actor));
	if (rightClickResult.IsExecSuccessed())
	{
		std::string state = "";
		state = rightClickResult.GetData_String("state", state);
		return state.c_str();
	}

	//点击的是动物
	if (picktype == 2)
	{
		if (m_Host->m_PickResult.actor != NULL)
		{
			auto HostRidComp = m_Host->getRiddenComponent();
			if (HostRidComp && HostRidComp->isVehicleController() && m_Host->m_PickResult.actor->getObjType() != OBJ_TYPE_VEHICLE)
			{
				return "";
			}
			m_Host->clickActorOnTrigger(m_Host->m_PickResult.actor->getObjId(), m_Host->m_PickResult.actor->getDefID());

			if (m_Host->getViewMode() == 0)
			{
				if (m_Host->m_PlayerAnimation)  m_Host->m_PlayerAnimation->performDig();
			}


			if (m_Host->m_PickResult.actor != NULL)
			{
				auto vehicle = dynamic_cast<ActorVehicleAssemble*>(m_Host->m_PickResult.actor);
				auto RidComp = static_cast<ClientActor*>(m_Host->m_PickResult.actor)->sureRiddenComponent();
				if (RidComp && RidComp->canBeRided(m_Host) && (!vehicle || !vehicle->canDig())&& 
					3912 != m_Host->m_PickResult.actor->getDefID()) //耗牛特殊处理  后续根据策划和下面的统一
				{
					m_Host->tryMountActor(static_cast<ClientActor*>(m_Host->m_PickResult.actor));
					return "";
				}

				if (m_Host->m_PickResult.actor->getObjType() == OBJ_TYPE_VILLAGER && toolid == 12598)	//使用命名牌
				{
					ActorVillager* villager = dynamic_cast<ActorVillager*>(m_Host->m_PickResult.actor);
					if (villager)
					{
						int uin = m_Host->getUin();
						villager->userNameBrand(uin);
					}
					return "";
				}

				WORLD_ID lastOpenDialogueMobID = m_Host->m_OpenDialogueMobID;
				//连击状态 判定
				if (CheckEnterComboState(m_Host->m_PickType, static_cast<ClientActor*>(m_Host->m_PickResult.actor), 1, true)&& skillAllowDigAndAttack())
					return "ToComboAttack";

				if (lastOpenDialogueMobID == 0 && m_Host->m_OpenDialogueMobID != 0)
				{
					//已经触发了打开对白，所以就不用再触发 m_Host->interactActor() 了
				}
				else
				{
					if (m_Host->interactActor(static_cast<ClientActor*>(m_Host->m_PickResult.actor), 1, true))
					{
					}
					else
					{
						if (m_Host->m_PickResult.actor)
						{
							static_cast<ClientActor*>(m_Host->m_PickResult.actor)->handleTouch(m_Host);
						}
					}
				}
			}
		}
	}
	//点击的是block
	else if (picktype == 1)
	{
		bool bInterrupt = false;
		m_Host->DoMtlClickByActor(bInterrupt);
		if (bInterrupt)
		{
			return "";
		}
		SandboxResult homelandresult = SandboxEventDispatcherManager::GetGlobalInstance().
			Emit("Homeland_logicToHomeLand",
				SandboxContext(nullptr).SetData_Number("type", HomeLand_ClickBlock | HomeLand_ClickChest).
				SetData_UserObject("blockpos", m_Host->m_PickResult.block).
				SetData_Userdata("ClientActor", "clientactor", m_Host->m_PickResult.actor));
		if (homelandresult.IsSuccessed())
		{
			return "";
		}
		auto HostRidComp = m_Host->getRiddenComponent();
		if (HostRidComp && HostRidComp->isVehicleController())
			return "";

		if (m_Host->getWorld())
		{
			// 租赁服房间权限 是否能放置方块
			if (LogicToState(Logic_BlockCanPlace)) return m_nextState.c_str();

			m_Host->DoMtlClickByActorNotify();

			int blockid = m_Host->getWorld()->getBlockID(m_Host->m_PickResult.block);
			m_Host->clickBlockOnTrigger(blockid, m_Host->m_PickResult.block);

			const ItemDef* def = GetDefManagerProxy()->getItemDef(toolid);
			// 创造锤播放音效
			if (ITEM_STONE_HAMMER <= toolid && toolid <= ITEM_TITANIUM_HAMMER && def && def->UseScript2[0]) {
				auto& blockpos = m_Host->m_PickResult.block;
				MINIW::ScriptVM::game()->callFunction(def->UseScript2, "u[ClientPlayer]iiiii", m_Host, toolid, blockid, blockpos.x, blockpos.y, blockpos.z);
			}
		}

		if (m_Host->m_PlayerAnimation)   m_Host->m_PlayerAnimation->performDig();
		bool ret = m_Host->interactBlock(m_Host->m_PickResult.block, m_Host->m_PickResult.face, m_Host->m_PickResult.facepoint);
		if (ret)
		{
			m_UseBlock = 0.0f;
			return "";
		}
	}
	else
	{
		//连击状态 判定
		if (CheckEnterComboState(m_Host->m_PickType) && skillAllowDigAndAttack())
			return "ToComboAttack";
	}

	return "";
}

const char* ActionIdleState::updateOnMobileForActionTrigger(float dtime)
{
	// 道具未解锁或未定义
	if (LogicToState(Logic_CheckItem)) return m_nextState.c_str();

	// 道具使用权限校验
	if (LogicToState(Logic_ItemCanUse)) return m_nextState.c_str();

	// 坐骑释放特有技能
	if (LogicToState(Logic_RiderSpecialSkill)) return m_nextState.c_str();

	int itemid = m_Host->getCurToolID();

	auto RidComp = m_Host->getRiddenComponent();
	ActorHorse* riding = NULL;
	if (RidComp)
	{
		riding = dynamic_cast<ActorHorse*>(RidComp->getRidingActor());
	}

	//马的背包
	ActorHorse* horse = m_Host->getFacedHorse();
	if (horse && GetDefManagerProxy()->getGunDef(itemid) == NULL)
	{
		if (m_Host->getViewMode() == 0)
		{
			m_Host->m_PlayerAnimation->performDig();
		}
		if (3912 == horse->getDefID()) //耗牛特殊处理  后续根据策划和下面的统一
		{
			m_Host->tryMountActor(horse);
			return "";
		}

		m_Host->useItem(itemid, PLAYEROP_STATUS_BEGIN, true);
		return "";
	}
	const ItemDef* def = GetDefManagerProxy()->getItemDef(itemid);

	m_Host->notifyUseItem2Tracking(itemid, PLAYEROP_STATUS_BEGIN);

	//根据usetarget来处理到对应的状态
	if (def)
	{
		int itemid = def->ID;
		const ToolDef* tooldef = GetDefManagerProxy()->getToolDef(itemid);
		if (tooldef && tooldef->Duration > 0 && m_Host->getEquipItemDuration(EQUIP_WEAPON) <= 0)
		{
			GetLuaInterfaceProxy().showGameTips(86036);
			return "";
		}
		//CD
		if (m_Host->isSkillCD() && !(1 == m_Host->getTouchControl()->getShootPosition() && itemid == 12311))//demon 沙虫炮右边使用按钮需要正
		{
			//ge GetGameEventQue().postInfoTips(4881);
			InfoTips(4881);
			return "";
		}
		if (m_Host->getPlayerAttrib())
		{
			if (m_Host->getPlayerAttrib()->strengthFoodShowState() != SFS_Empty)
			{
				if (!m_Host->getPlayerAttrib()->useCompatibleStrength())
				{
					if (m_Host->getPlayerAttrib()->getFoodLevel() < def->CostFoodLevel)
					{
						//GetGameEventQue().postInfoTips(6527);
                        InfoTips(6527);
						return "";
					}
				}
				else
				{
					if (m_Host->getPlayerAttrib()->getStrength() < def->CostFoodLevel)
					{
						//GetGameEventQue().postInfoTips(1571);
                        InfoTips(1571);
						return "";
					}
				}
			}
		}
		//右键使用特效
		{
			if (GetDefManagerProxy()->IsShowUseBtnForItemRightUse(itemid))
			{
				return "ToUse";
			}
		}
		//吃的东西
		if (def->UseTarget == ITEM_USE_PRESSFOOD)
		{
			//新加禁食效果,生效后玩家不能进食且不能有动作反馈也不能有声音 code by曹泽港
			if (m_Host->getPlayerAttrib() != NULL && !m_Host->getPlayerAttrib()->IsEnableEatFood())
			{
				return "";
			}
			return "ToEat";
		}
		//喝的东西
		else if (def->UseTarget == ITEM_USE_WATER)
		{
			//禁食效果也禁止喝水
			if (m_Host->getPlayerAttrib() != NULL && !m_Host->getPlayerAttrib()->IsEnableEatFood())
			{
				return "";
			}
			return "ToDrinkWater";
		}
		//蓄力攻击的武器
		else if (def->UseTarget == ITEM_USE_BOW || def->UseTarget == ITEM_USE_CHARGETHROW)
		{
			// 无需蓄力
			const ToolDef* tooldef = GetDefManagerProxy()->getToolDef(def->ID);
			if (tooldef && tooldef->AccumulatorType == 2)
			{
				return "ToUse";
			}
			m_ActionStartMark = Rainbow::Timer::getSystemTick();
			return "";
		}
		else if (def->UseTarget == ITEM_USE_HOOK && m_Host->getHookObj() == 0)
		{
			if (m_Host->m_PlayerAnimation) m_Host->m_PlayerAnimation->performDig();
			m_Host->useItem(itemid, PLAYEROP_STATUS_BEGIN);
			return "";
		}
		else if (def->UseTarget == ITEM_USE_GUN)
		{
			CustomGunDef* originCustomGunDef = GetDefManagerProxy()->getCustomGunDef(m_Host->getCurToolID());
			//新枪械系统：开火
			if (m_Host->getCustomGunDef())
			{
				if (m_Host->getCustomGunDef()->skillId.empty())
					return HandleFire();
			}
			else if (originCustomGunDef == nullptr) //新枪 数据异常
			{
				if (!(m_Host->getViewMode() == 0 && m_Host->getCamera() && m_Host->getCamera()->m_IsNeedHideHand && !m_Host->getGunLogical()->getZoom()))
				{
					return "ToGunUse";
				}
			}
			return "";
		}
		//-----Need delete end
		else if (def->UseTarget == ITEM_USE_MUSIC_ITEM) {
			//ToMusic
			return "";
		}

		else if (def->UseTarget == ITEM_USE_ADVANCEDDIG)
		{
			const ToolDef* tooldef = GetDefManagerProxy()->getToolDef(itemid);

			if (tooldef)
			{
				if (tooldef->SubType == 6) return "ToRangeDig";
				else if (tooldef->SubType == 5) return "ToChargeDig";
				else if (tooldef->Type == 6) return "ToChargeAttack";
			}
		}
		else if (def->UseTarget == ITEM_USE_PACKING_FCM_ITEM)
		{
			m_Host->usePackingFCMItem(itemid, m_Host->getCurOperatePos());
			return "";
		}
		//有自定义的脚本
		else if (!def->UseScript.empty() && def->UseTarget == ITEM_USE_CLICKBUTTON)
		{
			return "ToUse";
		}
		else if (def->UseTarget == ITEM_USE_SHIELD_ITEM) {
			if (!(m_Host->getViewMode() == 0 && m_Host->getCamera()->m_IsNeedHideHand)) 
			{
				if (m_Host->getPlayerAttrib())
				{
					// 手持盾有韧性才可继续防御
					BackPackGrid* grid = m_Host->getPlayerAttrib()->getEquipGrid(EQUIP_WEAPON);
					if (grid && grid->getToughness() > 0)
					{
						return "ToShieldDefence";
					}
				}

			}
			return "";
		}
		else
		{
			//int blockid = m_Host->GetExploitCheckBlockId(false);
			//if (checkCanExploit(itemid, blockid))
			if (IsHoelID(itemid) || IsShovelID(itemid))
			{
				return "ToExploit";
			}
		}
	}
	if (riding && (riding->getRiddenByActorID() == m_Host->getObjId()))
	{
		//if (riding->getObjType() == OBJ_TYPE_DRAGON_MOUNT)
		//{
		m_Host->doSpecialSkill();
		return "";
		//}
	}


	/*if (riding && (riding->getRiddenByActorID() == m_Host->getObjId()))
	{
		if (riding->getObjType() == OBJ_TYPE_MOON_MOUNT)
		{
			m_Host->doSpecialSkill();
			return "";
		}
	}*/
	return "";
}

const char* ActionIdleState::updateOnMobileForAction(float dtime)
{
	int judgeDuration = 300;
	const ItemDef* def = GetDefManagerProxy()->getItemDef(m_Host->getCurToolID());
	//长按使用按钮一定时间
	if (def && m_ActionStartMark > 0 && Rainbow::Timer::getSystemTick() - m_ActionStartMark > 300 && (def->UseTarget == ITEM_USE_BOW || def->UseTarget == ITEM_USE_CHARGETHROW))
	{
		const ToolDef* def = GetDefManagerProxy()->getToolDef(m_Host->getCurToolID());
		if (m_Host->getLivingAttrib() == NULL) return "";
		bool isEnchantArrorwFree = m_Host->getLivingAttrib()->getEquipEnchantValue(EQUIP_WEAPON, ENCHANT_ARROW_FREE) > 0;
		if (def)
		{
			if (m_Host->getWorld() && !GetWorldManagerPtr()->isGodMode() && !isEnchantArrorwFree && def->Type != 24)
			{
				//看数量是否够
				if (m_Host->getBackPack() && m_Host->getBackPack()->getItemCountInNormalPack(def->ConsumeID == -1 ? def->ID : def->ConsumeID) < def->ConsumeCount)
				{
					//ge GetGameEventQue().postInfoTips(7);
					InfoTips(7);
					return ""; //return "ToActionIdle";
				}
			}
		}

		return "ToChargeAttack";
	}
	return "";
}

const char* ActionIdleState::updateOnMobile(float dtime)
{
	if (m_Host == NULL || m_Host->isInSpectatorMode())
	{
		return "";
	}
	PlayerAttrib* playerAttrib = m_Host->getPlayerAttrib();
	/*if (playerAttrib && playerAttrib->hasBuff(NERVE_PARALYSIS_VIGILANCE_BUFF))
	{
		return "";
	}*/
	if (playerAttrib && playerAttrib->getBuffEffectBankInfo(BuffAttrType::BUFFATTRT_FORBID_OPERATE))
	{
		return "";
	}
	auto CarryComp = m_Host->getCarryComponent();
	if (CarryComp && CarryComp->isCarrying())
	{
		return carringState(dtime);
	}

	if (m_Host && m_Host->m_InputInfo && (m_Host->m_InputInfo->triggerLongPress || m_Host->m_InputInfo->useAction || m_Host->m_InputInfo->useActionTrigger))
	{
		auto* pFishingCom = m_Host->m_pFishingComponent;
		if (pFishingCom)
		{
			pFishingCom->endPlayFishByClient();
		}
	}

	//重力枪逻辑
	//if (m_Host->getCurToolID() == ITEM_GRAVITYGUN)
	//{
	//	return gravityGunState(dtime);
	//}

	//变形的角色不能使用道具
	if (m_Host->isShapeShift())
		return "";

	if (m_Host->isSittingInStarStationCabin())  //在传送舱内除了下舱，不允许其它操作
		return "";

	// 属性变身时，只能使用左右键攻击能力
	if (m_Host->isAttrShapeShift())
	{
		PlayerAttrib* playerAttrib = m_Host->getPlayerAttrib();
		if (playerAttrib && m_Host->m_InputInfo)
		{
			if (m_Host->m_InputInfo->tap)
			{
				m_Host->doPick(false, false, true);
				if (doInteractForLeftClickDown())
					m_Host->doAttrShapeShiftLeftClick();
				return "";
			}
			if (m_Host->m_InputInfo->useAction)
			{
				m_Host->doAttrShapeShiftRightClick();
				return "";
			}
		}
		return "";
	}


	if (m_Host->isRidingByHippocompus())//海马坐骑状态下只能移动
	{
		return "";
	}
	if (m_Host->isCrabClamp())
	{

		if (m_Host->m_InputInfo && m_Host->m_InputInfo->tap)
		{
			m_Host->m_PlayerAnimation->performDig();
			m_Host->addCrabClick();
		}
		return "";
	}
	else
	{
		m_Host->m_crabClickCount = 0;//非螃蟹钳击时重置单击次数
	}
	//优先判断道具技能
	bool handle = false;
	const char* trans = itemSkillUse(handle);
	if (trans != NULL && strlen(trans) > 0)
		return trans;
	if (handle)
		return "";

	// 枪支使用逻辑
	if (LogicToState(Logic_UseGun)) return m_nextState.c_str();

	// 音乐道具逻辑
	if (LogicToState(Logic_UseMusicItem)) return m_nextState.c_str();
	//如果没有输入信息
	if (!m_Host->m_InputInfo) return "";

	//长按屏幕，触发挖掘
	if (m_Host->m_InputInfo->triggerLongPress)
	{
		return updateOnMobileForLongPress(dtime);
	}
	//tap屏幕，看射线求交之后的结果处理角色的行为
	else if (IsInputTap()/*m_Host->m_InputInfo->tap*/)
	{
		return updateOnMobileForTab(dtime);
	}
	//按下了使用键
	else if (IsUseActionTrigger()/*m_Host->m_InputInfo->useActionTrigger*/)
	{
		return updateOnMobileForActionTrigger(dtime);
	}
	else if (IsUseAction()/*m_Host->m_InputInfo->useAction*/)
	{
		return updateOnMobileForAction(dtime);
	}

	auto comp = m_Host->getCustomGunComponent();
	if (comp && comp->IsAutoFire())
	{
		return HandleFire();
	}

	return "";
}

const char* ActionIdleState::doPickActorForRightClickDown(bool& bBreak)
{
	int itemid = m_Host->getCurToolID();
	const ItemDef* def = GetDefManagerProxy()->getItemDef(itemid);

	m_Host->clickActorOnTrigger(m_Host->m_PickResult.actor->getObjId(), m_Host->m_PickResult.actor->getDefID());

	if (m_Host->getSneaking())
	{
		//防止载具上的司机使用道具
		auto HostRidComp = m_Host->getRiddenComponent();
		if (HostRidComp && HostRidComp->isVehicleController())
		{
			return "";
		}
		ActorHorse* horse = dynamic_cast<ActorHorse*>(m_Host->m_PickResult.actor);
		if (horse)
		{
			if (m_Host->getViewMode() == 0)
			{
				m_Host->m_PlayerAnimation->performDig();
			}
			if (3912 == horse->getDefID()) //耗牛特殊处理  后续根据策划和下面的统一
			{
				m_Host->tryMountActor(horse);
				return "";
			}
			//解决客机不能打开背包bug
			m_Host->useItem(itemid, PLAYEROP_STATUS_BEGIN, true);
			//m_Host->interactHorse(horse);
			return "";
		}
	}
	else
	{
		/*if (m_Host->checkDeveloperHandleForActor(false,m_Host->m_PickResult.actor))
		{
			return "";
		}*/
		int actionStatus = 0;
		//SandboxCoreDriver::GetInstance().GetLua().CallFunctionM("RoleSkin_Helper", "IsSocialActionOpen", ">i", &actionStatus);
		////pc端右键点击人物模型发起互动动作
		auto pickactor = static_cast<ClientActor*>(m_Host->m_PickResult.actor);
		//if (actionStatus == 1 && pickactor->getObjType() == OBJ_TYPE_ROLE)
		//{
		//	MINIW::ScriptVM::game()->callFunction("Playmain_OnShowInviteActionBtn", "i", pickactor->getObjId());
		//}

		LivingAttrib* pLivingAttrib = dynamic_cast<LivingAttrib*>(pickactor->getAttrib());
		if (pLivingAttrib && pLivingAttrib->getBuffEffectBankInfo(BuffAttrType::BUFFATTRT_FORBID_OPERATE))
		{
			return "";
		}

		auto HostRidComp = m_Host->getRiddenComponent();
		if (HostRidComp && HostRidComp->isVehicleController() && pickactor->getObjType() != OBJ_TYPE_VEHICLE)
		{
			return "";
		}
		
		//添加检测操作对象是否可以自定义的载具 @tangjie
		auto vehicle = dynamic_cast<ActorVehicleAssemble*>(pickactor);
		auto RidComp = pickactor->sureRiddenComponent();
		bool isCar = false;
		if (itemid == ITEM_WRENCH)//renjie 扳手右键过山车则掉落
		{
			ActorTrainCar* car = dynamic_cast<ActorTrainCar*>(m_Host->m_PickResult.actor);
			if (car)
			{
				isCar = true;
			}
		}
		if (RidComp && RidComp->canBeRided(m_Host) && (!vehicle || !vehicle->canDig()) && isCar==false)  //需要对载具上的座位特殊处理
		{
			m_Host->tryMountActor(static_cast<ClientActor*>(m_Host->m_PickResult.actor));
			return "";
		}
		else if (def == NULL || (def->UseTarget != ITEM_USE_ADVANCEDDIG && def->UseTarget != ITEM_USE_HOOK) || m_Host->m_PickResult.actor->getObjType() == OBJ_TYPE_VILLAGER)
		{
			if (itemid == 12598)	//使用命名牌
			{
				ActorVillager* villager = dynamic_cast<ActorVillager*>(m_Host->m_PickResult.actor);
				if (villager)
				{
					int uin = m_Host->getUin();
					villager->userNameBrand(uin);
				}
				return "";
			}
			//家园处理
			SandboxResult homelandresult1 = SandboxEventDispatcherManager::GetGlobalInstance().
				Emit("Homeland_logicToHomeLand",
					SandboxContext(nullptr).SetData_Number("type", HomeLand_CheckIsAnimal).
					SetData_UserObject("blockpos", m_Host->m_PickResult.block).
					SetData_Userdata("ClientActor", "clientactor", m_Host->m_PickResult.actor));
			if (homelandresult1.IsSuccessed())
			{
				return "ToRecover";
			}

			//年兽处理 吴德燊
			SandboxResult rightClickResult = SandboxEventDispatcherManager::GetGlobalInstance().
				Emit("DoPickActorForRightClickDown_Event",
					SandboxContext(nullptr).SetData_UserObject("blockpos", m_Host->m_PickResult.block).
					SetData_Userdata("ClientActor", "clientactor", m_Host->m_PickResult.actor));
			if (rightClickResult.IsExecSuccessed())
			{
				return "";
			}

			if (m_Host->interactActor(pickactor, 2, true))
			{
				SandboxResult homelandresult3 = SandboxEventDispatcherManager::GetGlobalInstance().
					Emit("Homeland_notifyHomePrayEvent",
						SandboxContext(nullptr).SetData_Userdata("ClientActor", "clientactor", m_Host->m_PickResult.actor));
				if (homelandresult3.IsSuccessed())
				{
					return "";
				}
				else
				{
					if (WEAPON_SNIPPER_ID == m_Host->getCurToolID())
					{
						//客机 使用狙击枪 设置bBreak为false 走后面的开镜逻辑
						bBreak = false;
					}
					else if (m_Host->getViewMode() == 0)
					{
						m_Host->m_PlayerAnimation->performDig();
					}
					else
					{
						m_Host->getBody()->playAttack();
					}
					return "";
				}
			}
			else
			{
				if (pickactor)
				{
					pickactor->handleTouch(m_Host);
				}
			}
			/*else if (m_Host->m_PickResult.actor->canCarried(m_Host))
			{
				m_Host->carryActor(m_Host->m_PickResult.actor);
				return "";
			}*/
		}

	}
	bBreak = false;
	return "";
}

const char* ActionIdleState::doPickBlockForLeftClickDown(bool& bBreak)
{
	auto pWorld = m_Host->getWorld();
	if (!pWorld)
	{
		return "";
	}
	bool bInterrupt = false;
	m_Host->DoMtlClickByActor(bInterrupt);
	if (bInterrupt)
	{
		return "";
	}
	const WCoord& blockpos = m_Host->m_PickResult.block;
	int blockid = pWorld->getBlockID(m_Host->m_PickResult.block);
	LOG_INFO("点击方块为:%d", blockid);

	int itemid = m_Host->getCurToolID();
	const ItemDef* def = GetDefManagerProxy()->getItemDef(itemid);
	if (m_Host->getWorld())
	{
		SandboxResult homelandresult = SandboxEventDispatcherManager::GetGlobalInstance().
			Emit("Homeland_logicToHomeLand",
				SandboxContext(nullptr).SetData_Number("type", HomeLand_LeftClickBlock).
				SetData_UserObject("blockpos", m_Host->m_PickResult.block).
				SetData_Userdata("ClientActor", "clientactor", m_Host->m_PickResult.actor));
		if (homelandresult.IsSuccessed())
		{
			return "";
		}

		// 租赁服房间权限 是否能破坏方块
		if (LogicToState(Logic_BlockCanBreak)) return m_nextState.c_str();

		m_Host->DoMtlClickByActorNotify();
		
		m_Host->clickBlockOnTrigger(blockid, m_Host->m_PickResult.block);

		// 创造锤播放音效
		if (ITEM_STONE_HAMMER <= itemid && itemid <= ITEM_TITANIUM_HAMMER && def && def->UseScript2[0]) {
			
			MINIW::ScriptVM::game()->callFunction(def->UseScript2, "u[ClientPlayer]iiiii", m_Host, itemid, blockid, blockpos.x, blockpos.y, blockpos.z);
		}
	}

	SandboxResult homelandresult = SandboxEventDispatcherManager::GetGlobalInstance().
		Emit("Homeland_logicToHomeLand",
			SandboxContext(nullptr).SetData_Number("type", HomeLand_CheckIsCrops).
			SetData_UserObject("blockpos", m_Host->m_PickResult.block).
			SetData_Userdata("ClientActor", "clientactor", m_Host->m_PickResult.actor));
	if (homelandresult.IsSuccessed())
	{
		return "ToRecover";
	}
	else if (skillAllowDigAndAttack())
	{
		
		auto targetBlockDef =GetDefManagerProxy()->getItemDef(blockid);
		bool b = GetWorldManagerPtr() && GetWorldManagerPtr()->isGodMode();
		//运行模型下才跑
		if (targetBlockDef&&targetBlockDef->Type == ITEM_TYPE_BUILDING_BLOCK && !b)
		{
			return "ToAttackBlock";
		}
		else
		{
			//return "ToDig";
			return ChangeToDig2AttackBlock();
		}
	}
	return "";
}

const char* ActionIdleState::doPickBlockForRightClickDown(bool& bBreak)
{
	bool bInterrupt = false;
	m_Host->DoMtlClickByActor(bInterrupt);
	if (bInterrupt)
	{
		return "";
	}
	SandboxResult homelandresult1 = SandboxEventDispatcherManager::GetGlobalInstance().
		Emit("Homeland_logicToHomeLand",
			SandboxContext(nullptr).SetData_Number("type", HomeLand_CheckIsCrops).
			SetData_UserObject("blockpos", m_Host->m_PickResult.block).
			SetData_Userdata("ClientActor", "clientactor", m_Host->m_PickResult.actor));
	if (homelandresult1.IsSuccessed())
	{
		return "ToRecover";
	}

	SandboxResult homelandresult2 = SandboxEventDispatcherManager::GetGlobalInstance().
		Emit("Homeland_logicToHomeLand",
			SandboxContext(nullptr).SetData_Number("type", HomeLand_CheckIsFramOpen).
			SetData_UserObject("blockpos", m_Host->m_PickResult.block).
			SetData_Userdata("ClientActor", "clientactor", m_Host->m_PickResult.actor));
	if (homelandresult2.IsSuccessed())
	{
		return "ToFarmOpen";
	}

	SandboxResult homelandresult3 = SandboxEventDispatcherManager::GetGlobalInstance().
		Emit("Homeland_logicToHomeLand",
			SandboxContext(nullptr).SetData_Number("type", HomeLand_ClickBlock | HomeLand_ClickChest).
			SetData_UserObject("blockpos", m_Host->m_PickResult.block).
			SetData_Userdata("ClientActor", "clientactor", m_Host->m_PickResult.actor));
	if (homelandresult3.IsSuccessed())
	{
		return "";
	}

	// 方块放置权限校验
	if (LogicToState(Logic_BlockCanPlace)) return m_nextState.c_str();

	m_Host->DoMtlClickByActorNotify();

	if (m_Host->getWorld())
	{
		int blockid = m_Host->getWorld()->getBlockID(m_Host->m_PickResult.block);
		m_Host->clickBlockOnTrigger(blockid, m_Host->m_PickResult.block);
	}

	bool ret = m_Host->interactBlock(m_Host->m_PickResult.block, m_Host->m_PickResult.face, m_Host->m_PickResult.facepoint);
	if (ret)
	{
		if (m_Host->getViewMode() == 0)
		{
			m_Host->m_PlayerAnimation->performDig();
		}

		m_UseBlock = 0.0f;
		return "";
	}
	bBreak = false;
	return "";
}

const char* ActionIdleState::doExploitForRightClickDown(bool& bBreak)
{
	int itemid = m_Host->getCurToolID();
	World* curWorld = m_Host->getWorld();
	if (curWorld)
	{
		int blockid = m_Host->GetExploitCheckBlockId(false);
		if (DoByProgress(itemid, blockid))
		{
			//增加一种开垦的状态  code-by:liwentao
			return "ToExploit";
		}
		else if (IsHoelID(itemid) || IsShovelID(itemid))
		{
			if (m_Host->getViewMode() == 0)
			{
				m_Host->m_PlayerAnimation->performDig();
			}

			m_UseBlock = 0.0f;

			return "ToExploit";
		}
	}
	bBreak = false;
	return "";
}

const char* ActionIdleState::doUseForRightClickDown(const ItemDef* def, bool& bBreak)
{
	if (def)
	{
		int itemid = def->ID;
		const ToolDef* tooldef = GetDefManagerProxy()->getToolDef(m_Host->getCurToolID());
		if (tooldef && tooldef->Duration > 0 && m_Host->getEquipItemDuration(EQUIP_WEAPON) <= 0)
		{
			GetLuaInterfaceProxy().showGameTips(86036);
			return "";
		}
		//CD
		if (m_Host->isSkillCD())
		{
			if (def->ID == ITEM_PAINTTANK)
				//ge GetGameEventQue().postInfoTips(30622);
				InfoTips(30622);
			else
				//ge GetGameEventQue().postInfoTips(4881);
				InfoTips(4881);
			return "";
		}

		if (m_Host->getPlayerAttrib())
		{
			if (m_Host->getPlayerAttrib()->strengthFoodShowState() != SFS_Empty)
			{
				if (!m_Host->getPlayerAttrib()->useCompatibleStrength())
				{
					if (m_Host->getPlayerAttrib()->getFoodLevel() < def->CostFoodLevel)
					{
						//GetGameEventQue().postInfoTips(6527);
                         InfoTips(6527);
						return "";
					}
				}
				else
				{
					if (m_Host->getPlayerAttrib()->getStrength() < def->CostFoodLevel)
					{
						//GetGameEventQue().postInfoTips(1571);
                         InfoTips(1571);
						return "";
					}
				}
			}
		}

		//右键使用特效
		{
			if (GetDefManagerProxy()->IsShowUseBtnForItemRightUse(itemid))
			{
				return "ToUse";
			}
		}

		//吃的东西
		if (def->UseTarget == ITEM_USE_PRESSFOOD)
		{
			return "";
		}
		//喝的东西
		//else if (def->UseTarget == ITEM_USE_WATER)
		//{
		//	return "";
		//}
		//蓄力攻击的武器
		else if (def->UseTarget == ITEM_USE_BOW)
		{
			const ToolDef* def = GetDefManagerProxy()->getToolDef(m_Host->getCurToolID());
			bool isEnchantArrorwFree = m_Host->getLivingAttrib()->getEquipEnchantValue(EQUIP_WEAPON, ENCHANT_ARROW_FREE) > 0;
			if (def)
			{
				if (!GetWorldManagerPtr()->isGodMode() && !isEnchantArrorwFree && def->Type != 24)// 24代表法杖类型不需要消耗
				{
					//看数量是否够
					if (m_Host->getBackPack()->getItemCountInNormalPack(def->ConsumeID == -1 ? def->ID : def->ConsumeID) < def->ConsumeCount)
					{
						//ge GetGameEventQue().postInfoTips(7);
						InfoTips(7);
						return ""; //return "ToActionIdle";
					}
				}
			}
			//第一人称在切换东西的时候不进chargeattack
			if (!(m_Host->getViewMode() == 0 && m_Host->getCamera()->m_IsNeedHideHand))
			{
				return "ToChargeAttack";
			}
			return "";
		}
		else  if (def->UseTarget == ITEM_USE_CHARGETHROW)
		{
			// 无需蓄力
			const ToolDef* tooldef = GetDefManagerProxy()->getToolDef(def->ID);
			if (tooldef && tooldef->AccumulatorType == 2)
			{
				return "ToUse";
			}
			else
			{
				//第一人称在切换东西的时候不进chargeattack
				if (!(m_Host->getViewMode() == 0 && m_Host->getCamera()->m_IsNeedHideHand))
				{
					return "ToChargeAttack";
				}
			}
		}
		else if (def->UseTarget == ITEM_USE_HOOK && m_Host->getHookObj() == 0)
		{
			m_Host->m_PlayerAnimation->performDig();
			m_Host->useItem(itemid, PLAYEROP_STATUS_BEGIN);
			return "";
		}
		else if (def->UseTarget == ITEM_USE_GUN)
		{
			//新枪械系统：瞄准，不能有技能
			if (m_Host->getCustomGunDef())
			{
				if (m_Host->getCustomGunDef()->skillId.empty())
				{
					if (CustomGunSetting::AdsRelease)
						ADS(true);
					else
						HandleAim();
				}
				else
					bBreak = false; //还要进行道具的技能检测
			}
			else
			{
				if (m_Host->getViewMode() == CAMERA_TPS_BACK_2)
				{
					m_Host->lockEyeDirWithView();
				}
				m_Host->getGunLogical()->setZoom(!m_Host->getGunLogical()->getZoom());
			}
			return "";
		}
		else if (def->UseTarget == ITEM_USE_MUSIC_ITEM)
		{
			return "";
		}
		else if (def->UseTarget == ITEM_USE_ADVANCEDDIG)
		{
			const ToolDef* tooldef = GetDefManagerProxy()->getToolDef(itemid);
			if (tooldef)
			{
				if (tooldef->SubType == 6) return "ToRangeDig";
				else if (tooldef->SubType == 5) return "ToChargeDig";
				else if (tooldef->Type == 6) return "ToChargeAttack";
			}
		}
		else if (def->UseTarget == ITEM_USE_PACKING_FCM_ITEM)
		{
			m_Host->usePackingFCMItem(itemid, m_Host->getCurOperatePos());
			return "";
		}
		//有自定义的脚本
		else if (!def->UseScript.empty() && (def->UseTarget == ITEM_USE_CLICKBUTTON))
		{
			if (m_Host->getViewMode() == CAMERA_TPS_BACK_2)
			{
				m_Host->lockEyeDirWithView();
			}
			return "ToUse";
		}
		else if (def->UseTarget == ITEM_USE_SHIELD_ITEM) {
			if (!(m_Host->getViewMode() == 0 && m_Host->getCamera()->m_IsNeedHideHand)) 
			{
				if (m_Host->getPlayerAttrib())
				{
					// 手持盾有韧性才可继续防御
					BackPackGrid* grid = m_Host->getPlayerAttrib()->getEquipGrid(EQUIP_WEAPON);
					if (grid && grid->getToughness() > 0)
					{
						return "ToShieldDefence";
					}
				}
			}
			return "";
		}
	}
	bBreak = false;
	return "";
}

const char* ActionIdleState::updateOnPCForRightClickDown(float dtime, bool& bBreak)
{
	int itemid = m_Host->getCurToolID();
	const ItemDef* def = GetDefManagerProxy()->getItemDef(itemid);
	SetSwitchTick(5);//m_Host->m_SwitchTick = 5;

	m_ActionStartMark = Rainbow::Timer::getSystemTick();

	// 道具未解锁或未定义
	if (LogicToState(Logic_CheckItem)) return m_nextState.c_str();

	// 道具使用权限校验
	if (LogicToState(Logic_ItemCanUse)) return m_nextState.c_str();

	// 坐骑释放特有技能
	if (LogicToState(Logic_RiderSpecialSkill)) return m_nextState.c_str();

	// 工具模式相关操作
	if (LogicToState(Logic_TriggerToolMode)) return m_nextState.c_str();

	auto RidComp = m_Host->getRiddenComponent();
	ActorHorse* riding = NULL;
	if (RidComp)
	{
		riding = dynamic_cast<ActorHorse*>(RidComp->getRidingActor());
	}
	auto skillComp = m_Host->getSkillComponent();
	//射线检测
	m_Host->m_CurMouseX = m_Host->m_InputInfo->clickPosX;
	m_Host->m_CurMouseY = m_Host->m_InputInfo->clickPosY;

	//如果是连线钳
	SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("VehicleMgr_makesureVehicleEndLink",
		SandboxContext(nullptr));
	if (result.IsExecSuccessed())
	{
		if (result.GetData_Bool("vehicleEndLink"))
		{
			return "";
		}
	}
	bool needReturn = true; //标记是否需要中断返回
	const char* strResult = NULL; //记录返回的结果

//	if (skillAllowDigAndAttack())
	{
		int picktype = 0;
		if (itemid == ITEM_WRENCH)
		{
			picktype = m_Host->doPickByType(PICK_METHOD_RAYBLOCK, true);
		}
		else
		{
			bool pickliquid = itemid > 0 && GetDefManagerProxy()->getItemDef(itemid, true)->UseTarget == ITEM_USE_CLICKLIQUID;
			picktype = m_Host->doPick(pickliquid, true, true, true);
		}

		//点击的是动物 拆给e键
		//if (picktype == 2)
		//{
		//	strResult = doPickActorForRightClickDown(needReturn);
		//	if (needReturn)
		//	{
		//		//if (skillComp) skillComp->addDiscountKey(Rainbow::InputKeyCode::SDLK_RBUTTON);
		//		return strResult;
		//	}
		//}

		//防止载具上的司机使用道具
		if (RidComp && RidComp->isVehicleController())
		{
			return "";
		}

		//点击的是块
		if (picktype == 1 && WEAPON_SNIPPER_ID != itemid)//手持狙击枪时不与方块交互
		{
			//尝试安装锁
			if (!m_Host->getPlayerInputHelper()->getEKeyDetector()->isShortPress() && //确认是右键触发的
				m_Host->GetPlayer() &&
				m_Host->GetPlayer()->GetComponent<LockCtrlComponent>() &&
				m_Host->GetPlayer()->GetComponent<LockCtrlComponent>()->TryAddLock(m_Host->m_PickResult.block, itemid)
				)
			{
				return "";
			}

			needReturn = true;
			strResult = doPickBlockForRightClickDown(needReturn);
			if (needReturn)
			{
				if (skillComp) skillComp->addDiscountKey(Rainbow::InputKeyCode::SDLK_RBUTTON);
				return strResult;
			}
		}

		//开垦
		needReturn = true;
		strResult = doExploitForRightClickDown(needReturn);
		if (needReturn)
		{
			if (skillComp) skillComp->addDiscountKey(Rainbow::InputKeyCode::SDLK_RBUTTON);
			return strResult;
		}

		//使用？
		needReturn = true;
		strResult = doUseForRightClickDown(def, needReturn);
		if (needReturn)
		{
			if (skillComp) skillComp->addDiscountKey(Rainbow::InputKeyCode::SDLK_RBUTTON);
			return strResult;
		}

		if (riding && (riding->getRiddenByActorID() == m_Host->getObjId()))
		{
			//if (riding->getObjType() == OBJ_TYPE_DRAGON_MOUNT)
			//{
			m_Host->doSpecialSkill();
			return "";
			//}
		}

#if defined(BUILD_MINI_EDITOR_APP)
		//空手右键空气在编辑模式下直接创建方块
		if (m_Host->interactUnderEditorMode())
		{
			bBreak = false;
			return "";
		}
#endif


		/*if (riding && (riding->getRiddenByActorID() == m_Host->getObjId()))
		{
			if (riding->getObjType() == OBJ_TYPE_MOON_MOUNT)
			{
				m_Host->doSpecialSkill();
				return "";
			}
		}*/
	}
	bBreak = false;
	return "";
}

const char* ActionIdleState::updateOnPCForLongPress(float dtime, bool& bBreak)
{
	int picktype = m_Host->doPick(false, false, false);
	if (picktype == 1) //是方块
	{	
		if (m_Host->GetPlayer()->GetComponent<LockCtrlComponent>()->IsOpenPieMenu(m_Host->m_PickResult.block))
		{
			return "ToPieMenuCtrl";
		}
		else
		{
			auto pmtl = m_Host->getWorld()->getBlockMaterial(m_Host->m_PickResult.block);
			if (pmtl->BlockTypeId() == BlockMaterial::BlockType::BlockType_WaterStorage)
			{
				return "ToPieMenuCtrl";
			}
		}
	}

	return "";
}

const char* ActionIdleState::updateOnPCForRightClickUp(float dtime, bool& bBreak)
{
	bBreak = false;
	if(m_Host->m_PickResult.actor)
	{
		m_Host->m_PickResult.actor->GetActor()->rightClickUpInteract(m_Host);
	}
	return "";
}

const char* ActionIdleState::updateOnPCForRightClick(float dtime, bool& bBreak)
{
	int itemid = m_Host->getCurToolID();
	const ItemDef* def = GetDefManagerProxy()->getItemDef(itemid);
	//从useAction 直接切过来
	if (m_ActionStartMark < 0)
	{
		m_ActionStartMark = Rainbow::Timer::getSystemTick();
	}
	//道具未解锁或未定义
	if (LogicToState(Logic_CheckItem)) return m_nextState.c_str();

	SandboxResult resultCanUseItem = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_canUseItem",
		SandboxContext(nullptr).SetData_Number("uin", m_Host->getUin()).SetData_Number("itemid", itemid));
	bool canUseItemFlag = false;
	if (resultCanUseItem.IsExecSuccessed())
	{
		canUseItemFlag = resultCanUseItem.GetData_Bool();
	}
	if (!canUseItemFlag)
	{
		m_Host->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 451);
		return "";
	}

	SandboxResult resultCanPermitFlag = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_canPermit",
		SandboxContext(nullptr)
		.SetData_Number("uin", m_Host->getUin())
		.SetData_Number("itemid", itemid)
		.SetData_Number("bit", CS_PERMIT_PLACE_BLOCK));
	bool canPermitFlag = false;
	if (resultCanPermitFlag.IsExecSuccessed())
	{
		canPermitFlag = resultCanPermitFlag.GetData_Bool();
	}
	if (!canPermitFlag)
	{
		SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_isHost",
			SandboxContext(nullptr).SetData_Number("uin", m_Host->getUin()));
		bool isHostFlag = false;
		if (result.IsExecSuccessed())
		{
			isHostFlag = result.GetData_Bool();
		}
		if (isHostFlag)
			MINIW::ScriptVM::game()->callFunction("notifyAuthorityGameInfo2Player", "iiii", m_Host->getUin(), int(PLAYER_NOTIFYINFO_TIPS), 9725, int(CS_PERMIT_PLACE_BLOCK));
		else
			MINIW::ScriptVM::game()->callFunction("notifyAuthorityGameInfo2Player", "iiii", m_Host->getUin(), int(PLAYER_NOTIFYINFO_TIPS), 9720, int(CS_PERMIT_PLACE_BLOCK));
		return "";
	}

	//防止载具上的司机挖机
	auto HostRidComp = m_Host->getRiddenComponent();
	if (HostRidComp && HostRidComp->isVehicleController())
	{
		return "";
	}

	if (m_Host->isShapeShift())
		return "";

	m_Host->notifyUseItem2Tracking(itemid, PLAYEROP_STATUS_BEGIN);

	if (m_ActionStartMark > 0 && Rainbow::Timer::getSystemTick() - m_ActionStartMark > 200)
	{
		//如果是方块,那就放置
		m_Host->m_CurMouseX = m_Host->m_InputInfo->clickPosX;
		m_Host->m_CurMouseY = m_Host->m_InputInfo->clickPosY;

		bool pickliquid = itemid > 0 && GetDefManagerProxy()->getItemDef(itemid, true)->UseTarget == ITEM_USE_CLICKLIQUID;
		int picktype = m_Host->doPick(pickliquid);
		if (picktype == 1)
		{
			if (m_BuildCoolDown < 0)
			{
				if (m_Host->interactBlock(m_Host->m_PickResult.block, m_Host->m_PickResult.face, m_Host->m_PickResult.facepoint))
				{
					if (m_Host->getViewMode() == 0)
					{
						m_Host->m_PlayerAnimation->performDig();
					}
					m_BuildCoolDown = 0.2f;
					return "";
				}
			}
			else
			{
				return "";
			}
		}

		//根据usetarget来处理到对应的状态
		if (def)
		{
			//右键使用特效
			{
				if (GetDefManagerProxy()->IsShowUseBtnForItemRightUse(itemid))
				{
					return "ToUse";
				}
			}

			//CD道具
			if (m_Host->isSkillCD())
				return "";

			//吃的东西
			if (logicToUseTarget(def, ITEM_USE_PRESSFOOD))
			{
				return m_nextState.c_str();
			}
			//喝的东西
			else if (logicToUseTarget(def, ITEM_USE_WATER))
			{
				return m_nextState.c_str();
			}
			//蓄力攻击的武器
			else if (logicToUseTarget(def, ITEM_USE_BOW))
			{
				return m_nextState.c_str();
			}
			else  if (def->UseTarget == ITEM_USE_CHARGETHROW)
			{
				//第一人称在切换东西的时候不进chargeattack
				if (!(m_Host->getViewMode() == 0 && m_Host->getCamera()->m_IsNeedHideHand))
				{
					return "ToChargeAttack";
				}
			}

			//有自定义的脚本
			else if (!def->UseScript.empty() && def->UseTarget == ITEM_USE_CLICKBUTTON)
			{
				return "ToUse";
			}
			else if (def->UseTarget == ITEM_USE_SHIELD_ITEM) {
				if (!(m_Host->getViewMode() == 0 && m_Host->getCamera()->m_IsNeedHideHand)) 
				{
					if (m_Host->getPlayerAttrib())
					{
						// 手持盾有韧性才可继续防御
						BackPackGrid* grid = m_Host->getPlayerAttrib()->getEquipGrid(EQUIP_WEAPON);
						if (grid && grid->getToughness() > 0)
						{
							return "ToShieldDefence";
						}
					}
				}
				return "";
			}
		}

#if defined(BUILD_MINI_EDITOR_APP)
		//在编辑模式下空手创建方块
		if (m_Host->interactUnderEditorMode())
		{
			bBreak = false;
			return "";
		}
#endif
	}
	bBreak = false;
	return "";
}


bool ActionIdleState::doInteractForLeftClickDown()
{
	bool canInteract = false;
	//xyt 测试代码
	if (m_Host->m_PickResult.transobj)
	{
		//xyt 如果攻击的是新actor 
		auto pActorObject = dynamic_cast<MNSandbox::SceneActorObject*>(m_Host->m_PickResult.transobj);
		if (nullptr != pActorObject)
		{
			auto pComponent = pActorObject->GetComponent<AttackingTargetComponentEx>();
			struct OneAttackData stkData;
			pComponent->attackedFrom(stkData, m_Host);
		}
	}
	bool playHandAnim = false; 
	if (m_Host->m_PickResult.actor)
	{
		m_Host->clickActorOnTrigger(m_Host->m_PickResult.actor->getObjId(), m_Host->m_PickResult.actor->getDefID());

		auto pickactor = static_cast<ClientActor*>(m_Host->m_PickResult.actor);
		if (GetWorldManagerPtr() && (GetWorldManagerPtr()->isCreativeMode() || GetWorldManagerPtr()->isGodMode()))
		{
			canInteract = m_Host->interactActor(pickactor, 0);
		}
		else
		{
			if (m_Host->getViewMode() == 0)
			{
				if (m_Host->m_PickResult.collide_t < 300)
				{
					canInteract = m_Host->interactActor(pickactor, 0);
				}
				else
					playHandAnim = true;  
			}
			else
			{
				float boundRadius = (pickactor->getLocoMotion()->m_HitBoundWidth / 2);
				if (pickactor->getObjType() == OBJ_TYPE_VEHICLE)
				{
					if (m_Host->getPosition().distanceTo(m_Host->m_PickResult.collide_pos) < 300)
						canInteract = m_Host->interactActor(pickactor, 0);
					else
						playHandAnim = true;
				}
				else if ( (m_Host->getPosition().distanceTo(pickactor->getPosition()) - boundRadius) < 300)
				{
					canInteract = m_Host->interactActor(pickactor, 0);
				}
				else
					playHandAnim = true;
			}

		}
	}
	if (canInteract)
	{
		m_Host->m_PlayerAnimation->performDig();
	}
	else
	{
		if (playHandAnim)
		{
			m_Host->m_PlayerAnimation->performDig(); //策划要求在攻击距离不到的情况下，点击左键就执行空挥 code by:keguanqiang
		}

		if (m_Host->m_PickResult.actor)
		{
			static_cast<ClientActor*>(m_Host->m_PickResult.actor)->handleTouch(m_Host);
		}
	}
	SetSwitchTick(5);//m_Host->m_SwitchTick = 5;
	return canInteract;
}
const char* ActionIdleState::updateOnPCForLeftClickDown(float dtime, bool& bBreak)
{
	int itemid = m_Host->getCurToolID();
	const ItemDef* def = GetDefManagerProxy()->getItemDef(itemid);

	//防止载具上的司机使用道具
	auto HostRidComp = m_Host->getRiddenComponent();
	if (HostRidComp && HostRidComp->isVehicleController())
	{
		return "";
	}

	//变形的角色不能使用道具
	if (m_Host->isShapeShift())
		return "";

	if (def && def->UseTarget == ITEM_USE_GUN)
	{
		CustomGunDef* originCustomGunDef = GetDefManagerProxy()->getCustomGunDef(m_Host->getCurToolID());
		//新枪械系统：开火
		if (m_Host->getCustomGunDef())
		{
			if (m_Host->getCustomGunDef()->skillId.empty())
				return HandleFire();
			else
				bBreak = false;
		}
		else if (originCustomGunDef == nullptr) //新枪 数据异常
		{
			if (!(m_Host->getViewMode() == 0 && m_Host->getCamera()->m_IsNeedHideHand && !m_Host->getGunLogical()->getZoom()))
			{
				return "ToGunUse";
			}
		}
		return "";
	}
	else if (def && def->UseTarget == ITEM_USE_BUILDBLOCKREPAIR)
	{
		//m_nextState = "ToEat";
		//return "ToBuildBlockRepair";
		return "ToAttackBlock";
	}

	if (def && def->UseTarget == ITEM_USE_MUSIC_ITEM)
	{
		/*if (!(m_Host->getViewMode() == 0 && m_Host->getCamera()->m_IsNeedHideHand && !m_Host->getGunLogical()->getZoom()))
		{
			return "ToGunUse";
		}*/
		return "";
	}
	//if (def && def->UseTarget == ITEM_USE_BUILDBLOCKREPAIR)
	//{
	//	if (m_Host->m_PickResult.intersect_block)
	//	{
	//		auto pWorld = m_Host->getWorld();
	//		auto pmtl = pWorld->getBlockMaterial(m_Host->m_PickResult.block);
	//		if (pmtl && pmtl->BlockTypeId() == BlockMaterial::BlockType::BlockType_Architecture)
	//		{
	//			m_Host->getBody()->playAttack();
	//			pmtl->onBlockRepaired(pWorld, m_Host->m_PickResult.block, m_Host, 100);
	//		}
	//		bBreak = true;
	//		return "";
	//	}
	//}
	//防止重力手套挖地
	if (m_Host->getCurToolID() == ITEM_GRAVITYGUN)
	{
		return "";
	}

	m_Host->doPick(false, false, true);
	bool ableDigAndAttack = skillAllowDigAndAttack();

	if (m_Host->m_PickType == 1)
	{
		return doPickBlockForLeftClickDown(bBreak);
	}
	else if (m_Host->m_PickType == 2)
	{
		SandboxResult homelandresult = SandboxEventDispatcherManager::GetGlobalInstance().
			Emit("Homeland_logicToHomeLand",
				SandboxContext(nullptr).SetData_Number("type", HomeLand_CheckIsAnimal).
				SetData_UserObject("blockpos", m_Host->m_PickResult.block).
				SetData_Userdata("ClientActor", "clientactor", static_cast<ClientActor*>(m_Host->m_PickResult.actor)));
		if (homelandresult.IsSuccessed())
		{
			return "ToRecover";
		}
		else
		{
			//连击状态 判定
			if (CheckEnterComboState(m_Host->m_PickType, static_cast<ClientActor*>(m_Host->m_PickResult.actor), 0, false) && ableDigAndAttack)
			{
				m_Host->clickActorOnTrigger(m_Host->m_PickResult.actor->getObjId(), m_Host->m_PickResult.actor->getDefID());
				return "ToComboAttack";
			}

			bool ret = doInteractForLeftClickDown();
			if (ret && m_Host->m_PickResult.actor)
			{
				auto veh = dynamic_cast<ActorVehicleAssemble*>(m_Host->m_PickResult.actor);
				if (veh && veh->canDig() && ableDigAndAttack)
				{
					//return "ToDig";
					return ChangeToDig2AttackBlock();
				}
			}
		}
	}
	else
	{
		//连击状态 判定
		if (CheckEnterComboState(m_Host->m_PickType) && ableDigAndAttack)
			return "ToComboAttack";
		
		if (ableDigAndAttack)
		{
			m_Host->m_PlayerAnimation->performDig();
		}
	}

	//if (!m_Host->isShakingCamera()) {
	//	m_Host->setShakeCamera(true, 1, 0.5);
	//}

	bBreak = false;

	return "";
}

const char* ActionIdleState::updateOnPCForLeftClick(float dtime, bool& bBreak)
{
	// 租赁服房间权限 是否能破坏方块
	if (logicToBlockCanBreak(false)) return m_nextState.c_str();

	int itemid = m_Host->getCurToolID();
	const ItemDef* def = GetDefManagerProxy()->getItemDef(itemid);

	if (def && def->UseTarget == ITEM_USE_GUN)
	{
		return "";
	}

	//防止重力手套挖地
	if (m_Host->getCurToolID() == ITEM_GRAVITYGUN)
	{
		return "";
	}

	if (m_Host->getCurToolID() == ITEM_USE_MUSIC_ITEM)
	{
		return "";
	}

	//防止载具上的司机挖机
	auto HostRidComp = m_Host->getRiddenComponent();
	if (HostRidComp && HostRidComp->isVehicleController())
	{
		return "";
	}

	//变形的角色或者使用技能时候不能使用挖掘 
	if (m_Host->isShapeShift()|| !skillAllowDigAndAttack())
		return "";

	auto pWorld = m_Host->getWorld();
	if (!pWorld)
	{
		return "";
	}
	int blockid = pWorld->getBlockID(m_Host->m_PickResult.block);
	auto targetBlockDef = GetDefManagerProxy()->getItemDef(blockid);
	bool b = GetWorldManagerPtr() && GetWorldManagerPtr()->isGodMode();
	//运行模型下才跑
	if (targetBlockDef && targetBlockDef->Type == ITEM_TYPE_BUILDING_BLOCK && !b)
	{
		return "ToAttackBlock";
	}

	//return "ToDig";
	return ChangeToDig2AttackBlock();
}

const char* ActionIdleState::updateOnPCForEDownClick(float dtime, bool& bBreak)
{
	int picktype = m_Host->doPick(false, false, false);
	//2是actor
	if (picktype != 2)
	{
		bBreak = false;
		return "";
	}

	IClientActor* actor = m_Host->m_PickResult.actor;
	if (actor && actor->IsKindOf<ClientMob>())
	{
		ClientMob* mob = static_cast<ClientMob*>(actor);
		// 如果怪物已死亡且可剥皮
		bool isDead = mob->isDead();
		bool isInteractiveCorpse = mob->getFlagBit(ACTORFLAG_INTERACTIVE_CORPSE);
		bool isSkinned = mob->getSkinned();
		// LOG_INFO("PlayerControl: mob [%lld, %d] isDead: %d, isInteractiveCorpse: %d, isSkinned: %d", mob->getObjId(), mob->getDefID(), isDead, isInteractiveCorpse, isSkinned);
		if (isDead && isInteractiveCorpse && !isSkinned)
		{
			bBreak = true;
			return "ToSkin";
		}
	}

	auto pickactor = dynamic_cast<ClientPlayer*>(m_Host->m_PickResult.actor);
	if (!pickactor)
	{
		bBreak = false;
		return "";
	}
	// 获取玩家的属性组件
	PlayerAttrib* playerAttrib = pickactor->getPlayerAttrib();
	if (!playerAttrib)
	{
		bBreak = false;
		return "";
	}

	// 检查玩家是否处于倒地状态
	if (!playerAttrib->isPlayerDowned())
	{
		bBreak = false;
		return "";
	}

	bBreak = true;
	return "ToRevivePlayer";
}
//这个只跑执行逻辑。显示ui的逻辑在playmain.lua中
bool ActionIdleState::KeyEPressDealOnPC(const char** strResult)
{
	KeyPressDetector* eKey = m_Host->getPlayerInputHelper()->getEKeyDetector();
	bool eKeyDown = eKey->isKeyDown();
	bool eKeyUp = eKey->isKeyUp();
	bool eKeyShortPress = eKey->isShortPress();
	bool ieKeyLongPress = eKey->isLongPress();
	(*strResult) = "";
	//短按e触发正常的e键使用
	if (eKeyShortPress)
	{
		if (m_Host->getPressEEvent() == 1)
		{

		}
		else if (m_Host->getPressEEvent() == 2 && m_Host->m_PickResult.intersect_block)
		{
			//auto block = m_Host->GetWorld()->getBlock(m_Host->m_PickResult.firstIntersectBlock);
			//auto pmtl = g_BlockMtlMgr.getSingleton().getMaterial(block.getResID());
			//if (pmtl->BlockTypeId() == BlockMaterial::BlockType::BlockType_WaterStorage)
			//{

			//}
		}
		else if (m_Host->getPressEEvent() == 3 && m_Host->m_PickResult.isIntersectLiquid)
		{
			int useType = 0;
			int toolId = 3;
			int itemid = m_Host->getCurToolID();
			const ItemDef* def = GetDefManagerProxy()->getItemDef(itemid);
			if (def && def->UseTarget == ITEM_USE_WATER)
			{
				//auto bug = m_Host->getEquipGrid(EQUIP_WEAPON);
				//bug->addDuration()
				useType = 3;
				toolId = itemid;
			}
			//(*strResult) = "ToDrinkWater";//不使用state的update逻辑
			m_Host->UseWater(PLAYEROP_STATUS_END, toolId, useType);
			return true;
		}
	}

	if (ieKeyLongPress)
	{
	}
	return false;
}

const char* ActionIdleState::updateOnPC(float dtime)
{
	if (m_Host && m_Host->m_InputInfo->leftClickDown || m_Host->m_InputInfo->rightClickDown)
	{
		auto* pFishingCom = m_Host->m_pFishingComponent;
		if (pFishingCom)
		{
			pFishingCom->endPlayFishByClient();
		}
	}
	if (m_Host)
	{
		PlayerAttrib* playerAttrib = m_Host->getPlayerAttrib();
		/*if (playerAttrib && playerAttrib->hasBuff(NERVE_PARALYSIS_VIGILANCE_BUFF))
		{
			return "";
		}*/
		if (playerAttrib && playerAttrib->getBuffEffectBankInfo(BuffAttrType::BUFFATTRT_FORBID_OPERATE))
		{
			return "";
		}
	}
	auto CarryComp = m_Host->getCarryComponent();
	if (CarryComp && CarryComp->isCarrying())
	{
		return carringState(dtime);
	}

	// 属性变身时，只能使用左右键攻击能力
	if (m_Host->isAttrShapeShift())
	{
		PlayerAttrib* playerAttrib = m_Host->getPlayerAttrib();
		if (playerAttrib)
		{
			if (m_Host->m_InputInfo->leftClickDown)
			{
				m_Host->doPick(false);
				if (doInteractForLeftClickDown())
					m_Host->doAttrShapeShiftLeftClick();
				return "";
			}
			if (m_Host->m_InputInfo->rightClickDown)
			{
				m_Host->doAttrShapeShiftRightClick();
				return "";
			}
		}
		return "";
	}

	//变形的角色不能使用道具
	if (m_Host->isInSpectatorMode()) return "";

	//在传送舱内除了下舱，不允许其它操作
	if (m_Host->isSittingInStarStationCabin()) return "";
	if (m_Host->isRidingByHippocompus())
	{
		return "";
	}
	//螃蟹钳击时无法操作
	if (m_Host->isCrabClamp())
	{

		if (m_Host->m_InputInfo->leftClickDown)
		{
			m_Host->m_PlayerAnimation->performDig();
			m_Host->addCrabClick();
		}
		return "";
	}
	else
	{
		m_Host->m_crabClickCount = 0;//非螃蟹钳击时重置单击次数
	}
	//在非变形的情况下，优先判断道具技能
	if (!m_Host->isShapeShift())
	{
		bool handle = false;
		const char* trans = itemSkillUse(handle);
		//有技能释放时,把左右键都屏蔽了
		if (trans != NULL && strlen(trans) > 0) 
		{
			if (m_Host->getSkillComponent())
			{
				m_Host->getSkillComponent()->addDiscountKey(Rainbow::InputKeyCode::SDLK_RBUTTON);
				m_Host->getSkillComponent()->addDiscountKey(Rainbow::InputKeyCode::SDLK_LBUTTON);
			}
			return trans;
		}
		if (handle) 
		{
			if (m_Host->getSkillComponent())
			{
				m_Host->getSkillComponent()->addDiscountKey(Rainbow::InputKeyCode::SDLK_RBUTTON);
				m_Host->getSkillComponent()->addDiscountKey(Rainbow::InputKeyCode::SDLK_LBUTTON);
			}
			return "";
		}
	}

	// 枪支使用逻辑
	if (LogicToState(Logic_UseGun)) return m_nextState.c_str();

	// 音乐道具逻辑
	if (LogicToState(Logic_UseMusicItem)) return m_nextState.c_str();
	//重力枪逻辑
	//if (m_Host->getCurToolID() == ITEM_GRAVITYGUN) return gravityGunState(dtime);

	const char* strResult = NULL;
	bool bBreak = true; //标记是否需要返回，中断执行
	if (skillAllowDigAndAttack())
	{
		if (KeyEPressDealOnPC(&strResult))
		{
			return strResult;
		}

		KeyPressDetector* rClickKey = m_Host->getPlayerInputHelper()->getRKeyDetector();
		if (rClickKey->isLongPress())
		{
			int itemid = m_Host->getCurToolID();
			const ItemDef* def = GetDefManagerProxy()->getItemDef(itemid);

			int picktype = m_Host->doPick(false, false, false);
			if (picktype == 1) //是方块
			{
				int blockid = m_Host->GetWorld()->getBlockID(m_Host->m_PickResult.block);

				BlockDef* blockDef = GetDefManagerProxy()->getBlockDef(blockid);
				// 手持锤子和 检查是否为支持升级的方块类型
				if (def && def->UseTarget == ITEM_USE_BUILDBLOCKREPAIR &&
					blockDef && IsSupportedBlockType(blockDef->Type.c_str()))
				{
					return "ToPieMenuCtrl";
				}

				// 检查是否为支持预览的方块类型
				std::string blockType = blockDef->Type.c_str();

				if( blockType == "territory") // 2411 是领地柜的方块ID，具体ID需要根据实际情况调整
				{
					return "ToPieMenuCtrl";
				}
			}
		}

		if (IsRightClickDown())
		{
			strResult = updateOnPCForRightClickDown(dtime, bBreak);
			if (bBreak)
			{
				return strResult;
			}
		}
		else if( IsRightClickUp())
		{
			strResult = updateOnPCForRightClickUp(dtime, bBreak);
			if (bBreak)
			{
				return strResult;
			}
		}

		if (IsRightClick()/*m_Host->m_InputInfo->rightClick*/
			&& GetSwitchTick()/*m_Host->m_SwitchTick*/ == 0 && !GetIClientGameManagerInterface()->getICurGame()->isOperateUI() && !m_Host->isAimState())
		{
			bBreak = true;
			strResult = updateOnPCForRightClick(dtime, bBreak);
			if (bBreak) return strResult;
		}

		if (IsLeftClickDown())//(m_Host->m_InputInfo->leftClickDown)
		{
			bBreak = true;
			strResult = updateOnPCForLeftClickDown(dtime, bBreak);
			if (bBreak) return strResult;
		}

		if (IsLeftClick()/*m_Host->m_InputInfo->leftClick*/ && GetSwitchTick()/*m_Host->m_SwitchTick*/ <= 0)
		{
			bBreak = true;
			strResult = updateOnPCForLeftClick(dtime, bBreak);
			if (bBreak) return strResult;
		}

		KeyPressDetector *eKey = m_Host->getPlayerInputHelper()->getEKeyDetector();
		bool eKeyDown = eKey->isKeyDown();
		bool eKeyUp = eKey->isKeyUp();
		bool isShortPress = eKey->isShortPress();
		bool isLongPress = eKey->isLongPress();

		//短按e触发正常的e键使用
		if (isShortPress && m_Host->checkInteractive())
		{
			bool needReturn = true;
			//点击的是动物 Actor交互给e键使用
			if (m_Host->m_PickResult.intersect_actor)
			{
				strResult = doPickActorForRightClickDown(needReturn);
				if (needReturn)
				{
					//if (skillComp) skillComp->addDiscountKey(Rainbow::InputKeyCode::SDLK_RBUTTON);
					return strResult;
				}
			}
			else if (m_Host->m_PickResult.intersect_block)
			{
				m_Host->triggerBlock(m_Host->m_PickResult.block, m_Host->m_PickResult.face, m_Host->m_PickResult.facepoint);
			}

			return "";
		}

		if (isLongPress && m_Host->checkInteractive())
		{
			strResult = updateOnPCForLongPress(dtime, bBreak);
			if (bBreak)
			{
				return strResult;
			}
		}

		KeyPressDetector* rKey = m_Host->getPlayerInputHelper()->getRKeyDetector();

		bool isRLongPress = rKey->isLongPress();
		if (isRLongPress)
		{
			int itemid = m_Host->getCurToolID();
			const ItemDef* def = GetDefManagerProxy()->getItemDef(itemid);
			if (ItemIDs::BLUEPRINT == itemid)//写死用id来做判断 //建筑图纸
			{
				return "ToPieMenuCtrl";
			}
		}

		if (eKeyUp)
		{
			LOG_WARNING("eKeyUp --");
		}

		//e键按下检查是否可以救援和剥皮
		if (eKeyDown)
		{
			LOG_WARNING("eKeyDown --");
			strResult = updateOnPCForEDownClick(dtime, bBreak);
			if (bBreak)
			{
				return strResult;
			}
		}
	}

	//新枪械自动射击
	auto comp = m_Host->getCustomGunComponent();
	if (comp && comp->IsAutoFire())
	{
		return HandleFire();
	}

	return "";
}

void ActionIdleState::updateForCurrentTool()
{
	if (m_Host->getCurToolID() == ITEM_GRAVITYGUN)
	{
		const ItemDef* def = GetDefManagerProxy()->getItemDef(m_Host->getCurToolID());
		if (def != NULL)
		{
			auto bindAComponent = m_Host->getBindActorCom();
			for (int i = 0; i < (int)def->SkillID.size(); i++)
			{
				if (def->SkillID[i] == 113)
				{
					continue;
				}
				std::vector<WORLD_ID> idvec;
				Rainbow::Vector3f currentEyePos, currentDir;
				WCoord centerPos;
				// 没有抓东西的情况下
				if (bindAComponent && !bindAComponent->hasBindChildren())//
				{
					idvec = m_Host->doPickPhysicsActorByItemSkill(def->SkillID[i], centerPos, currentEyePos, currentDir);
					if (idvec.size() > 0)
					{
						m_Host->m_PlayerAnimation->performGravityGun(true);
					}
					else
					{
						m_Host->m_PlayerAnimation->performGravityGun(false);
					}
				}
			}
		}
	}
	if (m_Host->getCurToolID() == ITEM_VEHICLE_LINK_TOOL)
	{
		// 手持连接器时，需要确认准星对准的能连线的方块（显示ui放大25%，连接线是取消的时候，拉出的线要变成红色）		
		SandboxEventDispatcherManager::GetGlobalInstance().Emit("VehicleMgr_findTheVehicleBlock",
			SandboxContext(nullptr));
	}
}

std::string ActionIdleState::update(float dtime)
{
	//濒死状态啥也不能干
	PlayerAttrib* playerAttrib = dynamic_cast<PlayerAttrib*>(m_Host->getAttrib());
	if (playerAttrib && playerAttrib->isPlayerDowned())
	{
		return "";
	}

	std::string retState;

	if (m_Host->getPlayerAttrib()->isActionDisabled())
		retState = "";
	else
	{
		if (m_BuildCoolDown >= 0)
		{
			m_BuildCoolDown -= dtime;
		}

		m_UseBlock += dtime;

		updateForCurrentTool();

		if (m_Host->getWorld() && m_Host->getWorld()->IsUGCEditMode()) {
			retState = "ToUGCEditor";
		} else {
			//移动端逻辑
			if (GetClientInfoProxy()->isMobile())
			{
				retState = updateOnMobile(dtime);
			}
			//PC端控制
			else
			{
				retState = updateOnPC(dtime);
			}
		}
	}

	if (triggerRightClickDownOrUseActionOnce)
		triggerRightClickDownOrUseActionOnce = false;

	ActionIdleStateGunAdvance::Update(dtime, retState);

	return retState;
}

void ActionIdleState::doBeforeLeaving()
{
	ActionIdleStateGunAdvance::Leave();

	if (m_Host)
	{
		auto* pFishingCom = m_Host->m_pFishingComponent;
		if (pFishingCom)
		{
			pFishingCom->endPlayFishByClient();
		}
	}
}

bool ActionIdleState::IsRightClickDown()
{
	return PlayerState::IsRightClickDown() || triggerRightClickDownOrUseActionOnce;
}

bool ActionIdleState::IsUseActionTrigger()
{
	return PlayerState::IsUseActionTrigger() || triggerRightClickDownOrUseActionOnce;
}

bool ActionIdleState::IsRightClickUp()
{
	return PlayerState::IsRightClickUp();
}

bool ActionIdleState::IsKeyEDown()
{
	return  m_Host->m_InputInfo->keyEClickDown;
}

//状态：To Combo State
bool ActionIdleState::CheckEnterComboState(int pickType, ClientActor* target, int interactType, bool interactplot)
{
	//if (GetIWorldConfigProxy()->getGameData("combo_attack") == 0)
	//	return false;
	{
		return false;
	}
	if (pickType == 1)
		return false;

	if (m_Host)
	{
		//mod鱼竿，不进连击状态
		const ToolDef* toolDef = GetDefManagerProxy()->getToolDef(m_Host->getCurToolID());
		if (toolDef && toolDef->IsModFishRod())
		{
			return false;
		}

		//开关
		if (GetWorldManagerPtr())
		{
			//规则 新战斗连击开关
			if (GetWorldManagerPtr()->getComboAttack() != 1)
				return false;
			//运行模式 玩家 玩家操作 可攻击
			if (GetWorldManagerPtr()->isGameMakerRunMode())
			{
				if (!m_Host->checkActionAttrState(ENABLE_ATTACK))
				{
					m_Host->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 14005);
					return false;
				}
			}
		}

		if (!m_Host->canAttack())
			return false;

		if (m_Host->isFlying())
			return false;

		//骑的不是坐骑
		if (m_Host->getRiddenComponent() && m_Host->getRiddenComponent()->isRiding())
		{
			ClientActor* ridingActor = m_Host->getRiddenComponent()->getRidingActor();
			if (ridingActor && GetDefManagerProxy()->getHorseDef(ridingActor->getDefID()) == nullptr)
				return false;
		}

		if (target && target->alive())
			return m_Host->performInteractActor(target, interactType, interactplot, true);
		else
			return true;
	}

	return false;
}

bool ActionIdleState::skillAllowDigAndAttack()
{
	if (m_Host)
	{
		if (m_Host->getSkillComponent())
		{
			return !(m_Host->getSkillComponent()->haveActiveSkill());
		}
	}
	return true;
}

char* ActionIdleState::ChangeToDig2AttackBlock()
{
	//static bool b_test = true;
	//if(b_test)
	//	return "ToDig";
	bool b = GetWorldManagerPtr() && GetWorldManagerPtr()->isGodMode();
	if (b)//编辑模式都是ToDIg
	{
		return "ToDig";
	}
	return "ToAttackBlock";
}
// 辅助函数，用于检查方块类型是否支持升级
bool ActionIdleState::IsSupportedBlockType(const std::string& blockType)
{
	static const char* supportedTypes[] = {
		"multiceiling",   // 天花板-地基
		"multiwall",      // 墙
		//"socautodoor",    // 2518 扶梯门
		//"socdoor",        // 2524 1X2 木门
		//"socdoubledoor",  // 2516 2X2 双木门
		"multibranch",    // 1X2 立杆
		"multistair",     // 2X2楼梯
		"multitriangle"   // 2X2斜板
	};

	const int typeCount = sizeof(supportedTypes) / sizeof(supportedTypes[0]);
	for (int i = 0; i < typeCount; ++i)
	{
		if (blockType == supportedTypes[i])
			return true;
	}

	return false;
}