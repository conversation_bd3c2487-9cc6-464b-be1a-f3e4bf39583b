#include "GameSuviveRecordNetHandler.h"
#include "PlayerControl.h"
#include "FullyCustomModelMgr.h"
#include "ImportCustomModelMgr.h"
#include "StarStationTransferMgr.h"
#include "IRecordInterface.h"
#include "ClientGameRecord.h"
#include "DesertTradeCaravanMgr.h"
#include "ResourceCenter.h"
#include "Core/extend/custommodel/CustomModelMgr.h"
#include "WorldManager.h"
#include "ClientInfoProxy.h"
#include "container_workshop.h"
#include "SandboxActorSubsystem.h"
using namespace Rainbow;
using namespace MNSandbox;

GameSuviveRecordNetHandler::GameSuviveRecordNetHandler()
{}

GameSuviveRecordNetHandler::~GameSuviveRecordNetHandler()
{
}


void GameSuviveRecordNetHandler::handleRoleEnterWorld2Client(const PB_PACKDATA_CLIENT &pkg)
{
	//LOG_INFO("GameSuviveRecordNetHandler::handleRoleEnterWorld2Client():");
	int curLoadingState = m_root->getCurLoadingState();
	PlayerControl *playerCtrl = m_root->getPlayerControl();
	if(curLoadingState >= 0 && playerCtrl == NULL)
	{
		if (MINIW::ScriptVM::game())
		{
			MINIW::ScriptVM::game()->setUserTypePointer("ClientCurGame", m_root->getTypeName(), m_root);
		}
		m_root->setupPlayerAndVM();
		playerCtrl = m_root->getPlayerControl();
	}
	if (!playerCtrl)
	{
		assert(playerCtrl);
		return;
	}
	PB_RoleEnterWorldHC roleEnterWorld;
	roleEnterWorld.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	const PB_WorldDesc &worldDesc = roleEnterWorld.worlddesc();
	const PB_OWGlobal &owGlobal = roleEnterWorld.globalinfo();
	const PB_PlayerInfo &playerInfo = roleEnterWorld.playerinfo();
	const PB_RoleData &roleData = playerInfo.roledata();
	const PB_WorldCreateData &pbCreateData = worldDesc.createdata();

	WorldCreateData createData;
	memset(&createData, 0, sizeof(createData));
	createData.terrtype = pbCreateData.terrtype();
	createData.randseed1 = pbCreateData.randseed1();
	createData.randseed2 = pbCreateData.randseed2();
	createData.rolemodel = pbCreateData.rolemodel();
	createData.xtiles = pbCreateData.tilesx();
	createData.ztiles = pbCreateData.tilesz();
	if(pbCreateData.seedstr().c_str())
	{
		strncpy(createData.seedstr, pbCreateData.seedstr().c_str(), MAX_SEED_STR_LEN);
		createData.seedstr[MAX_SEED_STR_LEN -1] = 0;		
	}
	else strcpy(createData.seedstr, "none");

	WorldManager* worldMgr = ENG_NEW(WorldManager)(worldDesc.worldid(), worldDesc.worldtype(), worldDesc.owneruin(), worldDesc.owneruin(), createData, worldDesc.fromowid(), worldDesc.specialtype(), worldDesc.ctype(), worldDesc.editorsceneswitch());
	worldMgr->setRealOwnerUin(worldDesc.realowneruin());
	SetWorldManagerPtr(worldMgr);
	m_root->setWorldMgr(worldMgr);
	MINIW::ScriptVM::game()->setUserTypePointer("WorldMgr", "WorldManager", worldMgr);
	MINIW::ScriptVM::game()->callFunction("initWorldMgr", "");
	worldMgr->onInit();
	MINIW::ScriptVM::game()->callFunction("ApplyBallConfig", "");
	MINIW::ScriptVM::game()->callFunction("ApplyRocketConfig", "");

#ifdef SURVIVAL_ACTOR_SUPPORT_SANDBOX_NODEEX
	if (playerInfo.has_nodeid()) {
		SANDBOX_ASSERT(playerInfo.nodeid() != 0);
		PlayerControl* playerCtrl = m_root->getPlayerControl();
		if (playerCtrl)
			playerCtrl->SetNodeid(playerInfo.nodeid());

		LOG_INFO("[player_nodeid] recv player nodeid: %d , objid:%d", playerInfo.nodeid(), playerInfo.objid());
	}
#endif//SURVIVAL_ACTOR_SUPPORT_SANDBOX_NODEEX
	clientLogin(playerInfo.objid(), owGlobal, roleData);
	MINIW::ScriptVM *scriptvm = MINIW::ScriptVM::game();
	GetSandboxActorSubsystem()->HandleLuaCurWorld(scriptvm, playerCtrl->getWorld());
	//scriptvm->setUserTypePointer("CurWorld", "World", playerCtrl->getWorld());
	scriptvm->callFunction("initDevWorld", "i" , playerCtrl->getWorld()->getCurMapID());

	WorldDesc *desc = GetClientInfoProxy()->getCurWorldDesc();
	if (desc->realowneruin == worldDesc.owneruin())
	{
		m_root->setCurLadingState(CLOAD_COMPLETE);
	}
	

	//�����ʹ�õ�������Դ����Ϣ�ȼ��ؽ����������ж���Դ�ܷ����
	SandboxEventDispatcherManager::GetGlobalInstance().Emit("ModManager_loadMapUseDownloadRes",SandboxContext(nullptr)
		.SetData_Number("_specialType", desc->_specialType)
		.SetData_Number("worldid", desc->worldid));


	//�Ƚ���Դ�ļ�����Ϣ��ʼ��������������Դ�Ķ�̬����
	if (ResourceCenter::GetInstancePtr())
	{
		ResourceCenter::GetInstancePtr()->initMapLibResourceManifest(desc->worldid, desc->_specialType);
	}

	if (CustomModelMgr::GetInstancePtr())
	{
		CustomModelMgr::GetInstancePtr()->loadMapCustomModelData(desc->worldid, desc->downloaduin, "", CUSTOM_MODEL_TYPE, desc->_specialType);
	}
		
	if (FullyCustomModelMgr::GetInstancePtr())
	{
		FullyCustomModelMgr::GetInstancePtr()->loadMapFullyCustomModelData(desc->worldid, desc->downloaduin, desc->_specialType);
	}

	SandboxEventDispatcherManager::GetGlobalInstance().Emit("ModManager_loadModCustomRes", SandboxContext(NULL).SetData_Number("owid", desc->downloaduin));

		
	if (CustomModelMgr::GetInstancePtr()) 
	{
		CustomModelMgr::GetInstancePtr()->checkMapCustomItemByClassInfo();
	}

	SandboxEventDispatcherManager::GetGlobalInstance().Emit("TransferMgr_loadMapTransferData",
		SandboxContext(nullptr)
		.SetData_String("owid", to_string(desc->worldid))
		.SetData_Number("specialType", desc->_specialType));

	SandboxEventDispatcherManager::GetGlobalInstance().Emit("VehicleMgr_loadVehicleItemidFile",
		SandboxContext(nullptr)
		.SetData_String("owid", to_string(desc->worldid))
		.SetData_Number("specialType", desc->_specialType));

	if (ImportCustomModelMgr::GetInstancePtr())
	{
		ImportCustomModelMgr::GetInstancePtr()->loadMapImportCustomModelData(desc->worldid, desc->downloaduin, desc->_specialType);
	}
	GetDesertTradeCaravanMgr().loadTradeCaravanData(desc->worldid, playerCtrl->getUin(), desc->_specialType);
	GetStarStationTransferMgr().loadMapStarStationData(desc->worldid, desc->_specialType);

	SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_setMapCSOwid",
		SandboxContext(nullptr).SetData_String("wid", to_string(desc->worldid)).SetData_Number("specialType", desc->_specialType));

	GameSurviveRecord* record = dynamic_cast<GameSurviveRecord*>(m_root);
	if (record)
	{
		GAMERECORD_INTERFACE_EXEC(setPause(record->getPause()), (void)0);
	}
}