
#pragma once
#include "ActionBase.h"

class SleepStateAction : public ActionBase
{
public:
	SleepStateAction(ClientPlayer* pPlayer);
	virtual ~SleepStateAction();
	virtual int sleep(const WCoord &blockpos, bool refreshRevivePoint = false);
	virtual int sleepInVehicleBed(const WCoord &blockpos, ActorVehicleAssemble* assemble);
	virtual bool isRestInBed()
	{
		return m_RestInBed;
	}

	virtual void setRestInBed(bool b)
	{
		m_RestInBed = b;
	}

	virtual void wakeUp(bool immediately, bool updateallflag, bool setrevive);

	inline virtual bool isSleeping()
	{
		return m_IsSleeping;
	}

	virtual void setSleeping(bool b);


	virtual void onRestInBedBuff();	//�����ɴ����õ�BUFF
	virtual void offRestInBedBuff();	//����ɴ����õ�BUFF

	virtual int getSleepingTimer()
	{
		return m_SleepTimer;
	}

	virtual void playerTrySleep();

	bool isInBed();
private:
	bool m_RestInBed;//������Ϣ��־λ
	bool m_IsSleeping;//˯�����

public:
	int m_SleepTimer;//tick��¼˯������ʱ��
	int m_sleepTickInDay;//˯��ʱ���
	int m_sleepRealHour;

};
