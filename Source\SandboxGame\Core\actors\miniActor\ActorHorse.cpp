
#include "ActorHorse.h"
#include "backpack.h"
#include "ActorCSProto.h"
#include "PlayerControl.h"
#include "GameNetManager.h"
#include "LuaInterfaceProxy.h"
#include "MpActorManager.h"
#include "OgreUtils.h"
#include "HorseLocomotion.h"
#include "InWaterComponent.h"
#include "RiddenComponent.h"
#include "EffectComponent.h"
#include "SoundComponent.h"
#include "DropItemComponent.h"
#include "ClientActorFuncWrapper.h"
#include "TriggerComponent.h"
//#include "DirectionByDirComponent.h"
#include "AttackedComponent.h"
#include "BlockMaterialMgr.h"
#include "ICloudProxy.h"
#include "SandboxIdDef.h"
#include "OgreSoundSystem.h"
#include "EffectManager.h"
#include "navigationpath.h"
#include "WorldManager.h"
#include "LivingAttrib.h"
#include "MobAttrib.h"
#include "Entity/OgreEntity.h"
#include "VacantComponent.h"

using namespace MNSandbox;

void HorseActorBody::setEquipItem(EQUIP_SLOT_TYPE slot, int itemid)
{
	if(slot == EQUIP_HEAD)
	{
		const HorseDef *def = static_cast<ActorHorse *>(m_OwnerActor)->getHorseDef();
		if (def)
		{
			if(itemid == 0) itemid = def->BornSaddle;

			showSaddle(itemid>0 ? def->SaddleModel : 0);
		}
	}
	//铁耙,钛耙,钻耙装备上
	else if (slot == EQUIP_LEGGING)
	{
		const HorseDef *def = static_cast<ActorHorse *>(m_OwnerActor)->getHorseDef();
		if (def && (itemid == ITEM_IRONHOE || itemid == ITEM_GOLDHOE || itemid == ITEM_DIAMONDHOE))
		{
			std::string modelId = "110031";
			ActorHorse* pActorHorse = static_cast<ActorHorse*>(m_OwnerActor);
			const MonsterDef* pModelDef = pActorHorse->getDef();
			if (pModelDef != nullptr) modelId = pModelDef->Model;

			//和itemid对应关系 1,2,3
			char path[256];
			int rakeNum = 1;
			if (itemid == ITEM_IRONHOE) //修复沃沃兽装备贴图错误 code by renjie
			{
				sprintf(path, "entity/%s/male%d.png", modelId.c_str(), 1);
				rakeNum = 3;
			}
			else
			{
				rakeNum = itemid - ITEM_IRONHOE;
			}
			showRake(rakeNum);
			Rainbow::Model* model = m_Entity->GetMainModel();
			if (model)
			{
				char path[256];
				sprintf(path, "entity/%s/male%d.png", modelId.c_str(), itemid - ITEM_IRONHOE+1);
				model->SetSkinTexture(s_RakeNames[rakeNum - 1], path);
			}
		}
		else if (def && itemid == 0)
		{
			showRake(0);
		}
	}
}

void HorseActorBody::setEquipItemNoOwner(EQUIP_SLOT_TYPE slot, int itemid, const HorseDef* def)
{
	if (slot == EQUIP_HEAD)
	{
		if (def)
		{
			if (itemid == 0) itemid = def->BornSaddle;

			showSaddle(itemid > 0 ? def->SaddleModel : 0);
		}
	}
	//铁耙,钛耙,钻耙装备上
	else if (slot == EQUIP_LEGGING)
	{
		if (def && (itemid == ITEM_IRONHOE || itemid == ITEM_GOLDHOE || itemid == ITEM_DIAMONDHOE))
		{
			std::string modelId = "110031";
			ActorHorse* pActorHorse = static_cast<ActorHorse*>(m_OwnerActor);
			if (pActorHorse != nullptr)
			{
				const MonsterDef* pModelDef = pActorHorse->getDef();
				if (pModelDef != nullptr) modelId = pModelDef->Model;
			}

			//和itemid对应关系 1,2,3
			char path[256];
			int rakeNum = 1;
			if (itemid == ITEM_IRONHOE) //修复沃沃兽装备贴图错误 code by renjie
			{
				sprintf(path, "entity/%s/male%d.png", modelId.c_str(), 1);
				rakeNum = 3;
			}
			else
			{
				rakeNum = itemid - ITEM_IRONHOE;
			}
			showRake(rakeNum);
			Rainbow::Model* model = m_Entity->GetMainModel();
			if (model)
			{
				char path[256];
				sprintf(path, "entity/%s/male%d.png", modelId.c_str(), itemid - ITEM_IRONHOE + 1);
				model->SetSkinTexture(s_RakeNames[rakeNum - 1], path);
			}
		}
		else if (def && itemid == 0)
		{
			showRake(0);
		}
	}
}

ActorHorse::ActorHorse() : ActorContainerMob(HORSE_EQUIP_INDEX), m_SwimSpeed(0), m_FlySpeed(0), m_JumpHeight(0),
                           m_BindUIN(0),
                           m_ArmorSlotOpen(false),m_RakeSlotOpen(false),
                           m_iUseSwimSpeed(0), m_iChargeAddSpeed(1), m_HorseDef(nullptr), m_CurCharge(-1),
                           m_ChargePeakTicks(0)
                           , m_iChargeRushTicks(0), m_DoingBenteng(false), m_bDoingRush(false), m_bDoingDiving(false),
                           m_bPlayDivingEff(false), m_bPreInWater(false), m_bChangeRide(false), m_HorseYaw(0),
                           m_HorsePitch(0),
                           m_CheckUINTicks(0),
                           m_shieldLife(0),
						   m_iPreFlashTick(-1),
						   m_bStopFlySound(false),
						   m_HorseFlags(0),
							m_iPreJumpSkill(-1),	//********：技能前置跳跃初始化  codeby： keguanqiang
						   m_iInvisibleTick(0),			//********：隐身时长初始化  codeby： keguanqiang
							m_riddenControlPriority(false)
{
	for (int i = 0; i < MAX_EQUIPS_EX; i++)
	{
		m_EquipGrids[i].reset(HORSE_EQUIP_INDEX + i);
	}
	for (int i = 0; i < (sizeof(m_fSkillCD) / sizeof(int)); i++)
	{
		m_fSkillCD[i] = 0;
	}
	memset(m_Skills, 0, sizeof(m_Skills));
	memset(m_OtherRiddens, 0, sizeof(m_OtherRiddens));
	//m_NumRidePos = 1;

	auto RidComp = sureRiddenComponent();
	if (RidComp)
	{
		RidComp->setNumRiddenPos(1);
	}

	m_fEnergy = 100.0f;
	m_nLastJumpTick = 0;
	m_bTired = false;
	m_bRakeToolLiving = false;
	m_LandSpeed = 0;
	m_CatSkillState = CatHorseSkillState::CAT_NORMAL;
	m_bFloatageing = false;
	m_PosRotationIncrements = 0;
	m_CurPlaySnd = NULL;
	m_shieldCoolingTicks = 0;
	m_aureoleIsShow = false;
	m_shieldIsShow = false;
	m_bIsFirstLoadOverEntity = true;
	m_ExtendSkills.clear();
	m_ExtendOtherRiddens.clear();
	m_ExtendOtherRiddens.resize(SRC_MAX_RIDDERS);
	m_iDragonFlyHeight = 0;
	m_bDragonFlyIsFly = false;
	m_iDragonFlyState = 0;
	m_fDragonFlyBoneHeight = 0;
	m_skillComList.clear();

	//RemoveComponent("InWaterComponent");
	CreateComponent<InWaterComponent_Horse>("InWaterComponent");
	createEvent();
}

ActorHorse::~ActorHorse()
{
	OGRE_RELEASE(m_CurPlaySnd);

	//test, 后面sandboxobj修改后，这里可以去掉了。
	for (size_t i = 0; i < m_skillComList.size(); i++)
	{
		SANDBOX_DELETE(m_skillComList[i]);
	}
	m_skillComList.clear();
}

void ActorHorse::createEvent()
{
	//Rainbow::Vector3f getRiddenBindPos(ClientActor *ridden)
	typedef ListenerFunctionRef<Rainbow::Vector3f&, ClientActor*> ListenerGetRiddenBindPos;
	ListenerGetRiddenBindPos* listenerGetRiddenBindPos = SANDBOX_NEW(ListenerGetRiddenBindPos, [&](Rainbow::Vector3f& ret, ClientActor* ridden) -> void {
		ret = this->getRiddenBindPos(ridden);
		});
	ClientMob::Event2().Subscribe("getRiddenBindPos", listenerGetRiddenBindPos);

	//int findEmptyRiddenIndex(int index)
	typedef ListenerFunctionRef<int&, int> ListenerFindEmptyRiddenIndex;
	ListenerFindEmptyRiddenIndex* listenerFindEmptyRiddenIndex = SANDBOX_NEW(ListenerFindEmptyRiddenIndex, [&](int& ret, int index) -> void {
		ret = this->findEmptyRiddenIndex(index);
		});
	ClientMob::Event2().Subscribe("findEmptyRiddenIndex", listenerFindEmptyRiddenIndex);

	//WORLD_ID getRiddenByActorID(int i)
	typedef ListenerFunctionRef<WORLD_ID&, int> ListenerGetRiddenByActorID;
	ListenerGetRiddenByActorID* listenerGetRiddenByActorID = SANDBOX_NEW(ListenerGetRiddenByActorID, [&](WORLD_ID& ret, int i) -> void {
		ret = this->getRiddenByActorID(i);
		});
	ClientMob::Event2().Subscribe("getRiddenByActorID", listenerGetRiddenByActorID);


	//void setRiddenByActorObjId(WORLD_ID objId, int i /* = 0 */)
	typedef ListenerFunctionRef<WORLD_ID, int> ListenerSetRiddenByActorObjId;
	ListenerSetRiddenByActorObjId* listenerSetRiddenByActorObjId = SANDBOX_NEW(ListenerSetRiddenByActorObjId, [&](WORLD_ID objId, int i) -> void {
		this->setRiddenByActorObjId(objId, i);
		});
	ClientMob::Event2().Subscribe("setRiddenByActorObjId", listenerSetRiddenByActorObjId);

	//ClientActor* getRiddenByActor(int i = 0)
	typedef ListenerFunctionRef<ClientActor*&, int> ListenerGetRiddenByActor;
	ListenerGetRiddenByActor* listenerGetRiddenByActor = SANDBOX_NEW(ListenerGetRiddenByActor, [&](ClientActor*& ret, int i) -> void {
		ret = this->getRiddenByActor(i);
		});
	ClientMob::Event2().Subscribe("getRiddenByActor", listenerGetRiddenByActor);


	//int findRiddenIndex(ClientActor *ridden)
	typedef ListenerFunctionRef<int&, ClientActor*> ListenerFindRiddenIndex;
	ListenerFindRiddenIndex* listenerFindRiddenIndex = SANDBOX_NEW(ListenerFindRiddenIndex, [&](int& ret, ClientActor* ridden) -> void {
		ret = this->findRiddenIndex(ridden);
		});
	ClientMob::Event2().Subscribe("findRiddenIndex", listenerFindRiddenIndex);

	//bool getRiddenChangeFPSView()
	typedef ListenerFunctionRef<bool&> ListenerGetRiddenChangeFPSView;
	ListenerGetRiddenChangeFPSView* listenerGetRiddenChangeFPSView = SANDBOX_NEW(ListenerGetRiddenChangeFPSView, [&](bool& ret) -> void {
		ret = this->getRiddenChangeFPSView();
		});
	ClientMob::Event2().Subscribe("getRiddenChangeFPSView", listenerGetRiddenChangeFPSView);

	//bool canBeRided(ClientPlayer *player)
	typedef ListenerFunctionRef<bool&, ClientPlayer*> ListenerCanBeRided;
	ListenerCanBeRided* listenerCanBeRided = SANDBOX_NEW(ListenerCanBeRided, [&](bool& ret, ClientPlayer *player) -> void {
		ret = this->canBeRided(player);
		});
	ClientMob::Event2().Subscribe("canBeRided", listenerCanBeRided);

	//WCoord getRiderPosition(ClientActor *ridden)
	typedef ListenerFunctionRef<WCoord&, ClientActor*> ListenerGetRiderPosition;
	ListenerGetRiderPosition* listenerGetRiderPosition = SANDBOX_NEW(ListenerGetRiderPosition, [&](WCoord& ret, ClientActor *ridden) -> void {
		ret = this->getRiderPosition(ridden);
		});
	ClientMob::Event2().Subscribe("getRiderPosition", listenerGetRiderPosition);

	typedef ListenerFunctionRef<float&> ListenerFallHurtSubtract;
	ListenerFallHurtSubtract* listenerFallHurtSubtract = SANDBOX_NEW(ListenerFallHurtSubtract, [&](float& ret) -> void {
		ret = this->getFallHurtSubtract();
		});
	ClientMob::Event2().Subscribe("getFallHurtSubtract", listenerFallHurtSubtract);


	typedef ListenerFunctionRef<float&> ListenerFallHurtRate;
	ListenerFallHurtRate* listenerFallHurtRate = SANDBOX_NEW(ListenerFallHurtRate, [&](float& ret) -> void {
		ret = this->getFallHurtRate();
		});
	ClientMob::Event2().Subscribe("getFallHurtRate", listenerFallHurtRate);

}

bool ActorHorse::init(int monsterid)
{
	m_HorseDef = GetDefManagerProxy()->getHorseDef(monsterid);


	//对应的乘骑mod没有加载
	///////////////////////////////////////////////////////
	//Mod加载部分需要优化///////////////////////////////////
	//////////////////////////////////////////////////////
	if (m_HorseDef == NULL)
	{

		//getAttrib()->addHP(-10000);
		return false;
		//g_ModMgr.setToDefaultHorseDef(m_HorseDef);
	}

	if(!ActorContainerMob::init(monsterid)) return false;


	getAttrib()->resetMaxLife((float)GenRandomInt(m_HorseDef->MinHP, m_HorseDef->MaxHP));
	m_LandSpeed = GenRandomInt(m_HorseDef->MinLandSpeed, m_HorseDef->MaxLandSpeed);
	m_SwimSpeed = GenRandomInt(m_HorseDef->MinSwimSpeed, m_HorseDef->MaxSwimSpeed);
	m_FlySpeed = GenRandomInt(m_HorseDef->MinFlySpeed, m_HorseDef->MaxFlySpeed);
	m_JumpHeight = GenRandomInt(m_HorseDef->MinJumpHeight, m_HorseDef->MaxJumpHeight);
	if(GenRandomInt(100) < m_HorseDef->ArmorSlotProb) m_ArmorSlotOpen = true;
	else m_ArmorSlotOpen = false;
	m_RakeSlotOpen = true;

	getBody()->setEquipItem(EQUIP_HEAD, 0);
	getBody()->setEquipItem(EQUIP_BREAST, 0);
	getBody()->setEquipItem(EQUIP_LEGGING, 0);

	if(hasHorseSkill(HORSE_SKILL_LAVAWALK)) getAttrib()->setImmuneToFire(2);
	auto RidComp = sureRiddenComponent();
	if (RidComp)
	{
		if (hasHorseSkill(HORSE_SKILL_TWO_PEOPLE) || hasHorseSkill(HORSE_SKILL_HORIZONTAL_TWO_PEOPLE) || hasHorseSkill(HORSE_SKILL_TWO_PEOPLE_STAND))
			RidComp->setNumRiddenPos(2);
		if (hasHorseSkill(HORSE_SKILL_FOURPEOPLE))
		{
			RidComp->setNumRiddenPos(4);
		}
	}

	bool isCall = false;
	MINIW::ScriptVM::game()->callFunction("HorseSkillManagerGetConfig", "u[ActorHorse]>b", this, &isCall);
	if (isCall) return true;

	if(hasHorseSkill(HORSE_SKILL_MUTATE_FLY))
	{
		float skillvals[7];
		if (getHorseSkill(HORSE_SKILL_MUTATE_FLY, skillvals))
		{
			static_cast<HorseLocomotion *>(getLocoMotion())->setSpecialGravity(skillvals[5], skillvals[6]);
		}
	}

	m_iUseSwimSpeed = 0;
	if (hasHorseSkill(HORSE_SKILL_FLOAT)) {
		m_iUseSwimSpeed += 1;
		getLocoMotion()->m_yOffset = -80;
	}

	if (hasHorseSkill(HORSE_SKILL_DIVING) ||
		hasHorseSkill(HORSE_SKILL_GIANTWHALE_SWIM) ||
		hasHorseSkill(HORSE_SKILL_SWAN_DIVING)) {
		m_iUseSwimSpeed += 2;
		getLocoMotion()->m_yOffset = -20;
	}

	if (hasHorseSkill(HORSE_SKILL_WATER_RUSH)) {
		m_iUseSwimSpeed += 4;
		getLocoMotion()->m_yOffset = -20;
		float skillvals[7];
		if (getHorseSkill(HORSE_SKILL_WATER_RUSH, skillvals)) {
			m_iChargeAddSpeed = (int)skillvals[2];
			m_iChargeAddSpeed = m_iChargeAddSpeed <= 0 ? 1 : m_iChargeAddSpeed;
		}
	}



	if (GetDefManagerProxy()->getStoreHorseByID(monsterid))
	{
		m_iCheckAccountHorseCD = CHECK_ACCOUNT_HORSE_CD;
	}
	else
		m_iCheckAccountHorseCD = -1;

	return true;
}

flatbuffers::Offset<FBSave::ActorHorse> ActorHorse::saveMob(SAVE_BUFFER_BUILDER& builder)
{
	auto mobdata = ClientMob::saveMob(builder);

	flatbuffers::Offset<FBSave::ItemIndexGrid> grids[MAX_EQUIPS_EX];
	int count = 0;

	for (int i = 0; i < MAX_EQUIPS_EX; i++)
	{
		if (m_EquipGrids[i].isEmpty()) continue;
		grids[count++] = m_EquipGrids[i].saveWithIndex(builder);
	}

	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::ItemIndexGrid>>> items = 0;
	if (count > 0) items = builder.CreateVector(grids, count);

	int skills[MAX_SKILLS];
	for (int i = 0; i < MAX_SKILLS; i++)
	{
		skills[i] = m_Skills[i].id | (m_Skills[i].active << 16);
	}

	std::vector<int> extendSkillProfile(m_ExtendSkills.size());
	for (size_t i = 0; i < m_ExtendSkills.size(); i++)
	{
		extendSkillProfile[i] = m_ExtendSkills[i].id | (m_ExtendSkills[i].active << 16);
	}

	flatbuffers::Offset<flatbuffers::Vector<uint64_t>> otherriddens = 0;
	auto RidComp = getRiddenComponent();
	if (RidComp && RidComp->getNumRiddenPos() > 1)
	{
		uint64_t riddens[16];
		for (int i = 1; i < RidComp->getNumRiddenPos(); i++)
		{
			riddens[i - 1] = m_OtherRiddens[i - 1];
		}
		otherriddens = builder.CreateVector(riddens, RidComp->getNumRiddenPos() - 1);
	}

	auto horse = FBSave::CreateActorHorse(
		builder, mobdata, getRiddenByActorID(),
		items, builder.CreateVector(skills, MAX_SKILLS),
		(int16_t)getAttrib()->getMaxHP(), m_LandSpeed, m_FlySpeed,
		m_SwimSpeed, m_JumpHeight, m_BindUIN,
		m_ArmorSlotOpen ? 1 : 0, otherriddens,
		m_fEnergy, m_bTired, m_shieldCoolingTicks, builder.CreateVector(extendSkillProfile), builder.CreateVector(m_ExtendOtherRiddens), m_RakeSlotOpen ? 1 : 0, m_bRakeToolLiving);
	return horse;
}

flatbuffers::Offset<FBSave::SectionActor> ActorHorse::save(SAVE_BUFFER_BUILDER &builder)
{
	auto horse = saveMob(builder);
	return saveSectionActor(builder, FBSave::SectionActorUnion_ActorHorse, horse.Union());
}

bool ActorHorse::load(const void *srcdata, int version)
{
	auto src = reinterpret_cast<const FBSave::ActorHorse *>(srcdata);

	if(!ClientMob::load(src->mobdata(), version))
	{
		return false;
	}

	setRiddenByActorObjId(src->ridden());
	if (getRiddenByActorID() > 0)
	{
		DestroyComponent(GetComponentByName("NavigationPath"));
	}

	if(src->equips())
	{
		for(size_t i=0; i<src->equips()->size(); i++)
		{
			const FBSave::ItemIndexGrid *itemsrc = src->equips()->Get(i);

			int offset = itemsrc->index() - HORSE_EQUIP_INDEX;
			m_EquipGrids[offset].load(itemsrc);
		}
	}
	if(src->skills())
	{
		for(size_t i=0; i<src->skills()->size(); i++)
		{
			int s = src->skills()->Get(i);
			m_Skills[i].id = s & 0xffff;
			m_Skills[i].active = s >> 16;
		}
	}

	if (src->extendskills())
	{
		m_ExtendSkills.resize(src->extendskills()->size());
		for (size_t i = 0; i < src->extendskills()->size(); i++)
		{
			int es = src->extendskills()->Get(i);
			m_ExtendSkills[i].id = es & 0xffff;
			m_ExtendSkills[i].active = es >> 16;
		}
	}

	//if(!m_EquipGrids[0].isEmpty()) getBody()->showSkin(DEFAULT_SADDLE, true);

	if (src->maxhp() > 0)
	{
		getAttrib()->initMaxHP(src->maxhp());
	}
	if (version == 0) getAttrib()->initMaxHP(getAttrib()->getMaxHP()*5.0f);

	m_LandSpeed = src->landspeed();
	m_FlySpeed = src->flyspeed();
	m_SwimSpeed = src->swimspeed();
	m_JumpHeight = src->jumpheight();
	m_BindUIN = src->binduin();
	m_ArmorSlotOpen = src->armoropen()==1;
	m_RakeSlotOpen = src->rakeopen()==1;
	m_shieldCoolingTicks = src->shieldCoolingTicks();
	auto otherriddens = src->otherriddens();
	if(otherriddens)
	{
		for(size_t i=0; i<otherriddens->size(); i++)
		{
			m_OtherRiddens[i] = otherriddens->Get(i);
		}
	}

	if (src->extendriddens()) 
	{
		m_ExtendOtherRiddens.resize(src->extendriddens()->size());
		for (size_t i = 0; i < src->extendriddens()->size(); i++)
		{
			m_ExtendOtherRiddens[i] = src->extendriddens()->Get(i);
		}
	}

	if(m_FlySpeed == 0) m_FlySpeed = m_LandSpeed;

	MobAttrib *attr = static_cast<MobAttrib *>(getAttrib());
	attr->equip(EQUIP_HEAD, &m_EquipGrids[0]);
	if (m_ArmorSlotOpen) attr->equip(EQUIP_BREAST, &m_EquipGrids[1]);
		if (m_RakeSlotOpen) attr->equip(EQUIP_LEGGING, &m_EquipGrids[2]);

	m_fEnergy = src->energy();
	m_bTired = src->tired();
	m_bRakeToolLiving = src->rakeToolLiving();
	if (m_bTired)
	{
		auto effectComponent = getEffectComponent();
		if (effectComponent)
		{
			effectComponent->playBodyEffect("mob_3421_tired");
		}
	}

	m_iUseSwimSpeed = 0;

	if (getLocoMotion())
	{
		if (hasHorseSkill(HORSE_SKILL_FLOAT)) {
			m_iUseSwimSpeed += 1;
			getLocoMotion()->m_yOffset = -80;
		}

		if (hasHorseSkill(HORSE_SKILL_DIVING) ||
			hasHorseSkill(HORSE_SKILL_GIANTWHALE_SWIM) ||
			hasHorseSkill(HORSE_SKILL_SWAN_DIVING)) {
			m_iUseSwimSpeed += 2;
			getLocoMotion()->m_yOffset = -20;
		}

		if (hasHorseSkill(HORSE_SKILL_WATER_RUSH)) {
			m_iUseSwimSpeed += 4;
			getLocoMotion()->m_yOffset = -20;
			float skillvals[7];
			if (getHorseSkill(HORSE_SKILL_WATER_RUSH, skillvals)) {
				m_iChargeAddSpeed = (int)skillvals[2];
				m_iChargeAddSpeed = m_iChargeAddSpeed <= 0 ? 1 : m_iChargeAddSpeed;
			}
		}
	}
	return true;
}

ActorBody *ActorHorse::newActorBody()
{
	ActorBody *actorBody = SANDBOX_NEW(HorseActorBody, this);
	ClientMob::initMobBody(actorBody, m_Def);
	ClientMob::updateModelName(); //20210902 生物坐骑初始外观问题 codeby qinpeng
	return actorBody;
}

void ActorHorse::enterWorld(World *pworld)
{
	ClientMob::enterWorld(pworld);

	checkBindPlayer(true);
	m_DoingBenteng = false;

	if (m_Def && m_Def->ID == 3912) //牦牛 能被骑，但不能被控制
	{
		m_riddenControlPriority = true;
	}
}

ActorLocoMotion *ActorHorse::newLocoMotion()
{
	//HorseLocomotion *loco = dynamic_cast<HorseLocomotion*>(AddComponent("HorseLocomotion"));
	return CreateComponent<HorseLocomotion>("HorseLocomotion");
}

static int s_JumpVel[] =
{
	40, 60, 80, 100, 120, 130, 140, 150, 160, 170, 180, 190, 200, 210, 220, 230, 240, 250, 260, 270, 280, 290, 300
};
float ActorHorse::getFallHurtSubtract()
{
	if(getMoveMode() == ACTORMOVE_JUMP)
	{
		return 12.0f;
	}

	int i = 0;
	for(; i<sizeof(s_JumpVel)/sizeof(int); i++)
	{
		if(m_JumpHeight < s_JumpVel[i]) break;
	}

	return float(i);
}

float ActorHorse::getUIModelViewScale()
{
	return m_HorseDef->UIScale;
}

bool ActorHorse::hasSaddle()
{
	//return getEquipItem(EQUIP_HEAD)>0 || m_HorseDef->BornSaddle;
	return true;
}

bool ActorHorse::needUpdateAI()
{
	return (getRiddenByActor() == NULL || !hasSaddle()) && m_NeedUpdateAI;
}

float ActorHorse::getRunWalkFactor()
{
	if(needUpdateAI()) return ClientMob::getRunWalkFactor();

	ClientActor *riddenactor = getRiddenByActor();
	if(riddenactor == NULL) return 1.0f;

	LivingLocoMotion *riddenloc = dynamic_cast<LivingLocoMotion *>(riddenactor->getLocoMotion());
	
	if(riddenloc && riddenloc->m_MoveForward <= 0) return 0.5f;
	else
	{
		float speed = MobAttrib::defSpeed2MoveSpeed(getRiddenLandSpeed());
		if(isInCharge()) speed *= 1.2f;
		if (!Rainbow::FloatIsZero(MobAttrib::defSpeed2MoveSpeed(m_Def->Speed)))
		{
			return speed / MobAttrib::defSpeed2MoveSpeed(m_Def->Speed);
		}
		else
		{
			return speed;
		}
	}
}

void ActorHorse::onDie()
{
	if (m_BindUIN > 0)
	{
		ActorManager* actorMgr = dynamic_cast<ActorManager*>(m_pWorld->getActorMgr());
		if (!actorMgr) return ;
		ClientPlayer *owner = actorMgr->findPlayerByUin(m_BindUIN);
		if (owner) owner->updateAccountHorse(m_HorseDef->ID, 0.0f, -TICKS_ONEDAY / 24,0);
	}

	ActorContainerMob::onDie();
	breakInvisible();		// ********：死亡打断隐身  codeby： keguanqiang
}

int ActorHorse::getStepHeight()
{
	if (getRiddenByActor()) return BLOCK_SIZE;
	else return BLOCK_SIZE / 2;
}

int ActorHorse::getItemAndAttrib(RepeatedPtrField<PB_ItemData>* pItemInfos, RepeatedField<float>* pAttrInfos)
{
	for (int i = 0; i < MAX_EQUIPS_EX; i++)
	{
		if (m_EquipGrids[i].isEmpty()) continue;

		storeGridData(pItemInfos->Add(), &m_EquipGrids[i]);
	}

	return MAX_EQUIPS_EX;
}

BackPackGrid *ActorHorse::index2Grid(int index)
{
	assert(index >= HORSE_EQUIP_INDEX && index < HORSE_EQUIP_INDEX + MAX_EQUIPS_EX);

	return &m_EquipGrids[index - HORSE_EQUIP_INDEX];
}

bool ActorHorse::canPutItem(int index)
{
	if (!m_ArmorSlotOpen && index == HORSE_EQUIP_INDEX + 1)
	{
		return false;
	}
	else if (!m_RakeSlotOpen && index == HORSE_EQUIP_INDEX + 2)
	{
		return false;
	}

	return true;
}

bool ActorHorse::canBeRided(ClientPlayer *player)
{
	//if (getRiddenByActor() != NULL) return false;

	auto RidComp = getRiddenComponent();
	if (RidComp && RidComp->getNumRiddenPos() == 1)
	{
		if (getRiddenByActor() != NULL) return false;
		if (m_BindUIN > 0 && m_BindUIN != player->getUin()) return false;
	}
	else
	{		 
		 if(getRiddenByActorID(0) == 0)
		 {
			if (m_BindUIN > 0 && m_BindUIN != player->getUin()) return false;
		 }
		 else if (RidComp && RidComp->getNumRiddenPos() == 2 && getRiddenByActorID(1) != 0)
		 {
			 return false;
		 }
		 else if(RidComp && RidComp->getNumRiddenPos() == 4 && getRiddenByActorID(1) != 0 && getRiddenByActorID(2) != 0 && getRiddenByActorID(3) != 0)
		 {
			return false;
		 }		
	}

	int curtool = player->getCurToolID();
	if (isBreedItem(curtool) > 0) return false;
	//if (!hasSaddle() && m_Def->ID!= 3912) return false;

	const ToolDef *tool = GetDefManagerProxy()->getToolDef(curtool);
	if (tool && (tool->Type == 19 || tool->Type == 20)) return false;
	if (tool && (ActorHorse::mobCanRake(m_Def->ID)) && (curtool == ITEM_IRONHOE || curtool == ITEM_GOLDHOE || curtool == ITEM_DIAMONDHOE)) return false;
	if (tool && (m_Def->ID == 3912) && (curtool == 11320 )) return false;  //牦牛空奶瓶不给骑

	//// 虚空生物相关
	//if (tool && (m_Def->ID == 3247) && (curtool == 11320) && getMilkingTimes() > 0)
	//{
	//	return false;  //虚空角鹿 有奶的时候 空奶瓶不给骑
	//}
	bool bCanUse = false;
	if (VacantComponent::GetInteractVacantEnergy(this, curtool, bCanUse) > 0)
	{
		return false;
	}

	if(getCanRideByPlayer())
		return true;
	else
		return false;
}

void ActorHorse::afterChangeGrid(int index)
{
	ActorContainerMob::afterChangeGrid(index);

	MobAttrib *attr = static_cast<MobAttrib *>(getAttrib());
	if (index == HORSE_EQUIP_INDEX + 0)
	{
		//getBody()->showSkin(DEFAULT_SADDLE, !m_EquipGrids[0].isEmpty());
		attr->equip(EQUIP_HEAD, &m_EquipGrids[0]);
	}
	else if (index == HORSE_EQUIP_INDEX + 1)
	{
		attr->equip(EQUIP_BREAST, &m_EquipGrids[1]);
	}
	else if (index == HORSE_EQUIP_INDEX + 2)
	{
		attr->equip(EQUIP_LEGGING, &m_EquipGrids[2]);
	}

	if (m_pWorld && m_BindUIN > 0)
	{
		ClientActorMgr* actorMgr = m_pWorld->getActorMgr() ? m_pWorld->getActorMgr()->ToCastMgr() : nullptr;
		if (!actorMgr) return;
		ClientPlayer *player = actorMgr->findPlayerByUin(m_BindUIN);
		if (player)
		{
			player->setAccountHorseEquip(m_HorseDef->ID, index - HORSE_EQUIP_INDEX, m_EquipGrids[index - HORSE_EQUIP_INDEX].getItemID());
		}
	}
}

void ActorHorse::clearEquip(int index)
{
	if (index >= 0 && index < MAX_EQUIPS_EX)
	{
		ActorManager* actorMgr = dynamic_cast<ActorManager*>(m_pWorld->getActorMgr());
		if (!actorMgr) return;
		m_EquipGrids[index].clear();
		if (m_pWorld && m_BindUIN > 0)
		{
			ClientPlayer* player = actorMgr->findPlayerByUin(m_BindUIN);
			if (player)
			{
				player->setAccountHorseEquip(m_HorseDef->ID, index, 0);
			}
		}
	}
}

bool ActorHorse::needCheckVisible()
{
	if (getRiddenByActor() == g_pPlayerCtrl)
	{
		return false;
	}
	return true;
}

/**
 * @brief 按装备栏位置给坐骑穿戴装备, 下标须与类型一一对应
 * 
 * @param index 下标, 0-2 , 其中2可能已废弃
 * @param itemid 装备的物品ID
 */
void ActorHorse::equipByIndex(size_t index, int itemid)
{
	/*
	static std::map<size_t, int> Index2ItemType = {
		{ 0, 19 },
		{ 1, 20 },
	};
	*/
	const ToolDef *def = GetDefManagerProxy()->getToolDef(itemid);
	if (!def)
	{
		return;
	}

	if (index == 0)
	{
		if (def->Type == 19)
		{
			equipSaddle(itemid);
			return;
		}
	}
	else if (index == 1)
	{
		if (def->Type == 20)
		{
			equipSaddle(itemid);
			return;
		}
	}
	jsonxx::Object obj;
	obj << "index" << index << "itemid" << itemid;
	Rainbow::GetICloudProxyPtr()->InfoLog(m_BindUIN, 0, "cheat_horse_equip", obj);
	return;
}

void ActorHorse::equipSaddle(BackPackGrid &src)
{
	const ToolDef *def = GetDefManagerProxy()->getToolDef(src.getItemID());
	if (def)
	{
		size_t index;
		if (def->Type==19)
			index = 0;
		else if (def->Type==20)
			index = 1;
		else
			return;
		BackPackGrid *destgrid = &m_EquipGrids[index];
		BackPackGrid tmp(src);
		src.setItem(*destgrid);
		destgrid->setItem(tmp);

		afterChangeGrid(destgrid->getIndex());
	}
}

void ActorHorse::equipSaddle(int itemid)
{
	BackPackGrid grid;
	SetBackPackGrid(grid, itemid, 1);

	equipSaddle(grid);
}

void ActorHorse::equipRake(BackPackGrid &src)
{
	const ToolDef *def = GetDefManagerProxy()->getToolDef(src.getItemID());
	if (def)
	{
		BackPackGrid *destgrid = &m_EquipGrids[2];
		BackPackGrid tmp(src);
		src.setItem(*destgrid);
		destgrid->setItem(tmp);

		afterChangeGrid(destgrid->getIndex());
	}
}

void ActorHorse::equipRake(int itemid)
{
	BackPackGrid grid;
	SetBackPackGrid(grid, itemid, 1);

	equipRake(grid);
}

void ActorHorse::setAccountBind(int uin)
{
	m_BindUIN = uin;
}

bool ActorHorse::getHorseSkill(int id, float *vals)
{
	if (!m_HorseDef)
	{
		return false;
	}

	if(id == HORSE_SKILL_BREED)
	{
		if(m_HorseDef->EggBlock>0 && m_HorseDef->EggGenTicks>0) return true;
		else return false;
	}

	for(int i=0; i<MAX_HORSE_SKILL; i++)
	{
		if(m_HorseDef->BornSkills[i] == id)
		{
			const HorseAbilityDef *abdef = GetDefManagerProxy()->getHorseAbilityDef(id);
			if(abdef)
			{
				if(vals)
				{
					vals[0] = abdef->Effect[0];
					vals[1] = abdef->Effect[1];
					vals[2] = abdef->Effect[2];
					vals[3] = abdef->Effect[3];
					vals[4] = abdef->Effect[4];
					vals[5] = abdef->Effect[5];
					vals[6] = abdef->Effect[6];
				}
				return true;
			}
		}
	}

	return false;
}

std::vector<float> ActorHorse::getHorseSkill2(int id)
{
	float vals[7];
	std::vector<float> list;

	if (getHorseSkill(id, vals))
	{
		for (int i = 0; i < 7; i++)
		{
			list[i] = vals[i];
		}
	}
	return list;
}

// ********：增加新技能-获取神奇眼泪冷却时间  codeby： keguanqiang
int ActorHorse::getShieldCoolingTicks() {
	if (hasHorseSkill(HORSE_SKILL_SHIELD) || hasHorseSkill(HORSE_SKILL_SOLAR_DISK)
		|| hasHorseSkill(HORSE_SKILL_MAGIC_TEARS))									
		return m_shieldCoolingTicks;
	else
		return 1;
}

// ********：增加新技能-设置神奇眼泪冷却时间  codeby： keguanqiang
void ActorHorse::setShieldCoolingTicks(int ticks) {
	if (hasHorseSkill(HORSE_SKILL_SHIELD) || hasHorseSkill(HORSE_SKILL_SOLAR_DISK)
		|| hasHorseSkill(HORSE_SKILL_MAGIC_TEARS))									
		m_shieldCoolingTicks = ticks;
}

void ActorHorse::getHorseSkillList(int &skill1, int &skill2, int &skill3, int& skill4)
{
	if(m_HorseDef->EggBlock>0 && m_HorseDef->EggGenTicks>0)  skill1 = HORSE_SKILL_BREED;
	else skill1 = 0;

	skill2 = m_HorseDef->BornSkills[0];
	skill3 = m_HorseDef->BornSkills[1];
	skill4 = m_HorseDef->BornSkills[2];
}

int ActorHorse::getHorseCanAgeTick()
{
	if(m_HorseDef == NULL) return 1;
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(m_pWorld->getActorMgr());
	if(m_pWorld == NULL || actorMgr == NULL) return m_HorseDef->EggGenTicks;
	ClientPlayer *owner = actorMgr->findPlayerByUin(m_BindUIN);
	if(owner == NULL) return m_HorseDef->EggGenTicks;
	else return m_HorseDef->EggGenTicks -  owner->getAccountHorseLiveAge(m_HorseDef->ID);
}

bool ActorHorse::isTriggerSkillCharge()
{
	if (m_HorseDef && (m_HorseDef->ID == 3455 || m_HorseDef->ID == 3456 || m_HorseDef->ID == 3494)) //猫咪 //魔炎 牛魔王
	{
		if (getChargeProgress() >= TRIGGER_SKILL_CHAGER_PRO)
			return true;
	}

	return false;
}

void ActorHorse::onCheckAccountHorseExpireTime(std::string tips)
{
	if (!m_pWorld || !m_pWorld->getActorMgr())
		return;

	//时限到了下马
	ClientPlayer *player = dynamic_cast<ClientPlayer *>(getRiddenByActor());
	if (player)
	{
		player->mountActor(NULL, true);
		player->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 0, 0, tips.c_str());
	}

	//清除掉坐骑
	setNeedClear();
}

bool ActorHorse::interact(ClientActor *player, bool onshift/* =false */, bool isMobile)
{
	auto pTempPlayer = dynamic_cast<ClientPlayer*>(player);
	if (nullptr != pTempPlayer)
	{
		if (m_BindUIN > 0 && pTempPlayer->getUin() != m_BindUIN)
		{
			return false;
		}

		auto RidComp = sureRiddenComponent();
		int toolid = pTempPlayer->getCurToolID();

		// 虚空生物相关
		bool condition1 = m_Def->ID == 3247; //虚空小鹿
		bool bCanUse = false;
		bool condition2 = VacantComponent::GetInteractVacantEnergy(this, toolid, bCanUse) > 0;
		if (condition1 || condition2)
		{
			return ClientMob::interact(pTempPlayer);
		}

		if (isBreedItem(toolid) > 0 || m_Def->ID == 3407 || m_Def->ID == 3912)
		{
			return ClientMob::interact(pTempPlayer);
		}
		else if (/*m_RiddenByActor == 0*/RidComp && RidComp->checkRiddenByActorObjId(0))
		{
			const ToolDef *def = GetDefManagerProxy()->getToolDef(toolid);
			MobAttrib *attr = static_cast<MobAttrib *>(getAttrib());
			int nEquipItem = attr->getEquipItemWithType(EQUIP_HEAD);

			if (def && (def->Type == 19 || def->Type == 20))
			{
				BackPackGrid *curgrid = pTempPlayer->getBackPack()->index2Grid(pTempPlayer->getCurShortcut() + pTempPlayer->getBackPack()->getShortcutStartIndex());

				equipSaddle(*curgrid);
				pTempPlayer->getBackPack()->afterChangeGrid(curgrid->getIndex());
				setRakeToolLiving(false);
				//setCanMove(false);

				return true;
			}
			//驯服的牛 铁耙,钛耙,钻耙变成耙工具生物
			else if (def && def->Type == 4 && (ActorHorse::mobCanRake(m_Def->ID)))
			{
				BackPackGrid *curgrid = pTempPlayer->getBackPack()->index2Grid(pTempPlayer->getCurShortcut() + pTempPlayer->getBackPack()->getShortcutStartIndex());

				equipRake(*curgrid);
				pTempPlayer->getBackPack()->afterChangeGrid(curgrid->getIndex());
				setRakeToolLiving(nEquipItem > 0 ? false : true);
				//setCanMove(nEquipItem > 0 ? false : true);

				return true;
			}
			else if (m_bRakeToolLiving && (ActorHorse::mobCanRake(m_Def->ID)))
			{
				auto functionWrapper = getFuncWrapper();
				if (functionWrapper)
				{
					functionWrapper->setCanMove(!functionWrapper->getCanMove());
				}

				return true;
			}
			else if (m_EquipGrids[0].isEmpty()) return false;
			else return ActorContainerMob::interact(pTempPlayer);
		}
		else
		{
			return false;
		}

	}
	else
	{
		return false;
	}
}

float ActorHorse::getFallHurtRate()
{
	int hurtRate = -1;
	for (size_t i = 0; i < m_skillComList.size(); i++)
	{
		HorseSkillComponent* skill = m_skillComList[i];
		float rate = skill->getFallHurtRate();
		if (rate >= 0 && (hurtRate == -1 || rate < hurtRate))
			hurtRate = rate;
	}

	if (hurtRate >= 0)
		return hurtRate;

	if (hasHorseSkill(HORSE_SKILL_MUTATE_FLY) || hasHorseSkill(HORSE_SKILL_FLOATAGE) || hasHorseSkill(HORSE_SKILL_AIR_ALERT_FLOATAGE))
	{
		return 0.5f;
	}
	else if (hasHorseSkill(HORSE_SKILL_FLY) || hasHorseSkill(HORSE_SKILL_SWIFT_WINGS) || hasHorseSkill(HORSE_SKILL_MOONRISE) || hasHorseSkill(HORSE_SKILL_SLEIGH_FLY)
		|| hasHorseSkill(HORSE_SKILL_FLYINGFLOWER) || hasHorseSkill(HORSE_SKILL_GLANCE) || hasHorseSkill(HORSE_SKILL_SWAN_FLY) || hasHorseSkill(HORSE_SKILL_PLANE_FLY1)
		|| hasHorseSkill(HORSE_SKILL_PLANE_FLY2) || hasHorseSkill(HORSE_SKILL_WIND_WING)					// 20210716：增加新技能  codeby： keguanqiang
		|| hasHorseSkill(HORSE_SKILL_FUYAO))				// ********：扶摇技能不受到摔落伤害  codeby： keguanqiang
	{
		return 0.2f;
	}
	else if (hasHorseSkill(HORSE_SKILL_GIANTWHALE_FLAY))
	{
		return .0f;
	}
	else
	{
		return 1.0f;
	}
}

float ActorHorse::getBuffAttrValues(const BuffDef *buffDef, MODATTRIB_TYPE type){
	float attrValue = 0;
	size_t num = sizeof(buffDef->AttrTypes) / sizeof(buffDef->AttrTypes[0]);
	for (size_t i = 0; i < num; i++)
	{
		MODATTRIB_TYPE typeTmp = buffDef->AttrTypes[i];
		if (type == typeTmp) {
			attrValue = buffDef->AttrValues[i];
		}
	}
	return attrValue;
}
void ActorHorse::tick()
{
	ClientMob::tick();

	for (int i = 0; i < (sizeof(m_fSkillCD) / sizeof(float)); i++)
	{
		m_fSkillCD[i] -= 1.0f / 20.0f;
		if (m_fSkillCD[i] < 0)
			m_fSkillCD[i] = 0;
	}

	if (m_iCheckAccountHorseCD > 0)
	{
		m_iCheckAccountHorseCD--;
		if (m_iCheckAccountHorseCD <= 0)
		{
			m_iCheckAccountHorseCD = CHECK_ACCOUNT_HORSE_CD;
			int owerUin = getAccoutHorseOwnerUin();
			if (m_Def && owerUin > 0)
			{
				std::string objId = to_string(getObjId());
				MINIW::ScriptVM::game()->callFunction("CheckAccountHorseExpireTime", "sii", objId.c_str(), m_Def->ID, owerUin);
			}
		}
	}

	if (m_iPreJumpSkill > 0) //********：检测清除前置跳跃逻辑  codeby： keguanqiang
	{
		m_iPreJumpSkill--;
		if (m_iPreJumpSkill == 0 && getLocoMotion() && getLocoMotion()->m_OnGround)
			m_iPreJumpSkill = -1;
	}


	//新增脚本支持代码
	bool isEnd = false;
	for (size_t i = 0; i < m_skillComList.size(); i++)
	{
		HorseSkillComponent* skill = m_skillComList[i];
		isEnd = skill->tick() || isEnd;
	}
	if (isEnd) return;

	bool bCanRush = false;
	bool bCanFloat = hasWaterSkill(1);
	bool isDiving = false;
	bool bPlayEffect = false;
	if (isUseSwimSpeed()) {
		if (getLocoMotion() && getLocoMotion()->m_InWater) {
			bCanRush = hasWaterSkill(4);
			if (bCanRush) {
				auto RidComp = getRiddenComponent();
				if (m_bChangeRide || (!m_bPreInWater && RidComp && RidComp->isRidden())) {
					PlayerControl* riddenby = dynamic_cast<PlayerControl *>(getRiddenByActor());
					if (riddenby == g_pPlayerCtrl) {
						m_bChangeRide = false;
						m_bPreInWater = true;
						//ge GetGameEventQue().postEventResult("GIE_RIDING_ENTERWATERORLAND", 1);
						MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).SetData_Number("result", 1);
						if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
						{
							MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GIE_RIDING_ENTERWATERORLAND", sandboxContext);
						}
					}
				}
			}

			if (!m_bDoingRush && getLocoMotion()->getOwnerActor() && hasWaterSkill(2)) {
				ActorLiving* riddenby = dynamic_cast<ActorLiving *>(getRiddenByActor());
				if (riddenby && riddenby->getAttrib()) {
					bPlayEffect = (abs(getLocoMotion()->m_Motion.x) > 0.01f || abs(getLocoMotion()->m_Motion.z) > 0.01f);
					auto pAttrib = dynamic_cast<LivingAttrib *>(riddenby->getAttrib());
					if (pAttrib && riddenby->getLocoMotion()->isInsideNoOxygenBlock()) {
						int displayoxygen = (int)pAttrib->getOxygen();
						if (displayoxygen >= 0 && bPlayEffect) {
							isDiving = true;
							if (getBody() && getBody()->getCurAnim(0) != SEQ_SWIM_DIVING) {
								getBody()->setNeedUpdateAnim(false);
								getBody()->setCurAnim(SEQ_SWIM_DIVING, 0);
								m_bDoingDiving = true;
							}
						}
					}

					if (!bPlayEffect) {
						if (bCanRush && getBody() && getBody()->getCurAnim(0) != SEQ_SWIM_IDLE) {
							getBody()->setNeedUpdateAnim(false);
							getBody()->setCurAnim(SEQ_SWIM_IDLE, 0);
						}
					}
					else if (!isDiving) {
						if (bCanRush && getBody() && getBody()->getCurAnim(0) == SEQ_SWIM_IDLE) {
							getBody()->setNeedUpdateAnim(true);
						}
					}
				}
			}
			else if (bCanFloat) {
				bPlayEffect = (abs(getLocoMotion()->m_Motion.x) > 0.01f || abs(getLocoMotion()->m_Motion.z) > 0.01f);
			}
		}
		else if(hasWaterSkill(4)){
			m_bChangeRide = false;
			if (m_bPreInWater) {
				m_CurCharge = -1;
				m_bPreInWater = false;
				if(getBody())
					getBody()->setNeedUpdateAnim(true);
				//ge GetGameEventQue().postEventResult("GIE_RIDING_ENTERWATERORLAND", 0);
				MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).SetData_Number("result", 0);
				if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
				{
					MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GIE_RIDING_ENTERWATERORLAND", sandboxContext);
				}
			}
		}
	}

	if (!isDiving && m_bDoingDiving) {
		m_bDoingDiving = false;
		if (getBody())
			getBody()->setNeedUpdateAnim(true);
	}

	if (bPlayEffect && !m_bPlayDivingEff) {
		m_bPlayDivingEff = true;
		if (bCanFloat) 
		{
			auto effectComponent = getEffectComponent();
			if (effectComponent)
			{
				effectComponent->playBodyEffect("horse_3445_sealsspray");
			}
		}
		else 
		{
			auto effectComponent = getEffectComponent();
			if (effectComponent)
			{
				effectComponent->playBodyEffect("horse_3447_sealse");
			}
		}
	}
	else if(!bPlayEffect && m_bPlayDivingEff){
		m_bPlayDivingEff = false;
		if (bCanFloat) 
		{
			auto effectComponent = getEffectComponent();
			if (effectComponent)
			{
				effectComponent->stopBodyEffect("horse_3445_sealsspray");
			}
		}
		else
		{
			auto effectComponent = getEffectComponent();
			if (effectComponent)
			{
				effectComponent->stopBodyEffect("horse_3447_sealse");
			}
		}
	}


	if (hasHorseSkill(HORSE_SKILL_BAMBOO_DRAGONFLY_FLY))//处理脚部可能存在方块的情况
	{
		WCoord pos = getLocoMotion()->getPosition();
		pos.y = CoordDivBlock(pos.y + BLOCK_SIZE / 2) * BLOCK_SIZE;//做个向上取整，针对部分高度不是完全blocksize的方块导致高度不是整百
		CollideAABB box;
		getCollideBox(box);

		int blockid = m_pWorld->getBlockID(CoordDivBlock(pos));
		auto mtl = g_BlockMtlMgr.getMaterial(blockid);
		if (!mtl)
		{
			return;
		}
		const BlockDef* def = mtl->GetBlockDef();//GetDefManagerProxy()->getBlockDef(blockid);
		if (!def)
		{
			return;
		}
		CollideAABB box2;
		box2.setPosDim(CoordDivBlock(pos)*BLOCK_SIZE, WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));

		auto moveCollide = def->MoveCollide;
		if (blockid > 0 && box.intersect(box2) && !m_pWorld->checkNoCollisionBoundBox(box, this) && moveCollide == 1)//脚下有方块，向上移动
		{
			if (def->PhyCollide == 1)
				getLocoMotion()->m_Motion.y = 30;
			else if (def->PhyCollide == 2)
				getLocoMotion()->m_Motion.y = 30;
			else if (def->PhyCollide == 0)
				getLocoMotion()->m_Motion.y = 50;
		}
	}

	if (hasHorseSkill(HORSE_SKILL_BAMBOO_DRAGONFLY_FLY1) || hasHorseSkill(HORSE_SKILL_BAMBOO_DRAGONFLY_FLY2)) //竹蜻蜓飞行
	{
		//地上时候蓄力值反而是满的，恢复到满，飞行时消耗
		float skillvals[7];
		if (getHorseFlySkills(skillvals))
		{
			if (m_iDragonFlyState == 1)
			{
				int charge = MAX_HORSE_CHARGE - 1;
				if (m_pWorld && m_pWorld->isRemoteMode())
				{
					charge -= 4 * skillvals[6]; //客机计算m_OnGround慢，减多4次；
				}

				if (getLocoMotion()->m_OnGround && (m_CurCharge <= charge || m_bDragonFlyIsFly == false))
				{
					m_iDragonFlyState = 0;
					m_bDragonFlyIsFly = false;
				}

				if (m_CurCharge <= 0)
				{
					m_bDragonFlyIsFly = false;
					m_iDragonFlyState = 0;
					m_CurCharge = 0;
					auto effectComponent = getEffectComponent();
					if (effectComponent)
					{
						effectComponent->stopBodyEffect("screw_fly");
					}
				}
				else
				{
					if (m_bDragonFlyIsFly)
						m_CurCharge -= skillvals[6];
				}
				doFloatageSkill();
			}
			else if (m_iDragonFlyState == 0)
			{
				if (getLocoMotion()->m_OnGround)
				{
					if (m_CurCharge > MAX_HORSE_CHARGE)
						m_CurCharge = MAX_HORSE_CHARGE;
					else if (m_CurCharge < MAX_HORSE_CHARGE)
						m_CurCharge += skillvals[5];
				}
			}
			
		}
	}
	else
	{
		if (m_CurCharge >= 0)
		{
			if (m_ChargePeakTicks >= 10)
			{
				m_CurCharge -= MAX_HORSE_CHARGE / (2 * 20);
				if (m_CurCharge < 0) m_CurCharge = 0;
			}
			else
			{
				m_CurCharge += MAX_HORSE_CHARGE / (1 * 20);
				if (m_CurCharge >= MAX_HORSE_CHARGE)
				{
					m_CurCharge = MAX_HORSE_CHARGE;
					m_ChargePeakTicks++;
				}
			}
		}
	}

	if(getFlying())
	{
		if(!hasHorseSkill(HORSE_SKILL_MUTATE_FLY))
		{
			//m_FallDistance = 0;
			auto functionWrapper = getFuncWrapper();
			if (functionWrapper)
			{
				functionWrapper->setFallDistance(0);
			}
		}
		if(getLocoMotion()->m_OnGround)
		{
			setFlying(false);
			auto effectComponent = getEffectComponent();
			if (effectComponent)
			{
				effectComponent->stopBodyEffect(BODYFX_HORSE_FLY);
			}
		}
	}

	if(m_DoingBenteng)
	{
		if(getLocoMotion()->m_OnGround)
		{
			m_DoingBenteng = false;
			auto effectComponent = getEffectComponent();
			if (effectComponent)
			{
				effectComponent->stopBodyEffect(BODYFX_HORSE_BENTENG);
			}
		}
	}

	if (bCanRush) {
		if (m_bDoingRush) {
			m_bTired = false;
			if (m_fEnergy > 0) {
				m_fEnergy -= m_iChargeRushTicks;
				if (m_fEnergy < 0.0f) {
					m_fEnergy = 0.0f;
					m_bDoingRush = false;
				}
				else {
					playAnim(SEQ_SWIM_RUSH);
				}
			}
			else {
				m_bDoingRush = false;
			}
		}
		else {
			m_bTired = false;
			if (m_fEnergy >= 0.0f && m_fEnergy < 100.0f)
			{
				m_bTired = true;
				if (++m_ChargePeakTicks % m_iChargeAddSpeed == 0) {
					m_ChargePeakTicks = 0;
					m_fEnergy += 4.0f;
					if (m_fEnergy > 99.9f)
						m_fEnergy = 100.0f;
				}
			}
		}
	}

	if (getLocoMotion() && !getLocoMotion()->m_OnGround)
	{
		//if(hasHorseSkill(HORSE_SKILL_CAT_PUFF) && m_CanTriggerSkill)
		if ( m_CatSkillState == CatHorseSkillState::CAT_ENOUGH_CHARGE)
			m_CatSkillState = CatHorseSkillState::CAT_CAN_TRIGGER_SKILL;
	}
	else if(m_CatSkillState == CatHorseSkillState::CAT_CAN_TRIGGER_SKILL)
	{
		m_CatSkillState = CatHorseSkillState::CAT_NORMAL;
		//TODO 猫扑 威慑
		doCatSkill();
	}

	if (m_bFloatageing)
	{
		if (getLocoMotion() && getLocoMotion()->m_OnGround && m_fEnergy <= 0)
		{
			m_bFloatageing = false;
			setFlagBit(ACTORFLAG_FLOATAGE, false);
			setHorseFlagBit(HORSE_FLAG::FLYING, false);//飞行	// 20220422：增加飞行状态 codeby： huangrui
			if (!m_pWorld->isRemoteMode())
			{
				stopFlyEffect();
				m_bStopFlySound = true;
			}
		}

		doFloatageSkill();
	}
	else if (getLocoMotion() && getLocoMotion()->m_OnGround && getFlagBit(ACTORFLAG_FLOATAGE))
	{
		setFlagBit(ACTORFLAG_FLOATAGE, false);
		setHorseFlagBit(HORSE_FLAG::FLYING, false);//飞行	// 20220422：增加飞行状态 codeby： huangrui
	}

	if (m_iInvisibleTick > 0 && m_pWorld && !m_pWorld->isRemoteMode())	// ********：检查隐身的时间  codeby： keguanqiang
	{
		m_iInvisibleTick--;
		if (m_iInvisibleTick <= 0)
		{
			breakInvisible();
		}
	}

	if (m_bStopFlySound && checkDecayLoopSound())
	{
		m_bStopFlySound = false;
		playLoopSound(-1, "");
	}

	if (m_iPreFlashTick > 0)
	{
		m_iPreFlashTick--;
		if (m_iPreFlashTick <= 0)
		{
			m_iPreFlashTick = -1;
			doFlash();
		}
	}

	checkBindPlayer(false);

	//飞鸡飞行技能
	if(hasHorseSkill(HORSE_SKILL_MUTATE_FLY))
	{
		float skillvals[7];
		if (getHorseSkill(HORSE_SKILL_MUTATE_FLY, skillvals) && (m_LiveTicks - m_nLastJumpTick > skillvals[3]))
		{
			if (m_bTired)
			{
				m_fEnergy += skillvals[4] * 3;
			}
			else
			{
				m_fEnergy += skillvals[4];
			}
			if (m_fEnergy >= 100)
			{
				m_fEnergy = 100;
				m_bTired = false;
				auto effectComponent = getEffectComponent();
				if (effectComponent)
				{
					effectComponent->stopBodyEffect("mob_3421_tired");
				}
				sendFlyState();
				setFlagBit(ACTORFLAG_AI_TIRED, false);
			}
		}
	}

	auto RidComp = getRiddenComponent();
	if (!m_pWorld->isRemoteMode() && RidComp)
	{
		for(int i=0; i< RidComp->getNumRiddenPos(); i++)
		{
			ClientActor *ridden = getRiddenByActor(i);
			if(ridden)
			{
				auto riddenComp = ridden->getRiddenComponent();
				if (/*ridden->m_RidingActor != getObjId()*/!(riddenComp && riddenComp->checkRidingByActorObjId(getObjId())))
				{
					setRiddenByActor(NULL, i);
				}
				else if (ridden->isDead() || ridden->needClear())
				{
					//if(ridden->m_RidingActor == getObjId())
					if (riddenComp && riddenComp->checkRidingByActorObjId(getObjId()))
					{
						riddenComp->mountActor(NULL);
					}
					RidComp->clearRiddenActor(ridden);
				}
			}
		}

		if(isDead() || needClear())
		{
			//ridden->mountActor(NULL);
			for(int i=0; i< RidComp->getNumRiddenPos(); i++)
			{
				ClientActor *ridden = getRiddenByActor(i);
				if (ridden)
				{
					auto riddenComp = ridden->getRiddenComponent();
					if (riddenComp && /*ridden->m_RidingActor==getObjId()*/riddenComp->checkRidingByActorObjId(getObjId()))
					{
						riddenComp->mountActor(NULL);
					}
				}
			}
		}
		else
		{
			CollideAABB box;
			getCollideBox(box);
			box.expand(BLOCK_SIZE, BLOCK_SIZE/2, BLOCK_SIZE);

			ClientActor *ridden = getRiddenByActor();
			ClientPlayer *player = dynamic_cast<ClientPlayer *>(ridden);
			if(ridden && player)
			{	
				std::vector<IClientActor *>actors;
				m_pWorld->getActorsInBoxExclude(actors, box, this);
				for(size_t i=0; i<actors.size(); i++)
				{		
					ClientActor *actor = actors[i]->GetActor();
					if(!actor->needClear())
					{
						actor->onCollideWithPlayer(player);
					}
				}
			}
		}
	}
	else
	{
		tickByClient();
	}
	
	if (hasHorseSkill(HORSE_SKILL_SHIELD) || hasHorseSkill(HORSE_SKILL_SOLAR_DISK)
		|| hasHorseSkill(HORSE_SKILL_MAGIC_TEARS))			// ********：增加新技能-神奇眼泪  codeby： keguanqiang
	{

		if (m_shieldCoolingTicks > 0) {
			--m_shieldCoolingTicks;
		}

		if (m_shieldCoolingTicks == 0 && !m_aureoleIsShow) {
			if(getBody())
			  m_aureoleIsShow = true;

			if(hasHorseSkill(HORSE_SKILL_SHIELD))
			{
				auto effectComponent = getEffectComponent();
				if (effectComponent)
				{
					effectComponent->playBodyEffect("horse_3460_ready");
				}
			}
			else if (hasHorseSkill(HORSE_SKILL_SOLAR_DISK))
			{
				auto effectComponent = getEffectComponent();
				if (effectComponent)
				{
					effectComponent->playBodyEffect("horse_4502_ready");
				}
			}
			else if (hasHorseSkill(HORSE_SKILL_MAGIC_TEARS))	// ******** 新技能准备特效逻辑  codeby： keguanqiang
			{
				auto effectComponent = getEffectComponent();
				if (effectComponent)
				{
					effectComponent->playBodyEffect("horse_4529_ready");
				}
				auto sound = getSoundComponent();
				if (sound)
				{
					sound->playSound("ent.4527.attack1_2", 1.0f, 1.0f);
				}
			}

		}

		if (!m_shieldIsShow) {
			float skillvals[7];
			int buffid = BECOME_MOUNTS_BUFF;
			if (hasHorseSkill(HORSE_SKILL_SHIELD))
				getHorseSkill(HORSE_SKILL_SHIELD, skillvals);
			else if (hasHorseSkill(HORSE_SKILL_SOLAR_DISK))
			{
				getHorseSkill(HORSE_SKILL_SOLAR_DISK, skillvals);
				buffid = SOLARDISK_MOUNTS_BUFF;
			}
			else if (hasHorseSkill(HORSE_SKILL_MAGIC_TEARS))		// ********：新技能准备特效逻辑  codeby： keguanqiang
			{
				getHorseSkill(HORSE_SKILL_MAGIC_TEARS, skillvals);
			}

			int num = getLivingAttrib()->getBuffNum();
			if (num > 0) {
				for (size_t i = 0; i < (size_t)num; i++)
				{
					ActorBuff shieldBuff = getLivingAttrib()->getBuffInfo(i);
					if (shieldBuff.buffid == buffid) {
						if (shieldBuff.ticks > 0)
						{
							getLivingAttrib()->removeBuff(buffid);
							if (hasHorseSkill(HORSE_SKILL_SHIELD))
							{
								auto effectComponent = getEffectComponent();
								if (effectComponent)
								{
									effectComponent->stopBodyEffect("horse_3460_ready");
								}
							}
							else if (hasHorseSkill(HORSE_SKILL_SOLAR_DISK))
							{
								auto effectComponent = getEffectComponent();
								if (effectComponent)
								{
									effectComponent->stopBodyEffect("horse_4502_ready");
								}
							}
							else if (hasHorseSkill(HORSE_SKILL_MAGIC_TEARS))		// ********：新技能准备特效逻辑  codeby： keguanqiang
							{
								auto effectComponent = getEffectComponent();
								if (effectComponent)
								{
									effectComponent->stopBodyEffect("horse_4529_ready");
								}
							}

							m_shieldLife = (int)getBuffAttrValues(shieldBuff.def, MODATTR_DAMAGE_ABSORB);

							getLivingAttrib()->addBuffOnLoad(shieldBuff.buffid, shieldBuff.bufflv, shieldBuff.ticks);

							m_aureoleIsShow = false;
							m_shieldIsShow = true;
						}
					}
				}
			}
			else {
				const BuffDef *def = NULL;
#if 0
				if (getLivingAttrib()->isNewStatus())
				{
					int iRealStatusId = GetDefManagerProxy()->getRealStatusId(buffid, 1);
					def = GetDefManagerProxy()->getStatusDef(iRealStatusId);
				}
				else
				{
					def = GetDefManagerProxy()->getBuffDef(buffid, 1);
				}
#else
				int iRealStatusId = GetDefManagerProxy()->getRealStatusId(buffid, 1);
				def = GetDefManagerProxy()->getStatusDef(iRealStatusId);
#endif

				int consumeTick = (int)(skillvals[1] * 20 - m_shieldCoolingTicks);
				auto effectComponent = getEffectComponent();
				if (def && def->EffectTicks > consumeTick) {
					getLivingAttrib()->addBuffOnLoad(buffid, 1, def->EffectTicks - consumeTick);
					if (hasHorseSkill(HORSE_SKILL_SHIELD))
					{
						if (effectComponent)
						{
							effectComponent->stopBodyEffect("horse_3460_ready");
						}
					}
					else if (hasHorseSkill(HORSE_SKILL_SOLAR_DISK))
					{
						if (effectComponent)
						{
							effectComponent->stopBodyEffect("horse_4502_ready");
						}
					}
					else if (hasHorseSkill(HORSE_SKILL_MAGIC_TEARS))		// ********：新技能准备特效逻辑  codeby： keguanqiang
					{
						if (effectComponent)
						{
							effectComponent->stopBodyEffect("horse_4529_ready");
						}
					}
					m_aureoleIsShow = false;
					m_shieldIsShow = true;
					m_shieldLife = (int)getBuffAttrValues(def, MODATTR_DAMAGE_ABSORB);
				}
			}
		}
	}

	if(m_bIsFirstLoadOverEntity && getBody() && getBody()->getEntity() /* && getBody()->getEntity()->getRes()*/)
	{
		m_bIsFirstLoadOverEntity = false;
		if(hasHorseSkill(HORSE_SKILL_SLEIGH_FLY))
		{
			auto effectComponent = getEffectComponent();
			if (effectComponent)
			{
				effectComponent->playBodyEffect("horse_3485_2");
				effectComponent->playBodyEffect("horse_3485_3");
			}
		}
	}
}
void ActorHorse::tickByClient()
{
	if (getFlagBit(ACTORFLAG_FLOATAGE) || getHorseFlagBit(HORSE_FLAG::FLYING)) //飞行	// 20220422：增加飞行状态 codeby： huangrui
	{
		if (m_Def && !m_CurPlaySnd)
			playLoopSound(0, m_Def->AttackSound.c_str());
	}
	else if(m_CurPlaySnd)
	{
		m_bStopFlySound = true;
	}

	if (m_bStopFlySound && checkDecayLoopSound())
	{
		m_bStopFlySound = false;
		playLoopSound(-1, "");
	}
}

bool ActorHorse::attackedFrom(OneAttackData &atkdata, ClientActor *attacker /* = NULL */) {

	int isEnd = -1;
	for (size_t i = 0; i < m_skillComList.size(); i++)
	{
		HorseSkillComponent* skill = m_skillComList[i];
		isEnd = skill->attackedFrom(atkdata, attacker);
		if (isEnd == 1)
			return true;
		else if (isEnd == 0)
			return false;			
	}
	if (hasHorseSkill(HORSE_SKILL_SHIELD) || hasHorseSkill(HORSE_SKILL_SOLAR_DISK)
		|| hasHorseSkill(HORSE_SKILL_MAGIC_TEARS))		// ********：增加新技能-神奇眼泪  codeby： keguanqiang
	{
		float skillvals[7];
		int buffid = BECOME_MOUNTS_BUFF;
		if (hasHorseSkill(HORSE_SKILL_SHIELD))
			getHorseSkill(HORSE_SKILL_SHIELD, skillvals);
		else if (hasHorseSkill(HORSE_SKILL_SOLAR_DISK))
		{
			buffid = SOLARDISK_MOUNTS_BUFF;
			getHorseSkill(HORSE_SKILL_SOLAR_DISK, skillvals);
		}
		else if (hasHorseSkill(HORSE_SKILL_MAGIC_TEARS))	// ********：新技能逻辑  codeby： keguanqiang
		{
			getHorseSkill(HORSE_SKILL_MAGIC_TEARS, skillvals);
		}
			

		float ap = atkdata.atkpoints;

		if (atkdata.fromplayer)
		{
			ap += 1.0f * GetLuaInterfaceProxy().get_lua_const()->kongshou_shanghai_beilv; //modify by null  乘以空手伤害倍率
		}

		if (atkdata.atktype != ATTACK_RANGE)
		{
			if (getAttrib()->getHurtResistantTime() > MAX_HURTRESISTANT_TIME / 2)
			{
				if (ap <= getAttrib()->getMaxHurtInResistant())
				{
					return false;
				}

			}
		}

		int iAttackType = ((attacker && attacker->getAttrib()) ? attacker->getAttrib()->getAttackType(atkdata.atktype) : atkdata.atktype);
		if (!getAttrib()->hasImmuneType(getAttrib()->getImmuneTypeByAttackType(iAttackType))) {
			if (m_shieldLife > 0 && getLivingAttrib()->hasBuff(buffid)) {
				if (ap >= m_shieldLife) {
					atkdata.atkpoints -= m_shieldLife;
					m_shieldLife = 0;
				}
				else {
					m_shieldLife -= (int)ap;
					return false;
				}
				if (m_shieldLife <= 0) {
					getLivingAttrib()->removeBuff(buffid);
				}
			}

		}
	}
	if (atkdata.atktype != ATTACK_SUN && isInvulnerable(attacker))
	{
		return false;
	}
	if (ActorLiving::attackedFrom(atkdata, attacker))
	{
		setAISitting(false);
		//m_InLove = 0;
		setInLove(0);
		//m_InQuite = 0;
		setQuiteTick(0);

		if (m_Def->ID == 3123 && attacker && attacker->getObjType() == OBJ_TYPE_ROLE) //外星人 被玩家打才会掉
		{
			static int dropitems[] = { 12572, 12573, 12574, 12565, 12569, 12570, 12571 };
			if (GenRandomInt(100) < 20)
			{
				int n = sizeof(dropitems) / sizeof(int);
				int itemid = dropitems[GenRandomInt(n)];
				auto dropComponent = GetComponent<DropItemComponent>();
				if (dropComponent)
				{
					dropComponent->dropItem(itemid, 1);
				}
			}
		}

		auto RidComp = getRiddenComponent();
		if (RidComp && RidComp->isRiding())
		{
			if (isDead()) mountActor_mob(NULL);
		}



		// 20210917: 坐骑受击打断隐身 codeby： keguanqiang
		if (isInvisible())
			breakInvisible();

		return true;
	}
	else {
		return false;
	}
}

void ActorHorse::update(float dtime)
{
	//ActorLiving::update(dtime);
	ClientMob::update(dtime);

	bool isEnd = false;
	for (size_t i = 0; i < m_skillComList.size(); i++)
	{
		HorseSkillComponent* skill = m_skillComList[i];
		isEnd = skill->update(dtime) || isEnd;
	}
	if (isEnd) return;


	if (m_bFloatageing && getBody() && getBody()->getEntity() && getLocoMotion())
	{
		auto *pEntity = getBody()->getEntity();
		Rainbow::WorldPos pos = getLocoMotion()->getFramePosition();
		pEntity->SetPosition(pos);

		float t = getLocoMotion()->m_TickPosition.m_TickOffsetTime / GAME_TICK_TIME;
		float yaw = Rainbow::Lerp(getLocoMotion()->m_PrevRotateYaw, getLocoMotion()->m_RotateYaw, t);
		float pitch = Rainbow::Lerp(getLocoMotion()->m_PrevRotatePitch, getLocoMotion()->m_RotationPitch, t);

		pEntity->SetRotation(yaw, -pitch, 0.0f);
	}

	if (hasHorseSkill(HORSE_SKILL_BAMBOO_DRAGONFLY_FLY))
	{
		ClientActor *ridden = getRiddenByActor();
		if (ridden && ridden->getBody())
		{
			auto *pEntity = getBody()->getEntity();
			Rainbow::WorldPos pos = getLocoMotion()->getFramePosition();
			pos.y += m_fDragonFlyBoneHeight * Rainbow::WorldPos::UNIT;
			//if (ridden->getLocoMotion())
			//	pos.y += ridden->getLocoMotion()->m_BoundHeight * Rainbow::WorldPos::UNIT;
			pEntity->SetPosition(pos);
		}
	}
}

void ActorHorse::checkBindPlayer(bool isinit)
{
	if(m_pWorld->isRemoteMode() || !m_HorseDef) return;

	bool isEnd = false;
	for (size_t i = 0; i < m_skillComList.size(); i++)
	{
		HorseSkillComponent* skill = m_skillComList[i];
		isEnd = skill->checkBindPlayer(isinit) || isEnd;
	}
	if (isEnd) return;

	if(m_BindUIN <= 0)
	{
		int mobid = m_Def->ID;
		if(mobid==3431 || mobid==3432 || mobid==3434 || mobid==3435 || mobid==3437 || mobid==3438 || mobid==3440 || mobid==3441) //商店坐骑不可能没有bind uin
		{
			setNeedClear();
		}
		return;
	}
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(m_pWorld->getActorMgr());
	if (!actorMgr) return ;
	ClientPlayer *owner = actorMgr->findPlayerByUin(m_BindUIN);
	if(owner==NULL || owner->getSquareDistToActor(this)>SECTION_SIZE*SECTION_SIZE)
	{
		m_CheckUINTicks++;
		if(m_CheckUINTicks > 20)
		{
			setNeedClear();
		}
	}
	else
	{
		m_CheckUINTicks = 0;

		if(isinit)
		{
			owner->updateAccountHorse(m_HorseDef->ID, getAttrib()->getHP(), 0,m_shieldCoolingTicks);
			if(hasHorseSkill(HORSE_SKILL_BREED))
			{
				int eggflag = owner->getAccountHorseLiveAge(m_HorseDef->ID) >= m_HorseDef->EggGenTicks ? 0 : 1;
				owner->notifyGameInfo2Self(PLAYER_NOTIFYINFO_HORSEEGG, eggflag);
			}
		}
		else
		{
			int oldage = owner->getAccountHorseLiveAge(m_HorseDef->ID);
			owner->updateAccountHorse(m_HorseDef->ID, getAttrib()->getHP(), 1, m_shieldCoolingTicks);
			int newage = owner->getAccountHorseLiveAge(m_HorseDef->ID);

			if(hasHorseSkill(HORSE_SKILL_BREED))
			{
				if(oldage<m_HorseDef->EggGenTicks && newage>=m_HorseDef->EggGenTicks)
				{
					owner->notifyGameInfo2Self(PLAYER_NOTIFYINFO_HORSEEGG, 0);
				}
				else if(oldage>=m_HorseDef->EggGenTicks && newage<m_HorseDef->EggGenTicks)
				{
					owner->notifyGameInfo2Self(PLAYER_NOTIFYINFO_HORSEEGG, 1);
				}
			}
		}
	}
}

bool ActorHorse::checkDecayLoopSound()
{
	if (!m_CurPlaySnd)
		return true;

	if (hasHorseSkill(HORSE_SKILL_PLANE_FLY1) || hasHorseSkill(HORSE_SKILL_PLANE_FLY2))
	{
		float curVal = m_CurPlaySnd->getVolume();
		if (curVal <= 0)
			return true;

		m_CurPlaySnd->setVolume(curVal - 0.03);
		return false;
	}

	return true;
}

void ActorHorse::startCharge()
{
	if (!CanDriver()) {
		return;
	}
	bool isEnd = false;
	for (size_t i = 0; i < m_skillComList.size(); i++)
	{
		HorseSkillComponent* skill = m_skillComList[i];
		isEnd = skill->startCharge() || isEnd;
	}
	if (isEnd) return;

	if(getMoveMode() == ACTORMOVE_JUMP && !hasHorseSkill(HORSE_SKILL_MUTATE_FLY))
	{
		getLocoMotion()->setJumping(true);
		static_cast<HorseLocomotion *>(getLocoMotion())->m_StartJumpTicks = 5;
		return;
	}

 	if (hasHorseSkill(HORSE_SKILL_FLOLIGHT_FEATHER) && m_fSkillCD[0] <= 0)
	{
		float skillvals[7];
		getHorseSkill(HORSE_SKILL_FLOLIGHT_FEATHER, skillvals);
		m_fSkillCD[0] = skillvals[0];

		if (m_pWorld && !m_pWorld->isRemoteMode())
		{
			auto effectComponent = getEffectComponent();
			if (effectComponent)
			{
				effectComponent->playBodyEffect("swan_fly");
			}
			auto sound = getSoundComponent();
			if (sound)
			{
				sound->playSound("ent.4515.attack2", 1.0f, 1.0f);
			}

			syncSkillCD2Client(0, m_fSkillCD[0]);
		}
	}
	else if (hasHorseSkill(HORSE_SKILL_CYCLONE))	// 20210710：增加新技能  codeby： keguanqiang
	{
		if (m_fSkillCD[0] <= 0)
		{
			float skillvals[7];
			getHorseSkill(HORSE_SKILL_CYCLONE, skillvals);
			m_fSkillCD[0] = skillvals[0];

			if (m_pWorld && !m_pWorld->isRemoteMode())
			{
				if (m_pWorld->getEffectMgr() && getLocoMotion())
				{
					Rainbow::Vector3f dir = getLocoMotion()->getLookDir();
					dir  = MINIW::Normalize(dir);
					dir *= (BLOCK_SIZE * -3);
					WCoord pt = getPosition() + dir;
					m_pWorld->getEffectMgr()->playParticleEffectAsync("particles/tornado1.ent", pt, 40);
				}
				auto sound = getSoundComponent();
				if (sound)
				{
					sound->playSound("ent.4523.attack1", 1.0f, 1.0f);
				}
				syncSkillCD2Client(0, m_fSkillCD[0]);
			}
		}
		else
		{
			ClientPlayer *player = dynamic_cast<ClientPlayer *>(getRiddenByActor());
			if (player && player->hasUIControl())
			{
				auto sound = getSoundComponent();
				if (m_Def && sound)
				{
					sound->playSound(m_Def->AttackStopSound.c_str(), 1, 1, 1);
				}
			}
		}
	}
	else if (hasHorseSkill(HORSE_SKILL_MAGIC_BUBBLES))	// 20210715：增加新技能  codeby： keguanqiang
	{
		if (m_fSkillCD[0] <= 0)
		{
			float skillvals[7];
			getHorseSkill(HORSE_SKILL_MAGIC_BUBBLES, skillvals);
			m_fSkillCD[0] = skillvals[0];

			if (m_pWorld && !m_pWorld->isRemoteMode())
			{
				auto effectComponent = getEffectComponent();
				if (effectComponent)
				{
					effectComponent->playBodyEffect("bubbling");
				}
				auto sound = getSoundComponent();
				if (sound)
				{
					sound->playSound("ent.4526.attack1", 1.0f, 1.0f);
				}
				syncSkillCD2Client(0, m_fSkillCD[0]);
			}
		}
		/*else
		{
			ClientPlayer *player = dynamic_cast<ClientPlayer *>(getRiddenByActor());
			if (player && player->hasUIControl())
			{
				playSound(m_Def->AttackStopSound, 1, 1, 1);
			}
		}*/
	}
	else if (hasHorseSkill(HORSE_SKILL_SAIL) && m_fSkillCD[0] <= 0)	// 20210818：增加新技能-扬帆  codeby： keguanqiang
	{
		float skillvals[7] = { 0.0f };
		if (getHorseSkill(HORSE_SKILL_SAIL, skillvals))
		{
			m_fSkillCD[0] = skillvals[1];

			if (m_pWorld && !m_pWorld->isRemoteMode() && getLivingAttrib())
			{
				int buffid = skillvals[0] / 1000;
				int lv = skillvals[0] - buffid * 1000;
				getLivingAttrib()->addBuff(buffid, lv);

				syncSkillCD2Client(0, m_fSkillCD[0]);
			}
		}
	}
	else if ( (hasHorseSkill(HORSE_SKILL_SUMMONLV1) || hasHorseSkill(HORSE_SKILL_SUMMONLV2) || hasHorseSkill(HORSE_SKILL_SUMMONLV3))	// ********：新技能-召唤  codeby： keguanqiang
		&& m_fSkillCD[0] <= 0)	
	{
		float skillvals[7];
		std::string effectName = "";
		if (hasHorseSkill(HORSE_SKILL_SUMMONLV1))
		{
			getHorseSkill(HORSE_SKILL_SUMMONLV1, skillvals);
			effectName = "carpet81";
		}

		else if (hasHorseSkill(HORSE_SKILL_SUMMONLV2))
		{
			getHorseSkill(HORSE_SKILL_SUMMONLV2, skillvals);
			effectName = "carpet82";
		}
		else if (hasHorseSkill(HORSE_SKILL_SUMMONLV3))
		{
			getHorseSkill(HORSE_SKILL_SUMMONLV3, skillvals);
			effectName = "carpet83";
		}


		m_fSkillCD[0] = skillvals[0];

		if (m_pWorld && !m_pWorld->isRemoteMode())
		{
			auto effectComponent = getEffectComponent();
			if (effectComponent)
			{
				effectComponent->playBodyEffect(effectName.c_str());
			}
			if (m_Def)
			{
				auto sound = getSoundComponent();
				if (sound)
				{
					sound->playSound(m_Def->AttackSound.c_str(), 1, 1);
					sound->playSound(m_Def->AttackSound2.c_str(), 1, 1);
				}
			}

			syncSkillCD2Client(0, m_fSkillCD[0]);
		}
	}

	if (hasHorseSkill(HORSE_SKILL_INVISIBLE) && m_fSkillCD[1] <= 0)	// ********：隐身技能触发  codeby： keguanqiang
	{
		float skillvals[7];
		getHorseSkill(HORSE_SKILL_INVISIBLE, skillvals);


		if (m_iPreJumpSkill >= 0)  //前置跳跃	
		{
			if (getLocoMotion() && !getLocoMotion()->m_OnGround)
			{
				m_fSkillCD[1] = skillvals[0];
				m_iInvisibleTick = skillvals[1] * 20;

				if (m_pWorld && !m_pWorld->isRemoteMode())
				{
					startInvisible();
					syncSkillCD2Client(1, m_fSkillCD[1]);
				}
			}

			m_iPreJumpSkill = -1;
		}
	}

	// ********：扶摇技能触发  codeby： keguanqiang
	if (hasHorseSkill(HORSE_SKILL_FUYAO) && !m_bFloatageing)
	{
		float skillvals[7];
		getHorseSkill(HORSE_SKILL_FUYAO, skillvals);
		if (m_iPreJumpSkill >= 0)  //前置跳跃
		{
			if (getLocoMotion() && !getLocoMotion()->m_OnGround)
			{
				m_fEnergy = 20 * skillvals[2];
				m_bFloatageing = true;

				if (m_pWorld && !m_pWorld->isRemoteMode())
				{
					playFlyEffect();
					setFlagBit(ACTORFLAG_FLOATAGE, true);
					setHorseFlagBit(HORSE_FLAG::FLYING, true);//飞行	// 20220422：增加飞行状态 codeby： huangrui
					if (m_Def)
						playLoopSound(0, m_Def->AttackSound.c_str());
				}
			}

			m_iPreJumpSkill = -1;
		}
	}


	if (hasHorseSkill(HORSE_SKILL_BAMBOO_DRAGONFLY_FLY1) || hasHorseSkill(HORSE_SKILL_BAMBOO_DRAGONFLY_FLY2))
	{
		if (m_iDragonFlyState == 0 && m_CurCharge == MAX_HORSE_CHARGE)//竹蜻蜓起飞
		{
			m_iDragonFlyState = 1;
			float skillvals[7];
			getHorseFlySkills(skillvals);
			m_iDragonFlyHeight = getPosition().y + skillvals[4];
			m_bDragonFlyIsFly = true;
			auto effectComponent = getEffectComponent();
			if (effectComponent)
			{
				effectComponent->playBodyEffect("screw_fly");
			}
			playLoopSound(0, "ent.4510.attack1");

			//playAnim(SEQ_FLY);
		}
		else if (m_iDragonFlyState == 1)
		{
			if (m_bDragonFlyIsFly == false)
			{
				if (getPosition().y < m_iDragonFlyHeight)
					m_iDragonFlyHeight = getPosition().y;
				m_bDragonFlyIsFly = true;
				auto effectComponent = getEffectComponent();
				if (effectComponent)
				{
					effectComponent->playBodyEffect("screw_fly");
				}
				playLoopSound(0, "ent.4510.attack1");

				//playAnim(SEQ_FLY);
			}
		}
		return;
	}
	else if (hasHorseSkill(HORSE_SKILL_SWAN_FLY))
	{
		if (isUseSwimSpeed() && isDiving())
		{
			m_CurCharge = -1;
			return;
		}
		else
		{
			auto effectComponent = getEffectComponent();
			if (effectComponent)
			{
				effectComponent->playBodyEffect("mob_3421_jump");
			}
		}
	}
	else if(!getLocoMotion()->m_OnGround && !hasHorseSkill(HORSE_SKILL_MUTATE_FLY))
	{
		float skillvals[7];
		if(!getFlying() && hasHorseSkill(HORSE_SKILL_GLIDE))
		{
			setFlying(true);
			auto effectComponent = getEffectComponent();
			if (effectComponent)
			{
				effectComponent->playBodyEffect(BODYFX_HORSE_FLY);
			}
		}
		else if(!m_DoingBenteng && getHorseSkill(HORSE_SKILL_BENTENG, skillvals))
		{
			ClientActor *ridden = getRiddenByActor();
			if(ridden)
			{
				Rainbow::Vector3f dir;
				PitchYaw2Direction(dir, ridden->getLocoMotion()->m_RotateYaw, ridden->getLocoMotion()->m_RotationPitch);
				getLocoMotion()->m_Motion += dir*skillvals[0];

				auto effectComponent = getEffectComponent();
				if (effectComponent)
				{
					effectComponent->playBodyEffect(BODYFX_HORSE_BENTENG);
				}
				m_pWorld->getEffectMgr()->playSoundAtActor(this, "ent.3436.jump", 1.0f, 1.0f);

				m_DoingBenteng = true;
			}
		}
		else if (!m_bDoingRush && m_fEnergy > 99.9f && getHorseSkill(HORSE_SKILL_WATER_RUSH, skillvals)) {
			ClientActor* ridden = getRiddenByActor();
			if (ridden) {
				Rainbow::Vector3f dir;
				float fPitch = ridden->getLocoMotion()->m_RotationPitch;
				if (fPitch < -20.0f) { fPitch = -20.0f; }
				fPitch = (fPitch < -3.0f || fPitch > 25.0f) ? fPitch : 0;
				PitchYaw2Direction(dir, ridden->getLocoMotion()->m_RotateYaw, fPitch);
				getLocoMotion()->m_Motion += dir*skillvals[0];

				m_bTired = false;
				m_fEnergy = 100.0f;
				m_ChargePeakTicks = 0;
				int val = (int)skillvals[1];
				int iBarVal = (int)m_fEnergy;
				m_iChargeRushTicks = val <= 0 ? 50 : val;
				val = iBarVal / m_iChargeRushTicks;
				m_iChargeRushTicks = iBarVal%m_iChargeRushTicks > 0 ? (val+1) : val;
				m_bDoingRush = true;
				auto effectComponent = getEffectComponent();
				if (effectComponent)
				{
					effectComponent->playBodyEffect("horse_3447_sealsex");
				}
				auto sound = getSoundComponent();
				if (sound)
				{
					sound->playSound("ent.3445.jump", 1.0f, 1.0f);
				}
			}
		}
		return;
	}
	else if (hasHorseSkill(HORSE_SKILL_MUTATE_FLY))
	{
		float skillvals[7];
		if (getHorseSkill(HORSE_SKILL_MUTATE_FLY, skillvals) && (m_LiveTicks - m_nLastJumpTick) > skillvals[0])
		{
			ClientActor *ridden = getRiddenByActor();
			if (m_bTired)
			{
				return;
			}
			if(ridden)
			{
				setFlying(true);
				getLocoMotion()->m_Motion.y = skillvals[1];
				m_fEnergy -= skillvals[2];
				if (m_fEnergy < 0)
				{
					auto effectComponent = getEffectComponent();
					if (effectComponent)
					{
						effectComponent->playBodyEffect("mob_3421_tired");
					}
					auto sound = getSoundComponent();
					if (sound)
					{
						sound->playSound("ent.3421.tired", 1.0f, 1.0f);
					}
					m_fEnergy = 0;
					m_bTired = true;
					sendFlyState();
					setFlagBit(ACTORFLAG_AI_TIRED, true);
				}
				playAnim(SEQ_JUMP);
				auto effectComponent = getEffectComponent();
				if (effectComponent)
				{
					effectComponent->playBodyEffect("mob_3421_jump");
				}
			}
			m_nLastJumpTick = m_LiveTicks;
		}
		m_CurCharge = -1;
		return;
	}
	else if (hasHorseSkill(HORSE_SKILL_GIANTWHALE_FLAY))
	{
		if (isUseSwimSpeed() && isDiving())
		{
			m_CurCharge = -1;
			return;
		}
		else
		{
			playAnim(SEQ_FORTUNEMOOSKILL);
		}
	}

	m_CurCharge = 0;
	m_ChargePeakTicks = 0;
}

bool ActorHorse::getHorseFlySkills(float *skillvals /*=NULL*/)
{
	bool ret = false;
	if (hasHorseSkill(HORSE_SKILL_FLY))
	{
		ret = getHorseSkill(HORSE_SKILL_FLY, skillvals);
	}
	else if(hasHorseSkill(HORSE_SKILL_FLOATAGE))
	{
		ret = getHorseSkill(HORSE_SKILL_FLOATAGE, skillvals);
	}
	else if(hasHorseSkill(HORSE_SKILL_AIR_ALERT_FLOATAGE))
	{
		ret = getHorseSkill(HORSE_SKILL_AIR_ALERT_FLOATAGE, skillvals);
	}
	else if (hasHorseSkill(HORSE_SKILL_SWIFT_WINGS)) {
		ret = getHorseSkill(HORSE_SKILL_SWIFT_WINGS, skillvals);
	}
	else if (hasHorseSkill(HORSE_SKILL_MOONRISE)) {
		ret = getHorseSkill(HORSE_SKILL_MOONRISE, skillvals);
	}
	else if (hasHorseSkill(HORSE_SKILL_SLEIGH_FLY))
	{
		ret = getHorseSkill(HORSE_SKILL_SLEIGH_FLY, skillvals);
	}
	else if (hasHorseSkill(HORSE_SKILL_GIANTWHALE_FLAY))
	{
		ret = getHorseSkill(HORSE_SKILL_GIANTWHALE_FLAY,skillvals);
	}
	else if (hasHorseSkill(HORSE_SKILL_FLYINGFLOWER))
	{
		ret = getHorseSkill(HORSE_SKILL_FLYINGFLOWER,skillvals);
	}
	else if (hasHorseSkill(HORSE_SKILL_GLANCE))
	{
		ret = getHorseSkill(HORSE_SKILL_GLANCE,skillvals);
	}
	else if (hasHorseSkill(HORSE_SKILL_BAMBOO_DRAGONFLY_FLY1))
	{
		ret = getHorseSkill(HORSE_SKILL_BAMBOO_DRAGONFLY_FLY1, skillvals);
	}
	else if (hasHorseSkill(HORSE_SKILL_BAMBOO_DRAGONFLY_FLY2))
	{
		ret = getHorseSkill(HORSE_SKILL_BAMBOO_DRAGONFLY_FLY2, skillvals);
	}
	else if (hasHorseSkill(HORSE_SKILL_SWAN_FLY))
	{
		ret = getHorseSkill(HORSE_SKILL_SWAN_FLY, skillvals);
	}
	else if (hasHorseSkill(HORSE_SKILL_PLANE_FLY1))
	{
		ret = getHorseSkill(HORSE_SKILL_PLANE_FLY1, skillvals);
	}
	else if (hasHorseSkill(HORSE_SKILL_PLANE_FLY2))
	{
		ret = getHorseSkill(HORSE_SKILL_PLANE_FLY2, skillvals);
	}
	else if (hasHorseSkill(HORSE_SKILL_WIND_WING))	// 20210716：增加新技能  codeby： keguanqiang
	{
		ret = getHorseSkill(HORSE_SKILL_WIND_WING, skillvals);
	}
	return ret;
}

void ActorHorse::endCharge()
{
	bool isEnd = false;
	for (size_t i = 0; i < m_skillComList.size(); i++)
	{
		HorseSkillComponent* skill = m_skillComList[i];
		isEnd = skill->endCharge() || isEnd;
	}
	if (isEnd) return;

	if (getFlying())
	{
		setFlying(false);
		auto effectComponent = getEffectComponent();
		if (effectComponent)
		{
			effectComponent->stopBodyEffect(BODYFX_HORSE_FLY);
		}
	}

	if (m_CurCharge < 0) return;

	if (hasHorseSkill(HORSE_SKILL_SHIELD) || hasHorseSkill(HORSE_SKILL_SOLAR_DISK)
		|| hasHorseSkill(HORSE_SKILL_MAGIC_TEARS))		// ********：增加新技能-神奇眼泪  codeby： keguanqiang
	{
		if (m_CurCharge >= 1 && m_CurCharge < 27 && m_shieldCoolingTicks <= 0) {
			int buffid = BECOME_MOUNTS_BUFF;
			if (hasHorseSkill(HORSE_SKILL_SOLAR_DISK))
				buffid = SOLARDISK_MOUNTS_BUFF;

			getLivingAttrib()->addBuff(buffid, 1);
			float skillvals[7];
			if (hasHorseSkill(HORSE_SKILL_SHIELD))
			{
				getHorseSkill(HORSE_SKILL_SHIELD, skillvals);
				auto effectComponent = getEffectComponent();
				if (effectComponent)
				{
					effectComponent->stopBodyEffect("horse_3460_ready");
				}
			}
			else if (hasHorseSkill(HORSE_SKILL_SOLAR_DISK))
			{
				getHorseSkill(HORSE_SKILL_SOLAR_DISK, skillvals);
				auto effectComponent = getEffectComponent();
				if (effectComponent)
				{
					effectComponent->stopBodyEffect("horse_4502_ready");
				}
			}
			else if (hasHorseSkill(HORSE_SKILL_MAGIC_TEARS))
			{
				getHorseSkill(HORSE_SKILL_MAGIC_TEARS, skillvals);
				auto effectComponent = getEffectComponent();
				if (effectComponent)
				{
					effectComponent->stopBodyEffect("horse_4529_ready");
				}
				auto sound = getSoundComponent();
				if (sound)
				{
					sound->playSound("ent.4527.attack1", 1, 1, 6);
				}
			}

			int index = getLivingAttrib()->getBuffIndex(buffid);
			ActorBuff shieldBuff = getLivingAttrib()->getBuffInfo(index);
			m_shieldCoolingTicks = (int)(skillvals[1] * 20);
			m_shieldLife = (int)getBuffAttrValues(shieldBuff.def, MODATTR_DAMAGE_ABSORB);


			m_aureoleIsShow = false;
			m_shieldIsShow = true;
		}
	}
	if (hasHorseSkill(HORSE_SKILL_BAMBOO_DRAGONFLY_FLY1) || hasHorseSkill(HORSE_SKILL_BAMBOO_DRAGONFLY_FLY2))
	{
		m_bDragonFlyIsFly = false;

		auto effectComponent = getEffectComponent();
		if (effectComponent)
		{
			effectComponent->stopBodyEffect("screw_fly");
		}

		playLoopSound(-1, "");

		//playAnim(SEQ_STAND);
		return;
	}



	// ********：前置跳跃技能触发前置条件  codeby： keguanqiang
	if (hasHorseSkill(HORSE_SKILL_INVISIBLE) || hasHorseSkill(HORSE_SKILL_FUYAO))
	{
		if (m_iPreJumpSkill < 0)
			m_iPreJumpSkill = 2;	//前置跳跃延后2个tick后再检测在不在空中
	}


	if (!m_pWorld->isRemoteMode() && getLocoMotion() && getLocoMotion()->m_OnGround)
	{
		float t = getChargeProgress();
		float skillvals[7] = { 0.0f };
		if (getHorseFlySkills(skillvals))
		{
			m_fEnergy = 20 * skillvals[2] * t;
			m_bFloatageing = true;
			playFlyEffect();
			setFlagBit(ACTORFLAG_FLOATAGE, true);
			//if (m_pWorld->getEffectMgr())
				//m_pWorld->getEffectMgr()->playParticleEffect("particles/horse_3455_1.ent", getPosition(), 40);
			if (m_Def)
				playLoopSound(0, m_Def->AttackSound.c_str());
		}
		else
		{
			getLocoMotion()->m_Motion.y = (m_JumpHeight - 40.0f) * t + 40.0f;

			Rainbow::Vector3f fdir = Yaw2FowardDir(getLocoMotion()->m_RotateYaw);
			//骑乘焱焱蟹时向左跳
			if (getDefID() == 3896)
			{
				fdir = -Yaw2StrafingDir(getLocoMotion()->m_RotateYaw);
			}
			float forwardspeed = 20.0f;

			getLocoMotion()->m_Motion.x += fdir.x * forwardspeed * t;
			getLocoMotion()->m_Motion.z += fdir.z * forwardspeed * t;

			if (t >= TRIGGER_SKILL_CHAGER_PRO && (hasHorseSkill(HORSE_SKILL_CAT_PUFF) || hasHorseSkill(HORSE_SKILL_BULL_PUFF)))
			{
				m_CatSkillState = CatHorseSkillState::CAT_ENOUGH_CHARGE;
			}
			playAnim(SEQ_JUMP);
		}
	}

	if (m_pWorld->isRemoteMode() && getLocoMotion() && getLocoMotion()->m_OnGround)
	{
		float skillvals[7] = { 0.0f };
		if (getHorseFlySkills(skillvals))
		{
			float t = getChargeProgress();
			m_fEnergy = 20 * skillvals[2] * t;
			m_bFloatageing = true;
			setFlagBit(ACTORFLAG_FLOATAGE, true);
		}

	}

	if (!m_pWorld->isRemoteMode())	// ********：新技能狂风  codeby： keguanqiang
	{
		if (hasHorseSkill(HORSE_SKILL_KUANGFENG) && m_fSkillCD[0] <= 0)
		{
			float skillvals[7];
			getHorseSkill(HORSE_SKILL_KUANGFENG, skillvals);
			if (m_CurCharge > 66) //大于100*2/3
			{
				m_fSkillCD[0] = skillvals[1];

				doKuangFeng();
				syncSkillCD2Client(0, m_fSkillCD[0]);
			}
		}
	}

	m_CurCharge = -1;
}
void ActorHorse::useSkill()
{
	if (!m_pWorld || m_pWorld->isRemoteMode())
	{
		return;
	}

	bool isEnd = false;
	for (size_t t = 0; t < m_skillComList.size(); t++)
	{
		HorseSkillComponent* skill = m_skillComList[t];
		isEnd = skill->useSkill() || isEnd;
	}
	if (isEnd) return;

	if (hasHorseSkill(HORSE_SKILL_GOD_LIGHT) && m_fSkillCD[0] <= 0)
	{
		float skillvals[7];
		getHorseSkill(HORSE_SKILL_GOD_LIGHT, skillvals);
		int buffid1 = (int)skillvals[0] / 1000;
		int bufflv1 = skillvals[0] - buffid1 * 1000;
		int buffid2 = (int)skillvals[1] / 1000;
		int bufflv2 = skillvals[1] - buffid2 * 1000;
		//getLivingAttrib()->addBuff(buffid1, bufflv1, skillvals[3]*20);
		//getLivingAttrib()->addBuff(buffid2, bufflv2, skillvals[3]*20);

		ClientPlayer *player = dynamic_cast<ClientPlayer *>(getRiddenByActor());

		if (player && player->getLivingAttrib())
		{
			player->getLivingAttrib()->addBuff(buffid1, bufflv1, skillvals[3] * 20);
			player->getLivingAttrib()->addBuff(buffid2, bufflv2, skillvals[3] * 20);
		}

		//playBodyEffect("horse_4503");
		if (m_Def && !m_Def->AttackSound2.empty())
		{
			auto sound = getSoundComponent();
			if (sound)
			{
				sound->playSound(m_Def->AttackSound2.c_str(), 1, 1, 6);
			}
		}

		//playAnim(`);

		m_fSkillCD[0] = skillvals[2];
	}
	else if (hasHorseSkill(HORSE_SKILL_PLANE_FLASH) && m_fSkillCD[0] <= 0)
	{
		float skillvals[7];
		getHorseSkill(HORSE_SKILL_PLANE_FLASH, skillvals);
		m_iPreFlashTick = skillvals[1] * 20;

		m_fSkillCD[0] = skillvals[0];

		if (m_Def && !m_Def->AttackSound2.empty())
		{
			auto sound = getSoundComponent();
			if (sound)
			{
				sound->playSound(m_Def->AttackSound2.c_str(), 1, 1, 6);
			}
		}
		playAnim(SEQ_FLASH);
		auto effectComponent = getEffectComponent();
		if (effectComponent)
		{
			effectComponent->playBodyEffect("flash_on");
		}
		syncSkillCD2Client(0, m_fSkillCD[0]);

		setHorseFlagBit(HORSE_FLAG::FLASHING, true);
	}
}

bool ActorHorse::canUseSkill()
{
	if (hasHorseSkill(HORSE_SKILL_PLANE_FLASH) && m_fSkillCD[0] > 0)
	{
		return false;
	}

	return true;
}

void ActorHorse::logicByCanNotUseSkill()
{
	if (hasHorseSkill(HORSE_SKILL_PLANE_FLASH) && m_fSkillCD[0] > 0)
	{
		auto sound = getSoundComponent();
		if (m_Def && sound)
		{
			sound->playSound(m_Def->AttackStopSound2.c_str(), 1, 1, 1);
		}
	}
}

WORLD_ID ActorHorse::getRiddenByActorID(int i)
{
	if(i == 0)
	{
		auto RidComp = getRiddenComponent();
		if (RidComp)
		{
			return RidComp->getRiddenByActorID_Base(i);
		}
		return 0;
	}
	else
	{
		auto RidComp = getRiddenComponent();
		if (RidComp)
		{
			assert(i < RidComp->getNumRiddenPos());
		}
		if (i < SRC_MAX_RIDDERS)
		{
			return m_OtherRiddens[i - 1];
		}
		else
		{
			int j = i - SRC_MAX_RIDDERS;
			if (j >= 0 && (unsigned int)j < m_ExtendOtherRiddens.size())
			{
				return m_ExtendOtherRiddens[j];
			}
			return 0;
		}
	}
}

void ActorHorse::setRiddenByActorObjId(WORLD_ID objId, int i /* = 0 */)
{
	auto RidComp = getRiddenComponent();
	if (i == 0 && RidComp)
		RidComp->setRiddenByActorObjId_Base(objId, i);
	else
	{
		if (RidComp)
		{
			assert(i < RidComp->getNumRiddenPos());
		}
		m_OtherRiddens[i - 1] = objId;
	}
}

ClientActor *ActorHorse::getRiddenByActor(int i)
{
	WORLD_ID id = getRiddenByActorID(i);
	if (id != 0 && m_pWorld)
	{
		ActorManager* actorMgr = dynamic_cast<ActorManager*>(m_pWorld->getActorMgr());
		if (!actorMgr) return NULL;
		return  actorMgr->findActorByWID(id);
	}

	return NULL;
}

void ActorHorse::setRiddenByActor(ClientActor *p, int i)
{
	auto RidComp = getRiddenComponent();
	if (i == 0 && RidComp) RidComp->setRiddenByActor_Base(p, 0);
	else
	{
		if (RidComp)
		{
			assert(i < RidComp->getNumRiddenPos());
		}
		if (i < SRC_MAX_RIDDERS)
		{
			if (p) m_OtherRiddens[i - 1] = p->getObjId();
			else m_OtherRiddens[i - 1] = 0;
		}
		else
		{
			int j = i - SRC_MAX_RIDDERS;
			if (j >= 0 && (unsigned int)j < m_ExtendOtherRiddens.size())
			{
				if (p) m_ExtendOtherRiddens[j] = p->getObjId();
				else m_ExtendOtherRiddens[j] = 0;
			}
		}
	}

	//by:chenyingming 上坐骑重置坠落高度
	//【【坐骑】坠落的过程中上坐骑，坠落伤害会在下坐骑的时候才触发】
	//https://www.tapd.cn/22897851/bugtrace/bugs/view/1122897851001087824
	if (p)
	{
		auto funcWrapper = p->getFuncWrapper();
		if (funcWrapper)
		{
			funcWrapper->setFallDistance(0);
		}
	}

	bool isEnd = false;
	for (size_t t = 0; t < m_skillComList.size(); t++)
	{
		HorseSkillComponent* skill = m_skillComList[t];
		isEnd = skill->setRiddenByActor(p,i) || isEnd;
	}
	
	// 提到前面，防止提前返回导致下坐骑后坐骑血条不显示 code_by:liya
	setHPProgressDirty();
	if (isEnd) return;

	if (p && hasWaterSkill(4)) {
		m_bChangeRide = true;
	}

	if(p && hasHorseSkill(HORSE_SKILL_SLEIGH_FLY) && m_Def && (m_Def->ID == SANTA_SLEIGH1 || m_Def->ID == SANTA_SLEIGH2))
	{
		auto effectComponent = getEffectComponent();
		if (effectComponent)
		{
			effectComponent->playBodyEffect("horse_3485_1");
		}
		if(m_Def && !m_Def->AttackSound2.empty())
		{
			auto sound = getSoundComponent();
			if (sound)
			{
				sound->playSound(m_Def->AttackSound2.c_str(), 1, 1, 6);
			}
		}
	}

	if (!p && m_bFloatageing)
		m_fEnergy = 0;

	if (hasHorseSkill(HORSE_SKILL_BAMBOO_DRAGONFLY_FLY1) || hasHorseSkill(HORSE_SKILL_BAMBOO_DRAGONFLY_FLY2))
	{
		if (!p)
		{
			playAnim(SEQ_STAND);
			auto effectComponent = getEffectComponent();
			if (effectComponent)
			{
				effectComponent->stopBodyEffect("screw_fly");
			}
			playLoopSound(-1, "");
			m_fDragonFlyBoneHeight = 0;
		}
		else
		{
			m_fDragonFlyBoneHeight = 0;
		}
	}
}

Rainbow::Vector3f ActorHorse::getRiddenBindPos(ClientActor *ridden)
{
	Rainbow::Vector3f _pos(0, 0, 0);
	for (size_t i = 0; i < m_skillComList.size(); i++)
	{
		HorseSkillComponent* skill = m_skillComList[i];
		_pos = skill->getRiddenBindPos(ridden);
		if (_pos.x != 0 || _pos.y != 0 || _pos.z != 0)
			return _pos;
	}

	SandboxResult ret = SandboxEventDispatcherManager::GetGlobalInstance().Emit("GetRiddenBindPos", SandboxContext().SetData_Userdata("ActorHorse", "horse", this).SetData_Userdata("ClientActor", "actor", ridden));
	int x = (int)ret.GetData_Number("x", 0.0);
	int y = (int)ret.GetData_Number("y", 0.0);
	int z = (int)ret.GetData_Number("z", 0.0);
	if (x != 0 || y != 0 || z != 0)
	{
		Rainbow::Vector3f pos(x, y, z);
		return pos;
	}
	auto RidComp = getRiddenComponent();
	if (RidComp && RidComp->getNumRiddenPos() > 1)
	{
		int i = findRiddenIndex(ridden);
		//return m_Model->getAnchorWorldMatrix(200+i).getTranslate();
		if(getBody() && getBody()->getEntity() && getBody()->getEntity()->GetMainModel())
		{
			return getBody()->getBindPointPos(200 + i);
		}
	}

	if (hasHorseSkill(HORSE_SKILL_SINGLE_RIDE)) {
		return getBody()->getBindPointPos(202);
	}

	if (hasHorseSkill(HORSE_SKILL_BAMBOO_DRAGONFLY_FLY))
	{
		if (ridden && ridden->getBody() && RidComp)
		{
			Rainbow::Vector3f pos = RidComp->getRiddenBindPos_Base(ridden);
			if (m_fDragonFlyBoneHeight < 3.0f)
			{
				m_fDragonFlyBoneHeight += 1.0f;//2tick后才计算
				if (ridden->getBody() && getBody() && m_fDragonFlyBoneHeight > 1.0f)
				{
					m_fDragonFlyBoneHeight = ridden->getBody()->getBindPointPos(106).y - pos.y + 35.0f;
					if (m_fDragonFlyBoneHeight < 100.0f)//小于100都是不正常
						m_fDragonFlyBoneHeight = 1.0f;
				}
			}
			// pos.y -= m_fDragonFlyBoneHeight;
			return pos;
		}
	}

	if (RidComp)
	{
		return RidComp->getRiddenBindPos_Base(ridden);
	}
	return Rainbow::Vector3f(0, 0, 0);
}

int ActorHorse::findRiddenIndex(ClientActor *ridden)
{
	if (ridden == NULL)
		return -1;
	auto RidComp = getRiddenComponent();
	int n = 0;
	if (RidComp)
	{
		n = RidComp->getNumRiddenPos();
	}
	for (int i = 0; i < n; i++)
	{
		WORLD_ID riddenByActorID = getRiddenByActorID(i);
		WORLD_ID objectId = ridden->getObjId();
		if (riddenByActorID == objectId)
			return i;
	}

	return -1;
}

int ActorHorse::findEmptyRiddenIndex(int index /* = 0 */)
{
	auto RidComp = getRiddenComponent();
	int n = 0;
	if (RidComp)
	{
		n = RidComp->getNumRiddenPos();
	}
	if (index >= 0 && index < n && getRiddenByActor(index) == NULL)
		return index;
	for (int i = 0; i < n; i++)
	{
		if (getRiddenByActor(i) == NULL) return i;
	}
	return -1;
}

WCoord ActorHorse::getRiderPosition(ClientActor *ridden)
{
	if (getObjType() == OBJ_TYPE_SHAPESHIFT_HORSE)
	{
		return getPosition();
	}
	else
		return getPosition() + WCoord(0, 150, 0);
}

void ActorHorse::collideWithActor(ClientActor *actor)
{
	auto RidComp = getRiddenComponent();
	if (RidComp && RidComp->getNumRiddenPos())
	{
		 for(int i=0; i< RidComp->getNumRiddenPos(); i++)
		{
			ClientActor *ridden = getRiddenByActor(i);
			if (ridden)
			{
				auto riddenComp = ridden->getRiddenComponent();
				if (riddenComp && /*ridden->m_RidingActor == getObjId()*/riddenComp->checkRidingByActorObjId(getObjId()))
				{
					return;
				}
			}
		}
	}
	actor->applyActorCollision(this);
	auto triggerComponent = getTriggerComponent();
	if (triggerComponent)
	{
		triggerComponent->checkCollideOnTrigger(actor);
	}
}

void ActorHorse::onEnterWater()
{
	if (!isUseSwimSpeed()) { return; }
	if (hasWaterSkill(2)) { getLocoMotion()->m_Motion.y = 1.0f; }
	m_pWorld->getEffectMgr()->playSound(getPosition(), "env.splash", 1.0f, 1.0f);
	m_pWorld->getEffectMgr()->playParticleEffectAsync("particles/1024.ent", getPosition(), 100);
	m_pWorld->getEffectMgr()->playParticleEffectAsync("particles/1025.ent", getPosition(), 40);
}

void ActorHorse::sendFlyState()
{
	if(!m_pWorld->isRemoteMode() && hasHorseSkill(HORSE_SKILL_MUTATE_FLY))
	{
		ClientActor *ridden = getRiddenByActor(0);
		if (ridden)
		{
			ClientPlayer *player = dynamic_cast<ClientPlayer *>(ridden);
			if (player)
			{
				PB_HorseFlyStateHC horseFlyStateHC;
				horseFlyStateHC.set_m_fenergy(m_fEnergy);
				horseFlyStateHC.set_m_btired(m_bTired);
				GetGameNetManagerPtr()->sendToClient(player->getUin(), PB_HORSEFLYSTATE_HC, horseFlyStateHC, 0);
			}
		}
	}
}


void ActorHorse::getSkillEffectedActors(std::vector<ClientActor *> &actors, float skillRange)
{
	CollideAABB box;
	getCollideBox(box);
	Rainbow::Vector3f dir = Yaw2FowardDir(getLocoMotion()->m_RotateYaw);
	dir.y = 0.02f;
	WCoord mvec(dir * (skillRange * BLOCK_FSIZE));//skillvals[0]
	Rainbow::Vector3f colnormal;
	float t = m_pWorld->moveBox(box, mvec, colnormal);
	actors.clear();
	int range = (int)(t*mvec.length());
	if (t < 1)	//前方有障碍物的情况下，把范围扩大半格避免贴着障碍物的生物检测不到
		range += 50; 

	getLocoMotion()->getFacedActors(actors, dir, range, 200);
}

int  ActorHorse::checkHasSkill(int skillid1, int skillid2, float* skillvals)
{
	int skillId = -1;
	if(hasHorseSkill(skillid1))
		skillId = skillid1;
	else if(hasHorseSkill(skillid2))
		skillId = skillid2;

	if(skillId != -1){
		getHorseSkill(skillId, skillvals);
	}
	return skillId;
}

bool ActorHorse::canRriddenByNoOxygen()
{
	if (hasHorseSkill(HORSE_SKILL_MUTATE_FLY) || hasHorseSkill(HORSE_SKILL_FLOAT) || hasHorseSkill(HORSE_SKILL_DIVING) || hasHorseSkill(HORSE_SKILL_WATER_RUSH) ||
		hasHorseSkill(HORSE_SKILL_GIANTWHALE_FLAY) || hasHorseSkill(HORSE_SKILL_GIANTWHALE_SWIM) || hasHorseSkill(HORSE_SKILL_SWAN_DIVING))
	{
		return true;
	}

	return false;
}

bool ActorHorse::needSetZoomByJump()
{
	if (getLocoMotion() && getLocoMotion()->m_OnGround)
		return true;

	if (hasHorseSkill(HORSE_SKILL_SWAN_FLY))
		return true;

	return false;
}

void ActorHorse::setHorseFlagBit(int ibit, bool b)
{
	if (b) m_HorseFlags = m_HorseFlags | (1 << ibit);
	else m_HorseFlags = m_HorseFlags & ~(1 << ibit);

	if (m_pWorld && !m_pWorld->isRemoteMode())
	{
		PB_HorseFlagHC horseFlagHC;
		horseFlagHC.set_flag(m_HorseFlags);
		horseFlagHC.set_objid(getObjId());

		GetGameNetManagerPtr()->sendBroadCast(PB_HORSE_FLAG_HC, horseFlagHC);
	}
}

bool ActorHorse::checkIsInvalidTarget(ClientActor* target,ClientPlayer *player)
{
	if (!target)
		return true;
	auto RidComp = getRiddenComponent();
	if (target == this || /*target->getObjId() == m_RidingActor*/(RidComp && (RidComp->checkRidingByActorObjId(target->getObjId()) || /*target->getObjId() == m_RiddenByActor*/RidComp->checkRiddenByActorObjId(target->getObjId()))))
		return true;
	if (!player->canHurtActor(target))
		return true;
	if (target->isDead())
		return true;
	return false;
}

const char* ActorHorse::getSkillParticleName(int skillid)
{
	switch(skillid){
	case HORSE_SKILL_CAT_PUFF:
		return "particles/horse_3454_1.ent";
	case HORSE_SKILL_BULL_PUFF:
		return NULL;
	case HORSE_SKILL_DETER:
		return "particles/horse_3494_3.ent";//horse_3494_3.emo
	case HORSE_SKILL_BULL_DETER:
		return "particles/horse_3454_2.ent";
	default:
		return NULL;
	}
}

void ActorHorse::playSkillEffect(int skillid)
{
	const char* path = getSkillParticleName(skillid);

	if(path && m_pWorld->getEffectMgr())
		m_pWorld->getEffectMgr()->playParticleEffectAsync(path, getPosition(), 40);

	if (m_Def)
	{
		auto sound = getSoundComponent();
		if (sound)
		{
			sound->playSound(m_Def->AttackSound.c_str(), 1, 1, 6);
		}
	}

	//if(skillid == HORSE_SKILL_BULL_PUFF){//主机还要通知客机
	//	playAnim(SEQ_FORTUNEMOOSKILL);
	//}
}

void ActorHorse::setSkillCD(int index, float cd)
{
	if (index < 0 || index >= (sizeof(m_fSkillCD) / sizeof(float)))
	{
		return;
	}

	m_fSkillCD[index] = cd;
}

float ActorHorse::getSkillCD(int index)
{
	if (index < 0 || index >= (sizeof(m_fSkillCD) / sizeof(float)))
	{
		return 0;
	}

	return m_fSkillCD[index];
}

bool ActorHorse::showUseBtn()
{
	//指定坐骑显示使用按钮
	if (hasHorseSkill(HORSE_SKILL_GOD_LIGHT) || hasHorseSkill(HORSE_SKILL_YE_WU) || hasHorseSkill(HORSE_SKILL_PLANE_FLASH)
		|| hasHorseSkill(HORSE_SKILL_YANYU_FIRE) || hasHorseSkill(HORSE_SKILL_YOUMIN_FIRE) || hasHorseSkill(HORSE_SKILL_STARLIGHT) || hasHorseSkill(HORSE_SKILL_DOUDURUSH)
		|| hasHorseSkill(HORSE_SKILL_FORTUNEMOO))
		return true;

	return false;
}

void ActorHorse::syncSkillCD2Client(int index, float cd)
{
	if (!m_pWorld || m_pWorld->isRemoteMode())
		return;

	PB_Horse_SkillCDHC skillCDHC;
	skillCDHC.set_actorid(getObjId());
	skillCDHC.set_index(index);
	skillCDHC.set_cd(cd);
	m_pWorld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_HORSE_SKILLCD_HC, skillCDHC, this, false);
}

void ActorHorse::doSkillAddBufToTarget(ClientActor *target, float skillVal)
{
	ActorLiving *live = dynamic_cast<ActorLiving *>(target);
	if (live && live->getLivingAttrib())
		live->getLivingAttrib()->addBuff(SLOW_BUFF, 3, (int)(20*skillVal));//skillvals[1]
}
void ActorHorse::doSkillAttackTarget(ClientActor *target, float skillVal)
{
	OneAttackData atkdata;
	ATTACK_TARGET_TYPE targettype = target->getAttackTargetType();

	//memset(&atkdata, 0, sizeof(atkdata));
	atkdata.damage_armor = true;
	// 新伤害计算系统 code-by:liya
	if (GetLuaInterfaceProxy().get_lua_const()->isOpenNewHpdecCalculate)
	{
		atkdata.atkTypeNew = (1 << ATTACK_PUNCH);
		atkdata.atkPointsNew[ATTACK_PUNCH] = 5;
	}
	else
	{
		atkdata.atktype = ATTACK_PUNCH;
		atkdata.atkpoints = 5;
	}
	atkdata.enchant_atk = 0;
	atkdata.buff_atk = 0;
	atkdata.critical = false;

	atkdata.knockback = skillVal;
	atkdata.knockup = 0;
	atkdata.fromplayer = NULL;
	auto component = target->getAttackedComponent();
	if (component)
	{
		component->attackedFrom(atkdata, this);
	}
}

void ActorHorse::checkDoCatSkill(int skillId,std::vector<ClientActor *> &actors, float* skillvals,ClientPlayer *player,std::function<void(ClientActor*, float)> callback)
{
	if(skillId == -1)
		return;

	getSkillEffectedActors(actors,skillvals[0]);
	for (size_t i = 0; i < actors.size(); i++)
	{
		if(checkIsInvalidTarget(actors[i], player))
			continue;
		callback(actors[i], skillvals[1]);
		//doSkillAddBufToTarget(actors[i], skillvals[1]);
	}
	playSkillEffect(skillId);
}

void ActorHorse::doCatSkill()
{
	if (!m_pWorld || m_pWorld->isRemoteMode())
		return;

	ClientPlayer *player = dynamic_cast<ClientPlayer*>(GetWorldManagerPtr()->findActorByWID(getRiddenByActorID()));
	if (!player)
		return;

	std::vector<ClientActor *>actors;	
	float skillvals[7];
	int skillId = checkHasSkill(HORSE_SKILL_CAT_PUFF,HORSE_SKILL_BULL_PUFF,skillvals);
	checkDoCatSkill(skillId,actors,skillvals,player,std::bind(&ActorHorse::doSkillAddBufToTarget,this, std::placeholders::_1, std::placeholders::_2));
	//

	skillId = checkHasSkill(HORSE_SKILL_DETER,HORSE_SKILL_BULL_DETER,skillvals);
	checkDoCatSkill(skillId,actors,skillvals,player,std::bind(&ActorHorse::doSkillAttackTarget,this, std::placeholders::_1, std::placeholders::_2));
}
static Rainbow::Vector3f s_MotionDecay(0.9f, 0.98f, 0.9f);
void ActorHorse::doFloatageSkill()
{
	if (!getLocoMotion())
		return;
	float skillvals[7] = { 0.0f };

	// ********：扶摇技能也可以飞行  codeby： keguanqiang
	bool ret = getHorseFlySkills(skillvals);
	if (hasHorseSkill(HORSE_SKILL_FUYAO))
		ret = getHorseSkill(HORSE_SKILL_FUYAO, skillvals);

	if (ret)
	{
		if (m_pWorld && m_pWorld->isRemoteMode())
		{
			if (m_PosRotationIncrements > 0)
			{
				getLocoMotion()->setPosition(getLocoMotion()->m_Position + (m_HorsePos - getLocoMotion()->m_Position) / m_PosRotationIncrements);
				//getLocoMotion()->m_Position = getLocoMotion()->m_Position + (m_HorsePos - getLocoMotion()->m_Position) / m_PosRotationIncrements;
				getLocoMotion()->m_RotateYaw = getLocoMotion()->m_RotateYaw + WrapAngleTo180(m_HorseYaw - getLocoMotion()->m_RotateYaw) / m_PosRotationIncrements;
				getLocoMotion()->m_RotationPitch = getLocoMotion()->m_RotationPitch + (m_HorsePitch - getLocoMotion()->m_RotationPitch) / m_PosRotationIncrements;

				m_PosRotationIncrements--;
			}
			else
			{
				getLocoMotion()->setPosition(getLocoMotion()->m_Position + getLocoMotion()->getIntegerMotion(getLocoMotion()->m_Motion));
				//getLocoMotion()->m_Position = getLocoMotion()->m_Position + getLocoMotion()->getIntegerMotion(getLocoMotion()->m_Motion);
				getLocoMotion()->m_Motion *= s_MotionDecay;
			}
		}
		else
		{
			ActorLiving *riddenby = dynamic_cast<ActorLiving *>(getRiddenByActor());
			if (riddenby)
			{
				LivingLocoMotion *locomove = dynamic_cast<LivingLocoMotion *>(riddenby->getLocoMotion());
				if (locomove)
				{
					if (hasHorseSkill(HORSE_SKILL_BAMBOO_DRAGONFLY_FLY1) || hasHorseSkill(HORSE_SKILL_BAMBOO_DRAGONFLY_FLY2))
					{
						if (m_bDragonFlyIsFly)
						{
							if (m_CurCharge > 0)
								getLocoMotion()->m_Motion.y = skillvals[0];
							else
								getLocoMotion()->m_Motion.y = skillvals[1];
						}
					}
					else if (m_fEnergy > 0)
					{
						getLocoMotion()->m_Motion.y = skillvals[0];
						if (hasHorseSkill(HORSE_SKILL_FUYAO))	// ********：扶摇技能在盘旋起飞期间不能控制移动  codeby： keguanqiang
						{
							getLocoMotion()->m_Motion.x = 0;
							getLocoMotion()->m_Motion.z = 0;
						}
					}

					else
						getLocoMotion()->m_Motion.y = skillvals[1];

					Rainbow::Vector3f fdir = Yaw2FowardDir(getLocoMotion()->m_RotateYaw);

					float turnSpeed = skillvals[5] * skillvals[6];
					float forwardSpeed = skillvals[5] - turnSpeed;
					if (forwardSpeed <= 0)
						forwardSpeed = skillvals[5];

					if (locomove->m_MoveForward > 0)
					{
						forwardSpeed += skillvals[3];
					}
					else if (locomove->m_MoveForward < 0)
					{
						forwardSpeed -= skillvals[4];
					}

					if (hasHorseSkill(HORSE_SKILL_BAMBOO_DRAGONFLY_FLY1) || hasHorseSkill(HORSE_SKILL_BAMBOO_DRAGONFLY_FLY2) || (m_fEnergy > 0 && hasHorseSkill(HORSE_SKILL_FUYAO)))	// 20210929：扶摇技能在盘旋起飞期间不能控制移动  codeby： keguanqiang
					{
						//暂时不需要处理
					}
					else
					{
						getLocoMotion()->m_Motion.x = fdir.x*forwardSpeed;
						getLocoMotion()->m_Motion.z = fdir.z*forwardSpeed;

						fdir = Yaw2FowardDir(locomove->m_RotateYaw);
						getLocoMotion()->m_Motion.x += fdir.x*turnSpeed;
						getLocoMotion()->m_Motion.z += fdir.z*turnSpeed;
					}
				}
			}

			getLocoMotion()->doMoveStep(getLocoMotion()->m_Motion);

		   if (m_fEnergy <= 0 || !hasHorseSkill(HORSE_SKILL_FUYAO))		// 20210929：扶摇技能在盘旋起飞期间不能控制转向  codeby： keguanqiang
			{
				float targetyaw = getLocoMotion()->m_RotateYaw;
				getLocoMotion()->m_RotationPitch = 0.0f;
				Rainbow::Vector3f dpos = getLocoMotion()->m_Motion;
				dpos.y = 0;
				Direction2PitchYaw(&targetyaw, NULL, dpos);
				getLocoMotion()->m_RotateYaw += Rainbow::Clamp(WrapAngleTo180(targetyaw - getLocoMotion()->m_RotateYaw), -20.0f, 20.0f);
			}

			if (hasHorseSkill(HORSE_SKILL_BAMBOO_DRAGONFLY_FLY1) || hasHorseSkill(HORSE_SKILL_BAMBOO_DRAGONFLY_FLY2))
			{
				if (getPosition().y >= m_iDragonFlyHeight && m_bDragonFlyIsFly)
					setPosition(getPosition().x, m_iDragonFlyHeight, getPosition().z);
			}
		}
	}

	m_fEnergy--;
}


void ActorHorse::doFlash()
{
	if (hasHorseSkill(HORSE_SKILL_PLANE_FLASH))
	{
		float skillvals[7];
		getHorseSkill(HORSE_SKILL_PLANE_FLASH, skillvals);

		CollideAABB box;
		getCollideBox(box);
		Rainbow::Vector3f dir = Yaw2FowardDir(getLocoMotion()->m_RotateYaw);
		dir.y = 0.02f;
		WCoord mvec(dir * (skillvals[2] * BLOCK_FSIZE));//skillvals[0]
		Rainbow::Vector3f colnormal;
		float t = m_pWorld->moveBox(box, mvec, colnormal);
		dir *= (int)(t*mvec.length());

		setPosition(getPosition() + dir);
		auto effectComponent = getEffectComponent();
		if (effectComponent)
		{
			effectComponent->playBodyEffect("flash_off");
		}
		setHorseFlagBit(HORSE_FLAG::FLASHING, false);
	}
}


// ********：狂风技能  codeby： keguanqiang
void ActorHorse::doKuangFeng()
{
	if (!getLocoMotion() || !m_pWorld)
		return;
	auto sound = getSoundComponent();
	if (sound)
	{
		sound->playSound("ent.4545.attack1", 1, 1);
		sound->playSound("ent.4545.attack2", 1, 1);
	}
	//playBodyEffect("mechBird87");
	playAnim(SEQ_FORTUNEMOOSKILL);

	// 20210927：特效  codeby： keguanqiang
	if (m_pWorld->getEffectMgr())
	{
		Rainbow::Vector3f dir = Yaw2FowardDir(getLocoMotion()->m_RotateYaw);
		dir  = MINIW::Normalize(dir);
		dir *= (BLOCK_SIZE * 3);

		m_pWorld->getEffectMgr()->playParticleEffectAsync("particles/mechBird87.ent", getPosition() + dir, 200, getLocoMotion()->m_RotateYaw, 0, true, 0);
	}


	float skillvals[7];
	if (!getHorseSkill(HORSE_SKILL_KUANGFENG, skillvals))
		return;

	Rainbow::Vector3f dir = Yaw2FowardDir(getLocoMotion()->m_RotateYaw);
	dir  = MINIW::Normalize(dir);
	dir *= (skillvals[2] * BLOCK_SIZE * 2);

	CollideAABB box;
	getCollideBox(box);
	box.expand(skillvals[2] * BLOCK_SIZE, 0, skillvals[2] * BLOCK_SIZE);

	std::vector<IClientActor *>tmpactors;
	std::vector<ClientActor *>actors;
	m_pWorld->getActorsInBox(tmpactors, box);

	WCoord origin = getPosition();
	for (size_t i = 0; i < tmpactors.size(); i++)
	{
		ClientActor *actor = tmpactors[i]->GetActor();
		auto RidComp = getRiddenComponent();
		if (actor == this || (RidComp && (RidComp->checkRidingByActorObjId(actor->getObjId()) || RidComp->checkRiddenByActorObjId(actor->getObjId()))))
			continue;

		if (actor->isDead())
			continue;

		if (actor->needClear())
			continue;

		if (actor->getObjType() == OBJ_TYPE_ROLE)
			continue;

		ActorHorse *horse = dynamic_cast<ActorHorse *>(actor);
		if (horse)
			continue;

		Rainbow::Vector3f dp = (actor->getPosition() - origin).toVector3();
		if (dp.y > skillvals[4] * BLOCK_SIZE || dp.y < 0)
			continue;

		if (dp.x == 0.0f && dp.y == 0.0f && dp.z == 0.0f)
		{
			actors.push_back(actor);
			continue;
		}



		float t = DotProduct(dp, dir);
		if (t <= 0)
			continue;


		Rainbow::Vector3f tmp = CrossProduct(dp, dir);
		if (tmp.Length() / dp.Length() < skillvals[3] * BLOCK_SIZE)
			actors.push_back(actor);
	}

	for (size_t i = 0; i < actors.size(); i++)
	{
		ActorLiving *live = dynamic_cast<ActorLiving *>(actors[i]);
		if (live && live->getLivingAttrib())
		{
			int buffid = skillvals[0] / 1000;
			int lv = skillvals[0] - buffid * 1000;
			live->getLivingAttrib()->addBuff(buffid, lv);
			if (live->getNavigator())
				live->getNavigator()->clearPathEntity();
		}
	}
}

void ActorHorse::moveToPosition(const WCoord &pos, float yaw, float pitch, int interpol_ticks)
{
	if (m_bFloatageing && getLocoMotion())
	{
		WCoord dpos = pos - getLocoMotion()->m_Position;

		m_PosRotationIncrements = 3;
		m_HorsePos = pos;
		m_HorseYaw = yaw;
		m_HorsePitch = pitch;
		getLocoMotion()->m_Motion.x = 0;
		getLocoMotion()->m_Motion.y = 0;
		getLocoMotion()->m_Motion.z = 0;
	}
	else
	{
		if (getHorseFlagBit(HORSE_FLAG::FLASHING))
			interpol_ticks = 1;

		ActorLiving::moveToPosition(pos, yaw, pitch, interpol_ticks);
	}
		

}

int ActorHorse::getObjType() const
{
	return OBJ_TYPE_HORSE;
}

void ActorHorse::playLoopSound(int state, const char *name)
{
	if (state == -1 || m_CurPlaySnd)
	{
		OGRE_RELEASE(m_CurPlaySnd)
	}

	if (state == 0)
		m_CurPlaySnd = m_pWorld->getEffectMgr()->playLoopSound(getPosition(), name, 1.0f, 1.0f);
}

int ActorHorse::getAccoutHorseOwnerUin()
{
	std::vector<IClientPlayer *>players;
	if (GetWorldManagerPtr() == NULL)
		return 0;

	GetWorldManagerPtr()->getAllPlayers(players);
	for (size_t i = 0; i < players.size(); i++)
	{
		ClientPlayer *player = dynamic_cast<ClientPlayer*>(players[i]);
		if (player->isMyAccountHorse(getObjId()))
			return player->getUin();
	}

	return 0;
}

int ActorHorse::getBamboDragonFlyState()
{
	if (m_HorseDef && m_HorseDef->ID == BAMBOO_DRAGONFLY1 || m_HorseDef->ID == BAMBOO_DRAGONFLY2)
		return m_iDragonFlyState;
	return 0;
}

void ActorHorse::changeGunBulletSpawnPos(WCoord& pos)
{
	if (hasHorseSkill(HORSE_SKILL_BAMBOO_DRAGONFLY_FLY))
	{
		pos.y -= 80;
	}
}

bool ActorHorse::getRiddenChangeFPSView() {
	if (m_HorseDef && m_HorseDef->ChangeFPS)
		return true;
	auto RidComp = getRiddenComponent();
	if (RidComp)
	{
		return RidComp->getNumRiddenPos() > 1;
	}
	return false;
}

void ActorHorse::playFlyEffect()
{
	if (!m_pWorld || m_pWorld->isRemoteMode())
		return;

	if (hasHorseSkill(HORSE_SKILL_AIR_ALERT_FLOATAGE))
	{
		auto effectComponent = getEffectComponent();
		if (effectComponent)
		{
			effectComponent->playBodyEffect("horsechange_3464_1");
		}
	}
	else if (hasHorseSkill(HORSE_SKILL_SWIFT_WINGS))
	{
		auto effectComponent = getEffectComponent();
		if (effectComponent)
		{
			effectComponent->playBodyEffect("horsechange_3476_0");
		}
	}
	else if (hasHorseSkill(HORSE_SKILL_PLANE_FLY1))
	{
		if (m_Def)
		{
			if (m_Def->ID == 4517)
			{
				auto effectComponent = getEffectComponent();
				if (effectComponent)
				{
					effectComponent->playBodyEffect("jet_62"); //kgqTODO 前光波特效
				}
			}
			else if (m_Def->ID == 4520)
			{
				auto effectComponent = getEffectComponent();
				if (effectComponent)
				{
					effectComponent->playBodyEffect("jet_64");
				}
			}
		}
	}
	else if (hasHorseSkill(HORSE_SKILL_PLANE_FLY2))
	{
		if (m_Def)
		{
			if (m_Def->ID == 4518)
			{
				auto effectComponent = getEffectComponent();
				if (effectComponent)
				{
					effectComponent->playBodyEffect("jet_63");
				}
			}
			else if (m_Def->ID == 4521)
			{
				auto effectComponent = getEffectComponent();
				if (effectComponent)
				{
					effectComponent->playBodyEffect("jet_65");
				}
			}
		}
	}
	else if(hasHorseSkill(HORSE_SKILL_FLOATAGE))
	{
		auto effectComponent = getEffectComponent();
		if (effectComponent)
		{
			effectComponent->playBodyEffect("horse_3455_1");
		}
	}
}

void ActorHorse::stopFlyEffect()
{
	if (!m_pWorld || m_pWorld->isRemoteMode())
		return;
	auto effectComponent = getEffectComponent();
	if (effectComponent)
	{
		if (hasHorseSkill(HORSE_SKILL_FLOATAGE))
		{
			effectComponent->stopBodyEffect("horse_3455_1");
		}
		else if (hasHorseSkill(HORSE_SKILL_AIR_ALERT_FLOATAGE))
		{
			effectComponent->stopBodyEffect("horsechange_3464_1");
		}
		else if (hasHorseSkill(HORSE_SKILL_MOONRISE))
		{
			effectComponent->stopBodyEffect("horse_3478_fly");
		}
		else if (hasHorseSkill(HORSE_SKILL_PLANE_FLY1))
		{
			if (m_Def)
			{
				if(m_Def->ID == 4517)
					effectComponent->stopBodyEffect("jet_62"); //kgqTODO 前光波特效
				else if (m_Def->ID == 4520)
					effectComponent->stopBodyEffect("jet_64");
			}
		}
		else if (hasHorseSkill(HORSE_SKILL_PLANE_FLY2))
		{
			if (m_Def)
			{
				if (m_Def->ID == 4518)
					effectComponent->stopBodyEffect("jet_63");
				else if (m_Def->ID == 4521)
					effectComponent->stopBodyEffect("jet_65");
			}
		}
	}
}

void ActorHorse::playMoveEffect()
{
	if (!m_pWorld || m_pWorld->isRemoteMode())
		return;
	auto effectComponent = getEffectComponent();
	if (effectComponent)
	{
		if (hasHorseSkill(HORSE_SKILL_PLANE_FLY1))
		{
			if (m_Def)
			{
				if (m_Def->ID == 4517)
					effectComponent->playBodyEffect("jet_62"); //kgqTODO 前光波特效
				else if (m_Def->ID == 4520)
					effectComponent->playBodyEffect("jet_64");
			}
		}
		else if (hasHorseSkill(HORSE_SKILL_PLANE_FLY2))
		{
			if (m_Def)
			{
				if (m_Def->ID == 4518)
					effectComponent->playBodyEffect("jet_63");
				else if (m_Def->ID == 4521)
					effectComponent->playBodyEffect("jet_65");
			}
		}
	}
}

void ActorHorse::stopMoveEffect()
{
	if (!m_pWorld || m_pWorld->isRemoteMode())
		return;
	auto effectComponent = getEffectComponent();
	if (effectComponent)
	{
		if (hasHorseSkill(HORSE_SKILL_PLANE_FLY1))
		{
			if (!m_bFloatageing && m_Def)
			{
				if (m_Def->ID == 4517)
					effectComponent->stopBodyEffect("jet_62"); //kgqTODO 前光波特效
				else if (m_Def->ID == 4520)
					effectComponent->stopBodyEffect("jet_64");
			}
		}
		else if (hasHorseSkill(HORSE_SKILL_PLANE_FLY2))
		{
			if (!m_bFloatageing && m_Def)
			{
				if (m_Def->ID == 4518)
					effectComponent->stopBodyEffect("jet_63");
				else if (m_Def->ID == 4521)
					effectComponent->stopBodyEffect("jet_65");
			}
		}
	}
}

LivingLocoMotion* ActorHorse::getRiddenByLivingLocoMotion()
{
	ActorLiving *riddenby = dynamic_cast<ActorLiving *>(getRiddenByActor());
	if (riddenby)
	{
		LivingLocoMotion *locomove = dynamic_cast<LivingLocoMotion *>(riddenby->getLocoMotion());
		if (locomove)
			return locomove;
	}
	return NULL;
}



void ActorHorse::setSkillScript(std::string path)
{
	HorseSkillComponent* skill = SANDBOX_NEW(HorseSkillComponent);
	skill->init(path, this);
	m_skillComList.push_back(skill);
}

void ActorHorse::toDoMoveStep()
{
	getLocoMotion()->doMoveStep(getLocoMotion()->m_Motion);
}

void ActorHorse::updateHorseBodyAnim(int& anim)
{
	int id = -1;
	for (size_t i = 0; i < m_skillComList.size(); i++)
	{
		HorseSkillComponent* skill = m_skillComList[i];
		if (skill->updateHorseBodyAnim() >= 0)
			id = skill->updateHorseBodyAnim();
	}
	if (id > -1)
		anim = id;
}

void ActorHorse::updateRiddenBodyAnim(int& anim, ClientActor *ridden/* =NULL */)
{
	int id = -1;
	for (size_t i = 0; i < m_skillComList.size(); i++)
	{
		HorseSkillComponent* skill = m_skillComList[i];
		if (skill->updateRiddenBodyAnim(ridden) >= 0)
			id = skill->updateRiddenBodyAnim(ridden);
	}
	if (id > -1)
		anim = id;
}

void ActorHorse::OnHorseMounted()
{
	for (unsigned int i = 0; i < m_skillComList.size(); i++)
	{
		HorseSkillComponent* skill = m_skillComList[i];
		skill->OnHorseMounted();
	}
}

void ActorHorse::OnHorseDismounted()
{
	for (size_t i = 0; i < m_skillComList.size(); i++)
	{
		HorseSkillComponent* skill = m_skillComList[i];
		skill->OnHorseDismounted();
	}
}

void ActorHorse::AttackFacedActors(const Rainbow::Vector3f &dir, int range, int width, OneAttackData atkdata)
{
	std::vector<ClientActor *>actors;
	ClientPlayer *player = dynamic_cast<ClientPlayer*>(GetWorldManagerPtr()->findActorByWID(getRiddenByActorID()));

	getLocoMotion()->getFacedActors(actors, dir, range, width);

	auto RidComp = getRiddenComponent();
	for (size_t i = 0; i < actors.size(); i++)
	{
		ClientActor *target = actors[i];
		if (target == this || (RidComp && (RidComp->checkRidingByActorObjId(target->getObjId()) || RidComp->checkRiddenByActorObjId(target->getObjId())))) 
			continue;
		if (!player->canHurtActor(target)) continue;
		if (target->isDead()) continue;
		auto component = target->getAttackedComponent();
		if (component)
		{
			component->attackedFrom(atkdata, this);
		}
	}

}

void ActorHorse::RefreshRiddenActor()
{
	if (!m_pWorld->isRemoteMode())
	{
		auto RidComp = getRiddenComponent();
		if (RidComp)
		{
			for (int i = 0; i < RidComp->getNumRiddenPos(); i++)
			{
				ClientActor *ridden = getRiddenByActor(i);
				if (ridden)
				{
					auto riddenComp = ridden->getRiddenComponent();
					if ( /*ridden->m_RidingActor != getObjId()*/!(riddenComp && riddenComp->checkRidingByActorObjId(getObjId())))
					{
						setRiddenByActor(NULL, i);
					}
					else if (ridden && (ridden->isDead() || ridden->needClear()))
					{
						//if(ridden->m_RidingActor == getObjId())
						if (riddenComp && riddenComp->checkRidingByActorObjId(getObjId()))
						{
							riddenComp->mountActor(NULL);
						}
						RidComp->clearRiddenActor(ridden);
					}
				}
			}
		}
		

		if (isDead() || needClear())
		{
			//ridden->mountActor(NULL);
			if (RidComp)
			{
				for (int i = 0; i < RidComp->getNumRiddenPos(); i++)
				{
					ClientActor *ridden = getRiddenByActor(i);
					if (ridden)
					{
						auto riddenComp = ridden->getRiddenComponent();
						if (riddenComp && /*ridden->m_RidingActor==getObjId()*/riddenComp->checkRidingByActorObjId(getObjId()))
						{
							riddenComp->mountActor(NULL);
						}
					}
				}
			}
		}
		else
		{
			CollideAABB box;
			getCollideBox(box);
			box.expand(BLOCK_SIZE, BLOCK_SIZE / 2, BLOCK_SIZE);

			ClientActor *ridden = getRiddenByActor();
			ClientPlayer *player = dynamic_cast<ClientPlayer*>(ridden);
			if (ridden && player)
			{
				std::vector<IClientActor *>actors;
				m_pWorld->getActorsInBoxExclude(actors, box, this);
				for (size_t i = 0; i < actors.size(); i++)
				{
					ClientActor *actor = actors[i]->GetActor();
					if (!actor->needClear())
					{
						actor->onCollideWithPlayer(player);
					}
				}
			}
		}
	}
	else
	{
		tickByClient();
	}
}

int ActorHorse::getRiddenAnim()    // 20210715：获取骑乘动作  codeby： keguanqiang
{
	if (m_Def && (m_Def->ID >= 4524 && m_Def->ID <= 4529))
		return SEQ_STAND;

	return SEQ_SITDOWN;
}


bool ActorHorse::moveLocoMotionEntityWithHeading(float strafing, float forward)
{
	bool isEnd = false;
	for (size_t i = 0; i < m_skillComList.size(); i++)
	{
		HorseSkillComponent* skill = m_skillComList[i];
		isEnd = skill->moveLocoMotionEntityWithHeading(strafing, forward) || isEnd;
	}
	return isEnd;
}

// ********：打断隐身  codeby： keguanqiang
void ActorHorse::breakInvisible()
{
	if (!m_pWorld || m_pWorld->isRemoteMode())
		return;

	if (!getBody())
		return;

	if (!getHorseFlagBit(HORSE_FLAG::INVISIBLE))
		return;

	m_iInvisibleTick = -1;
	setHorseFlagBit(HORSE_FLAG::INVISIBLE, false);

	// 20210914: 隐身后骑乘者能看到特效，其余人看不到特效 begin codeby： keguanqiang
	bool igoreMotion = false;
	if (g_pPlayerCtrl)
	{
		auto PlayerRidComp = g_pPlayerCtrl->getRiddenComponent();
		if (PlayerRidComp && PlayerRidComp->getRidingActorObjId() == getObjId())
			igoreMotion = true;
	}

	getBody()->show(true, true, igoreMotion,false);	// 20210917: 坐骑名字的显示不受隐身技能的影响 codeby： keguanqiang
	// 20210914: 隐身后骑乘者能看到特效，其余人看不到特效 end codeby： keguanqiang

	setHPProgressDirty();
	auto effectComponent = getEffectComponent();
	if (effectComponent)
	{
		effectComponent->stopBodyEffect("carpet84");
	}

	syncInvisibleInfo(false);
}

// ********: 开始隐身  codeby： keguanqiang
void ActorHorse::startInvisible()
{
	if (!m_pWorld || m_pWorld->isRemoteMode())
		return;

	if (!getBody())
		return;

	if (getHorseFlagBit(HORSE_FLAG::INVISIBLE))
		return;

	setHorseFlagBit(HORSE_FLAG::INVISIBLE, true);

	// 20210914: 隐身后骑乘者能看到特效，其余人看不到特效 begin codeby： keguanqiang
	bool igoreMotion = false;
	if (g_pPlayerCtrl)
	{
		auto PlayerRidComp = g_pPlayerCtrl->getRiddenComponent();
		if (PlayerRidComp && PlayerRidComp->getRidingActorObjId() == getObjId())
			igoreMotion = true;
	}

	getBody()->show(false, false, igoreMotion,false);
	// 20210914: 隐身后骑乘者能看到特效，其余人看不到特效 end codeby： keguanqiang
	setHPProgressDirty();
	auto effectComponent = getEffectComponent();
	if (effectComponent)
	{
		effectComponent->stopBodyEffect("carpet83");			// 20210914: 隐身后中止召唤技能的特效播放 codeby： keguanqiang
		effectComponent->playBodyEffect("carpet84");
	}
	if (m_Def)
	{
		auto sound = getSoundComponent();
		if (sound)
		{
			sound->playSound(m_Def->AttackSound.c_str(), 1, 1);
		}
	}

	syncInvisibleInfo(true);
}

// ********: 同步隐身信息  codeby： keguanqiang
void ActorHorse::syncInvisibleInfo(bool invisible)
{
	PB_RideInvisibleHC msg;
	msg.set_invisible(invisible);
	msg.add_objidlist(getObjId());

	auto RidComp = getRiddenComponent();
	int RiddenPos = 0;
	if (RidComp)
	{
		RiddenPos = RidComp->getNumRiddenPos();
	}
	for (int i = 0; i < RiddenPos; i++)
	{
		ActorLiving *ridden = dynamic_cast<ActorLiving *>(getRiddenByActor(i));
		if (ridden && ridden->getBody())
		{
			// 20210914: 隐身后骑乘者能看到特效，其余人看不到特效 begin codeby： keguanqiang
			bool igoreMotion = false;
			if (g_pPlayerCtrl && g_pPlayerCtrl->getObjId() == ridden->getObjId())
				igoreMotion = true;

			ridden->getBody()->show(!invisible, false, igoreMotion,false);
			// 20210914: 隐身后骑乘者能看到特效，其余人看不到特效 end codeby： keguanqiang

			ridden->setHPProgressDirty();
			msg.add_objidlist(ridden->getObjId());
		}
	}

	GetGameNetManagerPtr()->sendBroadCast(PB_RIDE_INVISIBLE_HC, msg);
}



// ********：获取坐骑飞行动作  codeby： keguanqiang
int ActorHorse::getFlyAnim()
{
	if (hasHorseSkill(HORSE_SKILL_FUYAO) && m_fEnergy <= 0) //扶摇技能处于非盘旋状态时，播休闲动作
		return SEQ_STAND;

	return SEQ_FLY;
}

bool ActorHorse::playWalkOnLiquidEffect(bool iswater)
{
	bool isEnd = false;
	for (size_t i = 0; i < m_skillComList.size(); i++)
	{
		HorseSkillComponent* skill = m_skillComList[i];
		isEnd = skill->playWalkOnLiquidEffect(iswater) || isEnd;
	}

	return isEnd;
}

bool ActorHorse::canWalkOnLiquid(bool iswater)
{
	for (size_t i = 0; i < m_skillComList.size(); i++)
	{
		HorseSkillComponent* skill = m_skillComList[i];
		int ret = skill->canWalkOnLiquid(iswater);
		if (ret >= 0)
			return ret > 0;
	}

	return hasHorseSkill(iswater ? HORSE_SKILL_WATERWALK : HORSE_SKILL_LAVAWALK);
}

void ActorHorse::setBodyRotation(float yaw, float pitch, float roll)
{
	if (getBody() && getBody()->getEntity())
		getBody()->getEntity()->SetRotation(yaw, pitch, roll);
}

bool ActorHorse::mobCanRake(int mobDefId)
{
	return mobDefId == 3891 || mobDefId == 3262;
}

bool ActorHorse::getRiddenControl()
{
	if (m_Def && m_Def->ID == 3912) //牦牛 能被骑，但不能被控制
	{
		return false;
	}
	return true;
}