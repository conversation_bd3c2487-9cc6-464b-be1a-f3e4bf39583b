#ifndef __I_PLAYER_CONTROL_H__
#define __I_PLAYER_CONTROL_H__
#include "SandboxEngine.h"
#include "Geometry/AABB.h"
#include "OgreWCoord.h"
#include "Misc/InputEvent.h"
#include "worldData/world_types.h"

class World;
class IBackPack;
class IActorLocoMotion;
class WorldContainer;
class TouchControl;
class PCControl;
class IClientActor;
class IClientPlayer;
class GameCamera;
class InputInfo;
class CustomGunDef;
class ChunkViewer;
namespace MNSandbox
{
    class EventObjectManager;
    class SandboxEventDispatcherManager;
}

/* IPlayerControl 基类 */
class EXPORT_SANDBOXENGINE IPlayerControl // tolua_export
{// tolua_export
public:
    IPlayerControl();
    virtual ~IPlayerControl();

    virtual IClientActor* ControlCastToActor();
    virtual IClientPlayer* CastToPlayer();

    virtual void OnLoadChunk(CHUNK_INDEX index) = 0;

    virtual WCoord& GetPlayerControlPosition() = 0;

    virtual const char* GetPlayerControlNickname() = 0;

    virtual Rainbow::AABB GetIAABB() = 0;

    virtual bool GetPlayerControlSneaking() = 0;
    virtual bool GetPlayerControlRun() = 0;
    virtual void resetHandModel() = 0;
    virtual int GetIUin() = 0;
    virtual World* getIWorld() = 0;
    virtual int GetPlayerControlCurToolID() = 0;
    virtual IActorLocoMotion* getPlayerControlLocoMotion() = 0;
    virtual void setStill() = 0;
    virtual Rainbow::Vector3f getLookDir() = 0;
    virtual void setCurShortcut(int i) = 0;
    virtual unsigned short GetPlayerControlCurMapID() = 0;

    virtual IBackPack* getIPlayerControlBackPack() = 0;
    virtual bool GetAllowInput(size_t event, int keyvalue) = 0;
    virtual void iNotifyGameInfo2Self(int infotype, int id, int num = 0, const char* name = NULL, const char* buff = NULL) = 0;
    virtual void dismountActor() = 0;
    virtual void clearBuffAndStatus() = 0;
    virtual void setCurSprayPaint(bool isNext) = 0;
    virtual void iSetGameScore(int s) = 0;
    virtual int GetExploitCheckBlockId(bool isDigBlockId) = 0;
    virtual void setRefreshType(int type) = 0;
    virtual float getDigProgress() = 0;
    virtual float getReviveProgress() = 0; //获取救援进度
    virtual void setCircleProgress(float p) = 0;
    virtual int  getDigBlockID() = 0;
    virtual void setExploiting(bool val) = 0;
    virtual void setIsRightTool(bool val) = 0;
    virtual void setRightToolId(int val) = 0;
    virtual bool GetPlayerControlAimPos(int& outx, int& outy, int& outz, int distance = 20, bool calibration = false) = 0;
    virtual void SetCurMouseX(float value) = 0;
    virtual void SetCurMouseY(float value) = 0;
    virtual InputInfo* getInputInfo() = 0;
    virtual WorldContainer* GetPlayerControlCurOpenedContainer() = 0;

    virtual void handleInteraction() = 0;

    virtual int GetPlayerControlCurPlaceDir() = 0;
    virtual WCoord GetPlayerControlCurOperatePos(int operatedistance = 5) = 0;

    virtual int getViewMode() = 0;
    virtual GameCamera* getCamera() = 0;
    virtual WCoord GetPlayerControlEyePosition() = 0;
    virtual bool GetEnableInput() = 0;
    virtual void ActorUpdate(float dtime) = 0;
    virtual void onToggleGameMode() = 0;

    virtual TouchControl* getTouchControl() = 0;
    virtual PCControl* getPCControl() = 0;
    virtual void switchCurrentItem() = 0;
    virtual bool getBobbingByRocket() = 0;
    virtual bool isShakingCamera() = 0;
    virtual void setShakeCamera(bool shaking, int power = 1, float duration = 1) = 0;
    virtual void resetCurCameraConfig(bool isSavedConfig) = 0; // true:为自定义视角设置，false为系统默认设置
    virtual void applyCurCameraConfig() = 0;
    virtual int getCameraConfigOption(unsigned char option) = 0;
    virtual void setCameraConfigOption(unsigned char option, unsigned char value) = 0;
    virtual WCoord getCameraConfigPosition(float yaw, float pitch) = 0;
    virtual void setCameraConfigPosition() = 0;	//  以当前摄像机的位置为准
    virtual void setCameraConfigPositionEx(int x, int y, int z) = 0;
    virtual void setCameraRotate(int Yaw, int Pitch, float mul) = 0;
    virtual void updateGameCamera(float dtime) = 0;
    virtual void setViewMode(int mode, bool ignoreBasketBaller = false) = 0;
    virtual bool HasReversed() = 0;
    virtual bool HasGundef() = 0;
    virtual int getGunSpread() = 0;
    virtual int GetGunCrosshair() = 0;
    virtual void setZoom(bool open) = 0;
    virtual bool getZoom() = 0;
    virtual void resetRecoil() = 0;

    virtual WCoord getCameraPosition() = 0;
    virtual int getOption(unsigned char option) = 0;
    virtual float getInitPlayerYaw() = 0;

    virtual void onTouchInputEvent(const Rainbow::InputEvent& inevent) = 0;

    virtual bool IsGunHoldState(int state) = 0;
    virtual void PlayGunByState(int state) = 0;
    virtual const CustomGunDef* GetCustomGunDef() = 0;
    //获取当前的FPS手臂摇晃参数
    virtual void GetArmShakeParam(float& amplitude, float& speed) = 0;
    virtual void DoCurShotGunModEntry(bool enable) = 0;

    virtual bool interactBlock(const WCoord& targetblock, DirectionType targetface, const Rainbow::Vector3f& colpoint) = 0;
    virtual bool isSightMode() = 0;
    virtual void setSightMode(bool b, bool isIgnoreMoblie = true) = 0;
    virtual ChunkViewer* GetChunkViewerForCtrl() = 0;  // 提供给IPlayerControl访问的
#ifdef BUILD_MINI_EDITOR_APP
    virtual void SetEnableChunkViewer(bool enable) = 0;
#endif
};// tolua_export

EXPORT_SANDBOXENGINE IPlayerControl* GetIPlayerControl();
EXPORT_SANDBOXENGINE void SetIPlayerControl(IPlayerControl* controlptr);
#endif