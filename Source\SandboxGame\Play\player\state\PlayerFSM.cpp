#include "PlayerFSM.h"
#include "PlayerState.h"

#include "OgreScriptLuaVM.h"


PlayerFSM::PlayerFSM() : m_CurrentState(NULL), m_LastState(nullptr), m_DefaultState(nullptr)
{
}

PlayerFSM::~PlayerFSM()
{

}

PlayerState* PlayerFSM::getCurrentState()
{
	return m_CurrentState;
}

PlayerState* PlayerFSM::getLastState()
{
	return m_LastState;
}

void PlayerFSM::setDefaultState(const std::string &id)
{
	for (auto it = m_PlayerStates.begin(); it != m_PlayerStates.end(); it++)
	{
		if ((*it)->m_StateID == id)
		{
			m_DefaultState = *it;
			return;
		}
	}

	LOG_WARNING("setDefaultState Error: %s not found", id.c_str());
}

void PlayerFSM::resetToDefaultState()
{

}

void PlayerFSM::addState(PlayerState* state)
{
	//Check
	if (state == nullptr)
	{
		LOG_INFO("addState Error: null ref");
		return;
	}

	//Add first state
	if (m_PlayerStates.size() == 0)
	{
		m_PlayerStates.push_back(state);
		state->ownerFSM = this;
#ifndef IWORLD_SERVER_BUILD
		m_CurrentState = state;
#endif
		return;
	}

	for (auto it = m_PlayerStates.begin(); it != m_PlayerStates.end(); it ++)
	{
		if ((*it)->m_StateID == state->m_StateID)
		{
			LOG_INFO("State has already been added %s.", state->m_StateID.c_str());
			return;
		}
	}
	m_PlayerStates.push_back(state);
	state->ownerFSM = this;
}

bool PlayerFSM::replaceState(PlayerState* st)
{
	if(st == nullptr)
		return false;

	for (auto it = m_PlayerStates.begin(); it != m_PlayerStates.end(); it ++)
	{
		if ((*it)->m_StateID == st->m_StateID)
		{
			if(m_CurrentState == *it)
				m_CurrentState = st;
			if(m_DefaultState == *it)
				m_CurrentState = st;
			if(m_LastState == *it)
				m_CurrentState = st;

			m_PlayerStates.erase(it);
			m_PlayerStates.push_back(st);
			st->ownerFSM = this;
			return true;
		}
	}
	return false;
}

void PlayerFSM::deleteState(const std::string &id)
{
	if (id == "")
	{
		LOG_INFO("FSM ERROR: NullStateID is not allowed for a real state.");
		return;
	}

	for (auto it = m_PlayerStates.begin(); it != m_PlayerStates.end(); it++)
	{
		if ((*it)->m_StateID == id)
		{
			//ENG_DELETE(*it);  repeated delete
			m_PlayerStates.erase(it);
			return;
		}
	}

	LOG_INFO("FSM ERROR: Impossible to delete state  %s. It was not on the list of states", id.c_str());
}

bool PlayerFSM::performTransition(const std::string &trans)
{
	// Check for NullTransition before changing the current state
	if (trans == "")
	{
		LOG_INFO("FSM ERROR: NullTransition is not allowed for a real transition");
		return false;
	}

	std::string id = "";
	// Check if the currentState has the transition passed as argument
	if (nullptr != m_CurrentState)
	{
		id = m_CurrentState->getOutputState(trans);
		if (id == "")
		{
			return false;
		}
	}

	// Update the currentStateID and currentState		
	m_LastState = m_CurrentState;

	for (auto it = m_PlayerStates.begin(); it != m_PlayerStates.end(); it++)
	{
		if ((*it)->m_StateID == id)
		{
			m_LastState->OnLeave();
			m_CurrentState  = *it;
			m_CurrentState->OnEnter();
			return true;
		}
	}
	LOG_INFO("FSM ERROR: Cannot find state %s in PlayerStates", id.c_str());
	return false;
}

void PlayerFSM::update(float dtime)
{
	if(m_CurrentState)
	{
		std::string pst;
		//if(m_CurrentState->m_StateID == "ActionIdle"){
		//	//call lua handle
		//	//char stateid[64] = {0};
		//	//MINIW::ScriptVM::game()->callFunction("LuaPlayerStateMgrHandle","u[PlayerControl]>s", m_CurrentState->m_Host, stateid);
		//	//pst = stateid;
		//	//if(pst == "")
		//	pst = m_CurrentState->OnUpdate(dtime);
		//}else{

			pst = m_CurrentState->OnUpdate(dtime);
		//}

		if(pst != "" && m_CurrentState->m_StateID != pst )
		{
			performTransition(pst);
		}
	}
}

void PlayerFSM::tick(float dtime)
{
	if (m_CurrentState)
	{
		m_CurrentState->OnTick(dtime);
	}
}


PlayerState* PlayerFSM::getState(const std::string& id)
{
	for (auto it = m_PlayerStates.begin(); it != m_PlayerStates.end(); it++)
	{
		if ((*it)->m_StateID == id)
		{
			return (*it);
		}
	}
	return nullptr;
}

PlayerState* PlayerFSM::getState(const char* id)
{
	for (auto it = m_PlayerStates.begin(); it != m_PlayerStates.end(); it++)
	{
		if ((*it)->m_StateID.compare(id) == 0)
		{
			return (*it);
		}
	}
	return nullptr;
}

void PlayerFSM::setClientPlayer(ClientPlayer* pPlayer)
{
	for (auto it = m_PlayerStates.begin(); it != m_PlayerStates.end(); it++)
	{
		auto pState = dynamic_cast<ActionBase*>(*it);
		if (nullptr != pState)
		{
			pState->setPlayer(pPlayer);
		}
	}
}

