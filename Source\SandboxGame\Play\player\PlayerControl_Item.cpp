#include "PlayerControl.h"
#include "GameCamera.h"
#include "CameraModel.h"
#include "CameraManager.h"
#include "ClientItem.h"
#include "backpack.h"
#include "ClientActorManager.h"
#include "DefManagerProxy.h"
#include "PackingFullyCustomModelMgr.h"
#include "container_modelcraft.h"
#include "PlayerAttrib.h"
#include "CommonUtil.h"
#include "GameNetManager.h"
#include "RiddenComponent.h"
#include "CarryComponent.h"
#include "ActorBody.h"
#include "special_blockid.h"
#include "WorldManager.h"
#include "ActorLocoMotion.h"
#include "ActionIdleStateGunAdvance.h"
#include "ActorManager.h"
#include "IMiniDeveloperProxy.h"
#include "CraftingQueue.h"

using namespace MINIW;

extern unsigned int GetTeamColor(int teamid);

void PlayerControl::onSetCurShortcut(int i)
{
	PlayerAttrib *attr = getPlayerAttrib();
	if (attr->m_CurShotcut != i)
		ClientPlayer::onSetCurShortcut(i);

	int itemid = getCurToolID();
	//Select different grid
//	if(attr->m_CurShotcut != i)
//	{
		//m_StateController->onSwitchThingInHand();
//	}
	bool toolchange = (attr->m_CurShotcut != i || m_lastToolId != itemid);

	updateFishUp();
	
	BackPackGrid* itemgrid = attr->getEquipGrid(EQUIP_WEAPON);
	//if(m_lastToolId != itemid || attr->m_CurShotcut != i)
	//	setOperate(PLAYEROP_NULL);
	if (m_pWorld)
	{
		//加入isFishNeedUp判断 by：Jeff
		if (itemid == ITEM_CRAB || itemid == ITEM_HIPPOCAMPUS || itemid == ITEM_JELLYFISH ||GetDefManagerProxy()->isFishNeedUp(itemid))
		{
			this->playAnim(SEQ_CARRYING);
		}
		else
		{
			this->stopAnim(SEQ_CARRYING);
		}

	}
	if (m_pWorld && (itemid == ITEM_COLORED_GUN || itemid == ITEM_COLORED_EGG || itemid == ITEM_COLORED_EGG_SMALL || itemid == ITEM_COLOR_BRUSH))
	{
		if (GetWorldManagerPtr()->isGameMakerRunMode() && GetWorldManagerPtr()->m_RuleMgr && getTeam())
		{
			setSelectedColor(GetTeamColor(getTeam()));
		}
		MINIW::ScriptVM::game()->callFunction("SetGunMagazine", "ii", 0, 0);
	}
	//创造模式和编辑模式，染色方块可以变色 by：Jeff
	if (m_pWorld && (g_WorldMgr->isCreativeMode() || g_WorldMgr->isGameMakerMode()) && IsDyeableBlock(itemid))
	{
		unsigned int color = 0;
		int colordata = 0;
		sscanf(itemgrid->getUserdataStr().c_str(), "%d", &colordata);
		MINIW::ScriptVM::game()->callFunction("GetColorFromBlockInfo", "ii>i", itemid, colordata, &color);
		setSelectedColor(color);
		MINIW::ScriptVM::game()->callFunction("SetGunMagazine", "ii", 0, 0);
	}

	//switch tool in hand
	if (toolchange)
	{
		m_pCamera->trigerFPSHandAnimation();
		m_lastToolId = itemid;
		m_PlayerAnimation->performIdle();
	}
	//switchCurrentItem();

	//ge GetGameEventQue().postShortcutSelected(i);
	MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
		SetData_Number("selectgrid", i);
	if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
		MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_SHORTCUT_SELECTED", sandboxContext);
	}

	if (PackingFullyCustomModelMgr::GetInstancePtr())
	{
		PackingFullyCustomModelMgr::GetInstancePtr()->onShortcutChange(itemid);
	}

	if (GetIdleStateGunAdvance())
		GetIdleStateGunAdvance()->TryResetHandleEquip(true);
}

void PlayerControl::setCurShortcut(int i)
{
	if (MNSandbox::IMiniDeveloperProxy::getMiniDeveloperProxy()->GetTriggerOperateAttr(g_pPlayerCtrl->getUin(), 1)) //SwitchShortcut 
	{
		return;
	}
	if (isShapeShift())  //����״̬�²����л������
		return;

	auto CarryComp = getCarryComponent();
	if (CarryComp && CarryComp->isCarrying())	//����״̬�²����л������
		return;
	if (isRidingByHippocompus())
		return;      //骑乘海马的状态下不能切换快捷栏

	if (g_WorldMgr && g_WorldMgr->isUGCEditBuildMode())
	{
		if (m_enableSetShortcut)
			onSetCurShortcut(i);
		else
		{
			MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).SetData_Number("selectgrid", i);
			if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) 
			{
				MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_SHORTCUT_SELECTED", sandboxContext);
			}
		}
		m_cacheCurShotcut = i;
		return;
	}

	if (i < 0)
	{
		i = 7;
	}
	else if (i > 7)
	{
		i = 0;
	}
	if (m_enableSetShortcut)
	{
		onSetCurShortcut(i);
		selectShortcutOnTrigger(i);
	}
	else
	{
		MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).SetData_Number("selectgrid", i);
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) 
		{
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_SHORTCUT_SELECTED", sandboxContext);
		}
	}
	m_cacheCurShotcut = i;
}

void PlayerControl::EnableSetCurShortcut(bool b)
{
	m_enableSetShortcut = b;
}

void PlayerControl::RefreshCurShortcut()
{
	int curShotcut = getCurShortcut();
	if (m_enableSetShortcut && m_cacheCurShotcut >= 0 && curShotcut >= 0 && m_cacheCurShotcut != curShotcut)
	{
		setCurShortcut(m_cacheCurShotcut);
	}
}

void PlayerControl::onSetCurSprayPaint(bool isNext)
{
	MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_SPRAY_PAINT", MNSandbox::SandboxContext(nullptr).SetData_Bool("isNext", isNext));
}

void PlayerControl::setCurSprayPaint(bool isNext)
{
	onSetCurSprayPaint(isNext);
}

ClientActor *PlayerControl::spawnItem(int itemid, int num, int posx, int posy, int posz)
{
	BackPackGrid grid;
	SetBackPackGrid(grid, itemid, num);

	ClientItem *item = SANDBOX_NEW(ClientItem, grid);
	item->getLocoMotion()->gotoPosition(WCoord(posx, posy, posz), 0, 0);
	getActorMgr()->spawnActor(item);
	if (GetWorldManagerPtr()->isGodMode()) item->m_LiveTicks = 4800;

	return item;
}

bool PlayerControl::isCurToolUnlocked(int toolid)
{
	//#ifdef IWORLD_DEV_BUILD
	//	return true;
	//#endif
	if (toolid < 0) toolid = getCurToolID();
	if (toolid == 0) return true;
	if (toolid == 12280 || toolid == 12281 || toolid >= 12283 && toolid <= 12285) return true;

	const ItemDef *def = GetDefManagerProxy()->getItemDef(toolid);
	if (def && def->UnlockFlag != 0)
	{
		bool unlocked = false; //�Ƿ������
		bool hasItemUnlockedInfo = false; //�Ƿ��Ѿ����ؽ���������
		int tipsId = 268;
		MINIW::ScriptVM::game()->callFunction("isItemUnlockByItemId", "i>bbi", def->ID, &unlocked, &hasItemUnlockedInfo, &tipsId);
		if (unlocked)
		{
			return true;
		}
		else
		{
			int stringId = 268;
			if (!hasItemUnlockedInfo)
				stringId = tipsId;

			//ge GetGameEventQue().postInfoTips(stringId);
			CommonUtil::GetInstance().PostInfoTips(stringId);
			return false;
		}
	}

	return true;
}

void PlayerControl::switchCurrentItem()
{
	int itemid = 0;
	//�Ǳ���״̬
	if (isInSpectatorMode() && getSpectatorType() == SPECTATOR_TYPE_FOLLW && getToSpectatorPlayer())
	{
		PlayerAttrib *attr = getToSpectatorPlayer()->getPlayerAttrib();
		itemid = attr->getEquipItem(EQUIP_WEAPON);
		auto RidComp = getRiddenComponent();
		if (RidComp && RidComp->isVehicleController()) itemid = 0;
		BackPackGrid *itemgrid = attr->getEquipGrid(EQUIP_WEAPON);
		if (m_CameraModel){
			m_CameraModel->setCurTool_byGrid(itemid, NULL, itemgrid);
			if (!canShowCameraModel())
				m_CameraModel->show(false);
		}
		getPlayerAttrib()->ApplyWeaponEnchantEffect(getBody());
	}
	else if (getBody()->getMutateMob() <= 0)
	{
		PlayerAttrib *attr = getPlayerAttrib();
		itemid = getOPWay() != PLAYEROP_WAY_FOOTBALLER ? attr->getEquipItem(EQUIP_WEAPON) : 0;
		auto RidComp = getRiddenComponent();
		if (RidComp && RidComp->isVehicleController() || getOPWay() == PLAYEROP_WAY_BASKETBALLER)itemid = 0;
		//if(g_pPlayerCtrl && g_pPlayerCtrl->isVehicleController())
			//itemid = 0;
		BackPackGrid *itemgrid = attr->getEquipGrid(EQUIP_WEAPON);
		if (m_CameraModel) {
			m_CameraModel->setCurTool_byGrid(itemid, NULL, itemgrid);
			if (!canShowCameraModel())
				m_CameraModel->show(false);
		}
		getPlayerAttrib()->ApplyWeaponEnchantEffect(getBody());
	}

	const ToolDef* tooldef = GetDefManagerProxy()->getToolDef(itemid);
	if (tooldef && tooldef->IsModFishRod() && canShowCameraModel())
	{
		OnChangeTool(true);
	}
	else
	{
		OnChangeTool(false);
	}
}

void PlayerControl::resetHandModel()
{
	//�Ǳ���״̬
	if (isInSpectatorMode() && getSpectatorType() == SPECTATOR_TYPE_FOLLW && getToSpectatorPlayer())
	{
		PlayerAttrib *attr = getToSpectatorPlayer()->getPlayerAttrib();
		if (attr)
		{
			int itemid = attr->getEquipItem(EQUIP_WEAPON);
			BackPackGrid *itemgrid = attr->getEquipGrid(EQUIP_WEAPON);
			if (itemgrid)
			{
				m_CameraModel->setCurTool_byGrid(itemid, NULL, itemgrid);
				getPlayerAttrib()->ApplyWeaponEnchantEffect(getBody());
			}
		}

		//ˢ��װ��ģ��
		for (int i = 0; i < EQUIP_WEAPON; ++i)
		{
			int itemid = getPlayerAttrib()->getEquipItem((EQUIP_SLOT_TYPE)i);
			if (itemid > 0)
				applyEquips((EQUIP_SLOT_TYPE)i);
		}
	}
	else if (getBody()->getMutateMob() <= 0)
	{
		PlayerAttrib *attr = getPlayerAttrib();
		if (!attr)
			return;

		int itemid = getOPWay() != PLAYEROP_WAY_FOOTBALLER ? attr->getEquipItem(EQUIP_WEAPON) : 0;
		if (getOPWay() == PLAYEROP_WAY_BASKETBALLER)
			itemid = 0;

		BackPackGrid *itemgrid = attr->getEquipGrid(EQUIP_WEAPON);
		if (itemgrid)
		{
			m_CameraModel->setCurTool_byGrid(itemid, NULL, itemgrid);
			getPlayerAttrib()->ApplyWeaponEnchantEffect(getBody());
		}

		//ˢ��װ��ģ��
		for (int i = 0; i < EQUIP_WEAPON; ++i)
		{
			int itemid = getPlayerAttrib()->getEquipItem((EQUIP_SLOT_TYPE)i);
			if (itemid > 0)
				applyEquips((EQUIP_SLOT_TYPE)i);
		}
	}
}

void PlayerControl::setItem(int itemid, int toindex, int num, const char *sid_str/*= ""*/)
{
	return getBackPack()->setItem(itemid, toindex, num, sid_str);
}

void PlayerControl::setItemWithoutLimit(int itemid, int toindex, int num, const char *userdata_str, const char *sid_str /*= ""*/)
{
	return getBackPack()->setItemWithoutLimit(itemid, toindex, num, userdata_str, sid_str);
}

void PlayerControl::setDyeableItem(int itemid, int toIndex, int num, const char* userdata_str)
{
	if (!IsDyeableBlock(itemid))
		return;
	getBackPack()->setItemWithoutLimit(itemid, toIndex, num, userdata_str);
}

void PlayerControl::moveItem(int fromindex, int toindex, int num)
{
	BackPackGrid* fromgrid = getBackPack()->index2Grid(fromindex);
	//埋点
	TrackMove(fromindex, toindex, fromgrid->getItemID() , num);

	if (num <= 0) getBackPack()->shiftMoveItem(fromindex, toindex);
	else
	{
		getBackPack()->moveItem(fromindex, toindex, num);
		//if (fromindex == CUSTOMMODEL_START_INDEX + 1)
			//statisticToWorld(getUin(), 30016, "", getCurWorldType());
		if (fromindex >= STORAGE_START_INDEX && fromindex < CRAFT_START_INDEX) //存储箱的item被拿
			GetWorldManagerPtr()->getWorldInfoManager()->takeItemFormContainerNoticeActorVillager(m_pWorld, this, getCurOpenedContainerPos());
	}

}

void PlayerControl::swapItem(int fromIndex, int toIndex)
{
	BackPack *bp = getBackPack();
	if (bp == NULL) return;
	if (isCurToolUnlocked(bp->getGridItem(fromIndex)) && isCurToolUnlocked(bp->getGridItem(toIndex)))
	{
		bp->swapItem(fromIndex, toIndex);

		if (toIndex == CUSTOMMODEL_START_INDEX)
		{
			ContainerModelCraft *destcontainer = dynamic_cast<ContainerModelCraft *>(bp->getContainer(toIndex));
			if (destcontainer)
			{

				int modelType = destcontainer->getModelType();
				//if (modelType == BLOCK_MODEL)
					//statisticToWorld(getUin(), 30014, "", g_pPlayerCtrl->getCurWorldType(), "common_front", "blockcraft");
				//else if (modelType > BLOCK_MODEL && modelType <= BOW_MODEL)
					//statisticToWorld(getUin(), 30014, "", g_pPlayerCtrl->getCurWorldType(), "common_front", "itemcraft");
			}
		}
	}
}

void PlayerControl::mergeItem(int fromIndex, int toIndex)
{
	if (!getBackPack()->mergeItem(fromIndex, toIndex))
	{
		swapItem(fromIndex, toIndex);
	}
}

void PlayerControl::discardItem(int index, int num)
{
	//getBackPack()->discardItem(index, num);
	ClientPlayer::discardItem(index, num);
}

void PlayerControl::sortPack(int base_index)
{
	ClientPlayer::sortPack(base_index);

	//ge GetGameEventQue().postBackpackChange(-1);
	MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
		SetData_Number("grid_index", -1);
	if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
		MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_BACKPACK_CHANGE", sandboxContext);
	}
}

void PlayerControl::sortStorageBox()
{
	ClientPlayer::sortStorageBox();
}

void PlayerControl::craftItem(int craftid, int num)
{
	MNSandbox::GetGlobalEvent().Emit<const int, const int>("StatisticRainforest_onCondition_GetProp", craftid, getUin());
	getBackPack()->doCrafting(craftid, NULL, num);
}

void PlayerControl::AddToCraftingQueue(int craftId, int num)
{
	// 检查是否可以添加到制作队列
	if (craftId <= 0 || num <= 0) {
		return; // 无效的参数
	}
	const CraftingDef* craDef = GetDefManagerProxy()->getCraftingDef(craftId);
	int timeTick = craDef->CookingTick;
	if (0== timeTick)
	{ 
		timeTick = 400;//todo  //配置0 改成400
	}
	getCraftingQueue()->addTask(craftId, num, timeTick);
}

void PlayerControl::RemoveFromCraftingQueue(int index) {
	// 检查 index 是否有效
	if (index < 0) {
		return; // 无效的 index
	}

	getCraftingQueue()->removeTask(index); // 从制作队列中删除任务
}

int PlayerControl::repair(int tgtGridIdx)
{
	int ret = ClientPlayer::repair(tgtGridIdx);
	if (ret >= 0)
	{
		//ge GetGameEventQue().postRepairResult(tgtGridIdx);
		MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
			SetData_Number("gridindex", tgtGridIdx);
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_REPAIR_RESULT", sandboxContext);
		}
	}
	return ret;
}

int PlayerControl::enchant(int tgtGridIdx, int frmGridIdx, int enchants[MAX_ITEM_ENCHANTS])
{
	int ret = ClientPlayer::enchant(tgtGridIdx, frmGridIdx, enchants);
	if (ret >= 0)
	{
		//ge GetGameEventQue().postEnchantResult(ret);
		MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
			SetData_Number("gridindex", ret);
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_ENCHANT_RESULT", sandboxContext);
	}
	return ret;
}

int PlayerControl::enchantRandom(int tgtGridIdx)
{
	int ret = ClientPlayer::enchantRandom(tgtGridIdx);
	if (ret >= 0)
	{
		//ge GetGameEventQue().postEnchantResult(ret);
		MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
			SetData_Number("gridindex", ret);
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_ENCHANT_RESULT", sandboxContext);
	}
	return ret;
}

bool PlayerControl::isUnlockItemById(int itemid)
{
	auto itemDef = GetDefManagerProxy()->getItemDef(itemid);
	if (itemDef && itemDef->CondUnlcokType > 0 && !GetWorldManagerPtr()->isUnlockItem(itemDef->CondUnlcokType)) return false;
	if (itemDef && itemDef->CondUnlcokType == -1 && !isUnlockItem(itemDef->ID)) return false;

	return true;
}

void PlayerControl::setNpcShopInfo(int shopid)
{
	if (GetWorldManagerPtr())
	{
		GetWorldManagerPtr()->getWorldInfoManager()->setNpcShopInfo(shopid);
		m_stCurNpcShopInfo = *GetWorldManagerPtr()->getWorldInfoManager()->getCurShopInfo();
	}
}

int PlayerControl::getNpcShopInfo(int shopid, PB_NpcShopData* npcshopdata)
{
	setNpcShopInfo(shopid);
	if (shopid != m_stCurNpcShopInfo.iShopID) {
		return -1;
	}

	npcshopdata->set_shopid(shopid);
	npcshopdata->set_shopname(m_stCurNpcShopInfo.sShopName);
	npcshopdata->set_shopdesc(m_stCurNpcShopInfo.sShopDesc);
	npcshopdata->set_innerkey(m_stCurNpcShopInfo.sInnerKey);

	for (int i = 0; i < (int)m_stCurNpcShopInfo.vShopItemList.size(); i++) {
		auto pSkuInfo = npcshopdata->add_shopitemdata();

		pSkuInfo->set_skuid(m_stCurNpcShopInfo.vShopItemList[i].iSkuID);
		pSkuInfo->set_itemid(m_stCurNpcShopInfo.vShopItemList[i].iItemID);
		pSkuInfo->set_oncebuynum(m_stCurNpcShopInfo.vShopItemList[i].iOnceBuyNum);
		pSkuInfo->set_maxcanbuycount(m_stCurNpcShopInfo.vShopItemList[i].iMaxCanBuyCount);
		pSkuInfo->set_refreshduration(m_stCurNpcShopInfo.vShopItemList[i].iRefreshDuration);
		pSkuInfo->set_starnum(m_stCurNpcShopInfo.vShopItemList[i].iStarNum);
		pSkuInfo->set_costiteminfo1(m_stCurNpcShopInfo.vShopItemList[i].iCostItemInfo1);
		pSkuInfo->set_costiteminfo2(m_stCurNpcShopInfo.vShopItemList[i].iCostItemInfo2);
		pSkuInfo->set_leftcount(m_stCurNpcShopInfo.vShopItemList[i].iLeftCount);
		pSkuInfo->set_endtime(m_stCurNpcShopInfo.vShopItemList[i].iEndTime);
	}

	return 0;
}

int PlayerControl::buyNpcShopSku(int shopid, int skuid, int buycount, NpcShopInfo& skuinfo)
{
	if (GetWorldManagerPtr())
	{
		return GetWorldManagerPtr()->getWorldInfoManager()->buyNpcShopSku(shopid, skuid, buycount, skuinfo);
	}
	return 0;
}

void PlayerControl::respNpcShopInfo(const PB_NpcShopData& npcshopdata)
{
	m_stCurNpcShopInfo.iShopID = npcshopdata.shopid();
	m_stCurNpcShopInfo.sShopName = npcshopdata.shopname();
	m_stCurNpcShopInfo.sShopDesc = npcshopdata.shopdesc();
	m_stCurNpcShopInfo.sInnerKey = npcshopdata.innerkey();

	m_stCurNpcShopInfo.vShopItemList.clear();
	for (int i = 0; i < npcshopdata.shopitemdata_size(); i++) {
		NpcShopItemDef stSkuInfo;
		const PB_NpcShopItemData& skudata = npcshopdata.shopitemdata(i);
		stSkuInfo.iSkuID = skudata.skuid();
		stSkuInfo.iItemID = skudata.itemid();
		stSkuInfo.iOnceBuyNum = skudata.oncebuynum();
		stSkuInfo.iMaxCanBuyCount = skudata.maxcanbuycount();
		stSkuInfo.iRefreshDuration = skudata.refreshduration();
		stSkuInfo.iStarNum = skudata.starnum();
		stSkuInfo.iCostItemInfo1 = skudata.costiteminfo1();
		stSkuInfo.iCostItemInfo2 = skudata.costiteminfo2();
		stSkuInfo.iLeftCount = skudata.leftcount();
		stSkuInfo.iEndTime = skudata.endtime();

		m_stCurNpcShopInfo.vShopItemList.push_back(stSkuInfo);
	}

	//notify lua ui get npc shop info
	//ge GetGameEventQue().postSimpleEvent("GE_NPCSHOP_OPEN");
	if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
		MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_NPCSHOP_OPEN", MNSandbox::SandboxContext(nullptr));
}

void PlayerControl::updateNpcShopSkuInfo(int shopid, int skuid, int leftcount, int endtime, int buycount, bool isSuccess)
{
	if (m_stCurNpcShopInfo.iShopID != shopid) { return; }

	NpcShopItemDef* pSkuInfo = m_stCurNpcShopInfo.getNpcShopSkuDef(skuid);
	if (!pSkuInfo) { return; }
	pSkuInfo->iLeftCount = leftcount;
	pSkuInfo->iEndTime = endtime;

	//ge GetGameEventQue().postEventResult("GE_NPCSHOP_REFRESHSHOP", isSuccess ? 0 : 1);
	MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).SetData_Number("result", isSuccess ? 0 : 1);
	if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
	{
		MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_NPCSHOP_REFRESHSHOP", sandboxContext);
	}
}

void PlayerControl::reqGetNpcShopInfo(int shopid)
{
	if (isHost()) {
		//notify to lua ui
		setNpcShopInfo(shopid);
		//ge GetGameEventQue().postSimpleEvent("GE_NPCSHOP_OPEN");
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_NPCSHOP_OPEN", MNSandbox::SandboxContext(nullptr));
	}
	else if (m_pWorld->isRemoteMode()) {
		PB_GetNpcShopInfoCH reqDataCH;
		reqDataCH.set_shopid(shopid);

		GetGameNetManagerPtr()->sendToHost(PB_NPCSHOP_GETSHOPINFO_CH, reqDataCH);
	}
}

void PlayerControl::reqBuyNpcShopSku(int shopid, int skuid, int buycount)
{
	if (isHost()) {
		NpcShopInfo skuInfo;
		int iRet = buyNpcShopSku(shopid, skuid, buycount, skuInfo);
		if (iRet == 0)
		{
			iRet = updateNpcShopItemChange(shopid, skuid, buycount);
		}
		updateNpcShopSkuInfo(shopid, skuid, skuInfo.iLeftCount, skuInfo.iEndTime, buycount, iRet == 0);
	}
	else if (m_pWorld && m_pWorld->isRemoteMode()) {
		PB_BuyNpcShopItemCH reqDataCH;
		reqDataCH.set_shopid(shopid);
		reqDataCH.set_skuid(skuid);
		reqDataCH.set_buycount(buycount);

		if (GetGameNetManagerPtr()) {
			GetGameNetManagerPtr()->sendToHost(PB_NPCSHOP_BUYSKU_CH, reqDataCH);
		}
	}
}
