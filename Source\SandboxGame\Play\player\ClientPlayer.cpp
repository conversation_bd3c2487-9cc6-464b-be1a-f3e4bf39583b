#include "ClientPlayer.h"
#include "backpack.h"
#include "CraftingQueue.h"
#include "PlayerLocoMotion.h"
#include "ActorDungeonEye.h"
#include "ActorFirework.h"
#include "ActorCSProto.h"
#include "special_blockid.h"
#include "GameMode.h"
#include "BlockBed.h"
#include "ActorIslandBusinessman.h"

#include "ClientItem.h"
#include "ActorDesertVillager.h"
#include "ActorRocket.h"
#include "TransferMgr.h"
#include "PackingFullyCustomModelMgr.h"
#include "IClientGameManagerInterface.h"
#include "OgreMD5.h"
#include "MpActorManager.h"
#include "GameNetManager.h"
#include "MpActorTrackerEntry.h"
#include "GameCamera.h"
#include "PermitsSubSystem.h"

#include "Graphics/LegacyGlobalShaderParam.h"
#include "PlayerStateController.h"
#include "ActorBall.h"
#include "SandBoxManager.h"
#include "IRecordInterface.h"
#include "IRecordInterface.h"
#include "PlayerTaskManager.h"
#include "CameraModel.h"
#include "ObserverEventManager.h"
#include "MotionControl.h"
#include "VehicleMgr.h"
#include "TransferMgr.h"
#include "ActorBasketBall.h"
#include "container_starstationtransfercabin.h"
#include "container_giantscallops.h"
#include "StarStationTransferMgr.h"
#include "ActorVillager.h"
#include "Entity/OgreEntity.h"
#include "ActorInPortal.h"
#include "ActorBindVehicle.h"
#include "TotemComponent.h"
#include "AttackingTargetComponent.h"
#include "HPProgressComponent.h"
#include "TeamComponent.h"
#include "VehicleWorld.h"
#include "BlockPiano.h"
#include "OpenContainerComponent.h"
#include "SkillCDComponent.h"
#include "ItemSkillComponent.h"
#include "AccountHorseComponent.h"
#include "PetFollowListComponent.h"
#include "InteractTamedMobComponent.h"
#include "PetAccountComponent.h"
#include "PetSummonComponent.h"
#include "RadiationComponent.h"
#include "ChangeColorComponent.h"
#include "TransformersSkinComponent.h"
#include "WeaponSkilledComponent.h"
#include "AvatarSummonComponent.h"
#include "WeaponSkinMgr.h"
#include "StatisticTerrgenInterface.h"
#include "GameStatisticInterface.h"
#include "ActorFollow.h"
#include "PlayerAttrib.h"
#include "RevivePointComponent.h"
#include "CosumeAccumComponent.h"
#include "InWaterComponent.h"
#include "PlayerCheat.h"
#include "ClientInfoProxy.h"
#include "ActorCrab.h"
#include "ClientActorFuncWrapper.h"
#include "RiddenComponent.h"
#include "CarryComponent.h"
#include "SoundComponent.h"
#include "ActionAttrStateComponent.h"
#include "ParticlesComponent.h"
#include "AttackedComponent.h"
#include "FallComponent.h"
#include "BindActorComponent.h"
#include "TriggerComponent.h"
#include "ClientActorHelper.h"
#include "DigState.h"
#include "AdvancedDigState.h"
#include "SleepState.h"
#include "SitState.h"
#include "GameInfoProxy.h"

#include "CommonUtil.h"
#include "EffectComponent.h"
#include "FindComponent.h"
#include "ToAttackTargetComponent.h"
#include "Core/worldMesh/MiniCraftRenderer.h"
#include "SandboxCoreSubsystem.h"
#include "ClientActorThornBall.h"
#include "ThornBallComponent.h"
//#include "ClientAccount.h"
#include "ICloudProxy.h"
#include "WorldStringManagerProxy.h"
#include "FishingComponent.h"
#include "File/FileManager.h"
#include "BlockMaterialMgr.h"
#include "OgreSoundSystem.h"
#include "GameEffectManager.h"
#include "navigationpath.h"
#include "ClientActorIcicle.h"
#include "ActorVehicleAssemble.h"
#include "BaseItemMesh.h"
#include "CustomModelMgr.h"
#include "ActorBody.h"
#include "world.h"
#include "PlayerControl.h"
#include "ActorHorse.h"
#include "ChunkGenerator.h"
#include "OgreUtils.h"
#include "LuaInterfaceProxy.h"
#include "world.h"
#include "chunkio.h"
#include "EffectDestroyBlock.h"
#include "BlockScene.h"
#include "GameStatic.h"
#include "GameAnalytics.h"
#ifdef IWORLD_SERVER_BUILD
#include "ZmqProxy.h"
#include "game_event.pb.h"
#include "container_backpack.h"
#include "google/protobuf/message.h"
#include "proto_gs2ds.pb.h"
#endif
#include "ActorGeniusMgr.h"
#include "ClientErrCode.h"
//#include "Achievementmanager.h"
#include "WorldStringManagerProxy.h"
#include "UIActorBodyMgr.h"
#include "MiniReportMgrProxy.h"
#include "IMiniDeveloperProxy.h"
//#include "TaskSubSystem.h"
#include "TemperatureComponent.h"
#include "SwarmComponent.h"
#include "MusicManager.h"
#include "ViewerComponentActor.h"
#include "container_manualEmitter.h"
#include "MoveControl.h"
#include "SkillComponent.h"
#include "ItemUseComponent.h"
#include "GunUseComponent.h"
#include "CustomGunUseComponent.h"
#include "ChargeJumpComponent.h"
#include "VacantEffectComponent.h"
#include "SocRevivePointComponent.h"
#include "LockCtrlComponent.h"
#include "FishLineComponent.h"
#include "ActorPlayerCorpse.h"
#include "thinkingdata/GameAnalytics.h"
#include "container_sandboxGame.h"

#if defined(BUILD_MINI_EDITOR_APP) || (OGRE_PLATFORM == OGRE_PLATFORM_WIN32 && defined(STUDIO_SERVER))
#include "SandboxChatService.h"
#endif
#include "InputInfo.h"

#include "PhysicsComponent.h"

#if OGRE_PLATFORM != OGRE_PLATFORM_WIN32
#define O_BINARY (0)
#endif



#ifdef OPEN_CLOUD_VIEW_RANGE
#include "SandboxGameSetNode.h"
#endif

#include "BackPackStringRecorder.h"
#include "ActorBodySafeHandle.h"
#include "ModEntryMgr.h"
#include "ModPackMgr.h"
#include "GunGridDataComponent.h"
#include "CameraManager.h"

using namespace MINIW;
using namespace MNSandbox;
using namespace Rainbow;

const int STORE_ROLEDATA_TICK = 6 * 20;
const int ARCH_DATA_SAVE_TICK = 20 * 60; //60秒

int ClientPlayer::m_ViewRangeSetting = 2;

int ClientPlayer::m_ViewInnerRangeOffset = 0;

#define MAX_TAMEDMON_FOLLOWS_LENGTH 5

EXPORT_SANDBOXENGINE MNSandbox::Notify<int>& GetNotifyPlayerViewRange();

int getActionType(const std::string& szType)
{
	auto pLua = MINIW::ScriptVM::game()->getLuaState();
	if (!ScriptVM::isCurrentThreadIsMainThread(pLua))
	{
		return 0;
	}
	int t = lua_gettop(pLua);
	lua_getglobal(pLua, "ActionType");
	lua_pushstring(pLua, szType.c_str());
	lua_gettable(pLua, -2);
	int type = lua_tonumber(pLua, -1);
	lua_settop(pLua, t);
	return type;
}

std::map<ClientPlayer*, int> ClientPlayer::gDumpCatch;

void ClientPlayer::SetViewRangeSetting(int range)
{
	ClientPlayer::m_ViewRangeSetting = range;

	GetNotifyPlayerViewRange().Emit(GetCurViewRange(nullptr));
}

int ClientPlayer::GetCurViewRange(ClientPlayer* player)
{
	//这个player参数只是为了兼容player->getCurViewRange()
#ifndef DEDICATED_SERVER
	int viewRange = m_ViewRangeSetting;
#else
	int viewRange = Rainbow::GetICloudProxyPtr()->GetCloudViewRange();
#endif
	if (player)
	{
		viewRange = player->getCurViewRange();
	}
#ifdef OPEN_CLOUD_VIEW_RANGE
	if (Config::GetSingleton().IsSandboxMode())
	{
		auto gamesetNode = GetCurrentGameSetNode();
		if (gamesetNode)
		{
#ifdef DEDICATED_SERVER
			if (!player)
			{
				viewRange = 0;
			}
#endif
			viewRange = gamesetNode->GetCurGameViewRange(viewRange);
		}
	}
#endif // OPEN_CLOUD_VIEW_RANGE

	return viewRange;
}


IMPLEMENT_SCENEOBJECTCLASS(ClientPlayer)

ClientPlayer::ClientPlayer() : m_LastStoreTick(0), m_RegionStartPos(0, -1, 0), m_CurPlaySnd(NULL),
m_UIControlMode(0), m_MotionCtrl(this), m_CanControl(true), m_MaxLifeNum(0), m_CurDigBlockID(0), m_checkTime(0), m_SpeedUpTimes(1), m_PlayerStateMgr(this)
, m_NewPlayerFlag(true), m_curInteractPlotType(0), m_curStealWid(NULL), m_isStarStationTeleporting(false), m_isExploiting(false), m_clampId(0)
, m_crabClickCount(0), m_attribDirty(false), m_battleHornVillager(false)
, m_pBindPlatform(nullptr), m_attrViewRange(0), m_needSaveTechTree(false)
, m_needSaveUserData(false), m_MoveForward(0.0f), m_MoveRight(0.0f), m_MoveControl(NULL), m_GunHoleState(GunHoldState::NOGUN)//持枪状态
, m_pItemComponent(nullptr), m_pGunComponent(nullptr), m_pCustomGunComponent(nullptr), m_pActionIdleStateGunAdvance(nullptr)
, m_SkillCDComponent(nullptr),m_TransformerSkinComponent(nullptr), m_VacantEffectComponent(nullptr), m_TemperatureComponent(nullptr)
, m_RadiationComponent(nullptr), m_pFishLineComponent(nullptr),m_GmFlySpeed(0), m_isSkinning(false)
{
#ifdef IWORLD_SERVER_BUILD
	init_failed = false;
#endif

	gDumpCatch[this] = 1;
	m_CurDigVehicleID = 0;
	m_LoginNum = 1;
	curShowDialogId = 0;
	m_SleepState = nullptr;
	//m_SpawnPoint = WCoord(0, -1, 0); //个人复活重生点
	//m_RevivePoint = WCoord(0, -1, 0);
	//m_TriggerRevivePoint = WCoord(0, -1, 0);
	//m_SpawnForced = false;
	//m_bFirstPlayReviveEffect = false;
	setOperate(PLAYEROP_NULL);

	m_surviveDays = -1;
	m_suriveDaysByWorld = -1;
	m_GameScore = 0;
	m_GameRanking = 0;
	m_GameResults = 0;
	m_CWKills = 0;
	m_Kills = 0;
	m_DieTimes = 0;
	m_PlayerStartTimes = 0;
	m_socAttackInfo.clean();
	m_RangeAttackPower = 1;
	auto functionWrapper = getFuncWrapper();
	if (functionWrapper)
	{
		functionWrapper->setJetpackFlying(false);
	}
	//m_JetpackFlying = false;
	m_SnakeGodWingFlying = false;
	//m_JetpackCosumeAccum = 0;
	//m_OxypackCosumeAccum = 0;
	//m_SnakeGodWingCosumeAccum = 0;

	m_LastTriggerBlock = WCoord(0, -1, 0);

	m_TestItemIconIndex = 0;

	m_CurPlaySndToolID = 0;
	m_fSpecSpeed = 0.0f;

	m_SelectedColor = -1; //默认彩色
	m_SuspicionValue = 0;
	m_LastClientInputMotionY = MAX_INT;
	m_nSpectatormode = SPECTATOR_MODE_NONE;
	m_nSpectatortype = SPECTATOR_TYPE_FREE;
	m_nSpectatorUin = 0;
	m_nToSpectatorUin = 0;


	m_nPosYPos = 0.0f;
	m_nPosYNeg = 0.0f;
	m_nPosXZ = 0.0f;
	m_nGuardTick = 0;
	m_bGuardError = false;
	m_bPreOnGround = false;
	m_bOffGrounding = false;
	m_nQuickSpeedCount = 0;
	m_nReciveCount = 0;
	m_OPWay = PLAYEROP_WAY_NORMAL;
	m_nFreezingFlag = FREEZING_STATE_CLEAN;
	m_RocketTeleport = false;
	m_bHookObj = 0;
	m_IsUseHearth = false;
	m_UsingHearthPos = WCoord(0, 0, 0);
	m_nSkinActPartnerUin = 0; //******** codeby：chenwei 舞伴uin

	for (int i = 0; i < MAX_MAP; i++)
	{
		m_WorldTimes[i] = 0;
		m_LandingPoints[i] = WCoord(0, -1, 0);
	}

	m_pTaskMgr = ENG_NEW(PlayerTaskManager)(this);

	m_OpenDialogueMobID = 0;
	//m_BlueprintTicks = 0;

	m_CurSyncCustomModelIndex = 0;
	m_SyncCustomModelTick = 0;

	m_CurSynTransferIndex = 0;
	m_SynTransferTick = 0;

	m_UsePhysics = false;

	m_WeaponMotionStatus = 0;
	m_WeaponMotionName = "";
	m_iInVehicleWorkShop = 0;

	m_MotionState = 0;
	m_helloTick = 0;
	m_clicktimes = 0;
	m_KillScorpionCount = 0;

	m_BoundHeight = 180;
	m_BoundWidth = 60;
	m_AttackBoundHeight = 200;
	m_AttackBoundWidth = 200;
	m_AttackBoundThickness = 200;
	m_TriggerSounder = NULL;
	m_PickItemLastIndex = 0;
	m_strCustomModel.clear();
	m_fCustomModelScale = 1.0;
	m_IsSkipNight = false;
	m_SkipNightTime = 0;
	m_TickInDay = 0;
	m_TickInHit = 0;
	m_IsCoconutHit = false;
	m_InteractPoseidonStatue = true;
	m_InteractPoseidonStatueTime = -1;
	m_isInPierceMode = false;
	m_isOpenPierceViewBright = false;

	// 注册动作回调
	int sttype[PLAYERSTTYPE_MAX];
	memset(sttype, 0, sizeof(sttype));
	sttype[PLAYERSTTYPE_STOP] = MOTION_STATIC + 1;
	sttype[PLAYERSTTYPE_MOVE] = MOTION_MOVE + 1;
	sttype[PLAYERSTTYPE_RUN] = MOTION_RUN + 1;
	sttype[PLAYERSTTYPE_JUMPFIRST] = MOTION_JUMP + 1; // PLAYERSTTYPE_JUMP
	sttype[PLAYERSTTYPE_JUMPSECOND] = MOTION_TWOJUMP + 1;
	sttype[PLAYERSTTYPE_SNEAK] = MOTION_SNEAK + 1;
	sttype[PLAYERSTTYPE_FALLGROUND] = MOTION_FALLGROUND + 1;
	for (int i = 0; i < PLAYERSTTYPE_MAX; i++)
	{
		if (sttype[i] == 0)
			continue;

		MotionState val = (MotionState)(sttype[i] - 1);
		auto funcListen = [this, val]() {
			this->motionStateChangeOnTrigger(val);
		};
		m_PlayerStateMgr.regListen((PLAYERST_TYPE)i, funcListen);
	}
	isSpringMove = false;
	m_checkboxscale = 1;
	m_bLoadSpeedUp = false;
	m_nLang = 0;
	m_nApiid = 999;
	m_nEnterTs = 0;
	//m_nBuffCheckTime = 0;
	m_CurSynStarStationIndex = 0;
	m_SynStarStationTick = 0;

	m_suddenIllnessBuffTraceTick = 0;
	m_isBuffChangeViewMode = false;
	//m_bInTransform = false;
	//m_iCheckDistanceTick = 0;
	//m_TransformReason = 0;
	//m_MainSkinPlayerID = 0;
	//m_CurSummonPetID = "";


	m_PlayerAttrib = NULL;
	//m_AccountWorldPoint.resize(0);
	//m_InteractSpBlockList.resize(0);
	m_usingPianoPos = WCoord(0, -1, 0);
	m_sitingPianoPos = WCoord(0, -1, 0);


	// ********：是否在音乐方块中  codeby： huangxin
	m_isInMusicClubArea = false;
	//音乐厅聊天气泡 2021-09-25 codeby: luoshuai
	m_showLimitTime = 5.0f;
	m_haveShowTime = 5.0f;
	m_chatContent = "";
	//********：是否在音乐方块中  codeby： huangxin
	m_isInMusicClubArea = false;
	m_treeItemIndex = 1;
	m_KillScorpionCount = 0;
	m_AttrRightClickTick = 0;
	m_AttrShapeShiftTick = 0;
	m_AttrStopRecoverTick = 0;
	//m_totemComp = new TotemComponent(this);
	//m_pSkillCDComp = new SkillCDComponent(this);
	//m_pPetFollowListComp = new PetFollowListComponent();
	//m_pPetAccountComp = new PetAccountComponent(this);
	//m_pChangeColorComp = new ChangeColorComponent(this);
	//m_pTransformersSkinComp = ENG_NEW(TransformersSkinComponent)(this);
	m_exposePos = true;
	m_CheatData = ENG_NEW(PlayerCheatData)(this);
	m_SaveSizeRecorded = 0;

	m_FlagBitChanged = 0xffffffff;  // 初始化时同步一次
	m_TipTime.clear();
	m_OldJumpCharge = -1;
	m_SyncCtrl = m_CanControl;
	m_gotoPosChunkXZ = WCoord(-1, -1, -1);
	createEvent();

	CreateComponent<ItemUseComponent>("ItemUseComponent");
	CreateComponent<GunUseComponent>("GunUseComponent");
	//CreateComponent<CustomGunUseComponent>("CustomGunUseComponent");
	//CreateComponent<CarryComponent>("CarryComponent");
	CreateComponent<RevivePointComponent>("RevivePointComponent");
	auto compAccum = CreateComponent<CosumeAccumComponent>("CosumeAccumComponent");
	if (compAccum)
	{
		compAccum->LuaPluginMgr().BindLuaPluginByFile("sandboxengine/sandboxengine/sandboxcore/components/CosumeAccumComponent.lua");
		compAccum->Event().Emit("OnInit");
	}
	//CreateComponent<InWaterComponent_Player>("InWaterComponent");
	auto compInWater = CreateComponent<InWaterComponent_Player>("InWaterComponent");
	if (compInWater)
	{
		compInWater->LuaPluginMgr().BindLuaPluginByFile("sandboxengine/sandboxengine/sandboxcore/components/InWaterComponent_player.lua");
		compInWater->Event().Emit("OnInit");
	}
	//replace component
	//CreateComponent<PlayerFollow>("Follow");
	CreateComponent<SkillComponent>("SkillComponent");

	//CreateComponent<AccountHorseComponent>("AccountHorseComponent");
	//RemoveComponent("ActorBindVehicle");

	//RemoveComponent("ActorInPortal");
	//CreateComponent<PlayerInPortal>("PlayerInPortal");
	//CreateComponent<ChangeColorComponent>("ChangeColorComponent");
	//CreateComponent<PetAccountComponent>("PetAccountComponent");
	//CreateComponent<PetFollowListComponent>("PetFollowListComponent");
	//CreateComponent<PetSummonComponent>("PetSummonComponent");
	m_SkillCDComponent = CreateComponent<SkillCDComponent>("SkillCDComponent");
	CreateComponent<TotemComponent>("TotemComponent");
	//CreateComponent<InteractTamedMobComponent>("InteractTamedMobComponent");
	m_pTeamComponent = CreateComponent<PlayerTeamComponent>("TeamComponent");
	m_TransformerSkinComponent = CreateComponent<TransformersSkinComponent>("TransformersSkinComponent");
	CreateComponent<PlayerHPProgressComponent>("HPProgressComponent");
	m_pItemSkillComponent = CreateComponent<ItemSkillComponent>("ItemSkillComponent");
	CreateComponent<OpenContainerComponent>("OpenContainerComponent");
	m_pAttackingTargetComponent = CreateComponent<PlayerAttackingTargetComponent>("AttackingTargetComponent");
	//CreateComponent<WeaponSkilledComponent>("WeaponSkilledComponent");
	//CreateComponent<AvatarSummonComponent>("AvatarSummonComponent");
	CreateComponent<SwarmComponent>("SwarmComponent");
	CreateComponent<NavigationPath>("NavigationPath");
	CreateComponent<SocRevivePointComponent>("SocRevivePointComponent");
	CreateComponent<LockCtrlComponent>("LockCtrlComponent");
	//CreateComponent<SkillComponent>("SkillComponent");
	SetAsActorViewer(4);

	auto surviveFun = [&](float dtime)
	{
		if (getSurviveDay() > m_surviveDays)
		{
			if (m_surviveDays != -1)
			{
				addAchievement(3, ACHIEVEMENT_SURVIVEDAY, -1);
				if (m_pWorld && !m_pWorld->isRemoteMode())
				{
					addSFActivity(SFACTIVITY_SAFEDAY, 1, 1, !this->hasUIControl());
					//updateTaskSysProcess(TASKSYS_SURVIVE_DAY, m_pWorld->getCurMapID());
				}
				auto def = GetDefManagerProxy()->getExtremityScoreDef(ACHIEVEMENT_SURVIVEDAY);
				if (def)
					addOWScore(def->Score);

				//成就系统, 统计单机模式生存天数, 每增加1天统计一次(只针对单机的冒险)
				ReportSurviveDay();
			}

			m_surviveDays = getSurviveDay();
		}

		if (m_pWorld && getSurviveDay(m_pWorld->getCurMapID()) > m_suriveDaysByWorld)
		{
			if (m_suriveDaysByWorld != -1)
			{
				addAchievement(3, ACHIEVEMENT_SURVIVEDAY, m_pWorld->getCurMapID());
				if (m_pWorld && !m_pWorld->isRemoteMode())
				{
					addSFActivity(SFACTIVITY_SAFEDAY, 1, 1, !this->hasUIControl());
					updateTaskSysProcess(TASKSYS_SURVIVE_DAY, m_pWorld->getCurMapID());
				}
			}

			m_suriveDaysByWorld = getSurviveDay(m_pWorld->getCurMapID());
		}
	};
	auto skillCDFuntion = [&](float dtime)
	{
		if (m_SkillCDComponent == nullptr)
		{
			m_SkillCDComponent = GetComponent<SkillCDComponent>();
		}
		
		if (m_SkillCDComponent)
		{
			m_SkillCDComponent->onUpdate(dtime);
		}
	};
	auto skinFuntion = [&](float dtime)
	{
		if (m_TransformerSkinComponent == nullptr)
		{
			m_TransformerSkinComponent = GetComponent<TransformersSkinComponent>();
		}

		if (m_TransformerSkinComponent)
		{
			m_TransformerSkinComponent->OnUpdate(dtime);
		}
	};
#ifndef IWORLD_SERVER_BUILD
	mNeedUpdateChunk[UpdateSurvive] = surviveFun;
	mNeedUpdateChunk[UpdateSkillCD] = skillCDFuntion;
	mNeedUpdateChunk[UpdateSkin] = skinFuntion;
#endif
	//#ifdef BUILD_MINI_EDITOR_APP
//	m_pActorChunkPos = CreateComponent<PlayerChunkPos>("ActorChunkPos");
//#endif
	SetSerializableInChunk(true);

#ifdef SURVIVAL_ACTOR_SUPPORT_SANDBOX_NODEEX
#else
	m_flags.SetFlag(FLAGTYPE_CANBINDNODEID, false); // 不去绑定ID
#endif// SURVIVAL_ACTOR_SUPPORT_SANDBOX_NODEEX
}

//事件注册
void ClientPlayer::createEvent()
{
	//Rainbow::Vector3f getCarryingBindPos()
	typedef ListenerFunctionRef<Rainbow::Vector3f&> ListenerGetCarryingBindPos;
	ListenerGetCarryingBindPos* listenerGetCarryingBindPos = SANDBOX_NEW(ListenerGetCarryingBindPos, [&](Rainbow::Vector3f& ret) -> void {
		ret = this->getCarryingBindPos();
	});
	Event2().Subscribe("getCarryingBindPos", listenerGetCarryingBindPos);

	typedef ListenerFunctionRef<bool&, ClientActor*, bool, int> ListenerMountActor;
	ListenerMountActor* listenerMountActor = SANDBOX_NEW(ListenerMountActor, [&](bool& ret, ClientActor* actor, bool isforce, int seatIndex) -> void {
		ret = this->mountActor(actor, isforce, seatIndex);
	});
	Event2().Subscribe("mountActor_key_1", listenerMountActor);

	typedef ListenerFunctionRef<> ListenerBlockSizeOnTrigger;
	ListenerBlockSizeOnTrigger* listenerBlockSizeOnTrigger = SANDBOX_NEW(ListenerBlockSizeOnTrigger, [&]() -> void {
		this->moveOneBlockSizeOnTrigger();
	});
	Event2().Subscribe("moveOneBlockSizeOnTrigger", listenerBlockSizeOnTrigger);


	typedef ListenerFunctionRef<float> ListenerHurtOnTrigger;
	ListenerHurtOnTrigger* listenerHurtOnTrigger = SANDBOX_NEW(ListenerHurtOnTrigger, [&](float hp) -> void {
		this->beHurtOnTrigger(hp);
	});
	Event2().Subscribe("beHurtOnTrigger", listenerHurtOnTrigger);

	typedef ListenerFunctionRef<ClientActor*> ListenerCheckCollideOnTrigger;
	ListenerCheckCollideOnTrigger* listenerCollideOnTrigger = SANDBOX_NEW(ListenerCheckCollideOnTrigger, [&](ClientActor* actor) -> void {
		this->checkCollideOnTrigger(actor);
	});
	Event2().Subscribe("checkCollideOnTrigger", listenerCollideOnTrigger);

	typedef ListenerFunctionRef<float, bool> ListenerCalFallMotion;
	ListenerCalFallMotion* listenerCalFallMotion = SANDBOX_NEW(ListenerCalFallMotion, [&](float motiony, bool onground) -> void {
		this->calFallMotion(motiony, onground);
	});
	Event2().Subscribe("calFallMotion", listenerCalFallMotion);

	typedef ListenerFunctionRef<float> ListenerFall;
	ListenerFall* listenerFall = SANDBOX_NEW(ListenerFall, [&](float fallh) -> void {
		this->fall(fallh);
	});
	Event2().Subscribe("fall", listenerFall);

	typedef ListenerFunctionRef<float&> ListenerFallHurtSubtract;
	ListenerFallHurtSubtract* listenerFallHurtSubtract = SANDBOX_NEW(ListenerFallHurtSubtract, [&](float& ret) -> void {
		ret = this->getFallHurtSubtract();
	});
	Event2().Subscribe("getFallHurtSubtract", listenerFallHurtSubtract);

}

void ClientPlayer::showSpecailTryShapeAnimForActorBody()
{
	ActorBody* actorbody = getBody();
	if (nullptr == actorbody)
	{
		return;
	}
	if (!actorbody->getShapeAnimEntity()) {
		//new entity
		Rainbow::Entity* entity = Rainbow::Entity::Create();
		//load model data
		//res\entity\100062
		core::string modelPath = "entity/110083/body1";

#if ENTITY_MODIFY_MODEL_ASYNC

		entity->LoadAsync(modelPath, false, [](Rainbow::Entity* e) {
			Rainbow::ColourValue color(119 / 255.0f, 121 / 255.0f, 214 / 255.0f, 0.75f);
		e->SetRimColor(&color, 0.1f);
		});

#else

		Rainbow::SharePtr<Asset> pdata = Model::LoadModelAssetByPathRule(modelPath.c_str());
		if (pdata) {

			Model* shapeAnimModel = Model::CreateInstanceFromAsset(pdata);
			entity->Load(shapeAnimModel);

			Rainbow::ColourValue color(119 / 255.0f, 121 / 255.0f, 214 / 255.0f, 0.75f);
			entity->SetRimColor(&color, 0.1f);
		}

#endif

		entity->AttachToScene(getWorld()->getScene());
		WCoord curPos = getPosition();// + WCoord(0,150,0);
		entity->SetPosition(curPos.toWorldPos());

		float scale = 1.3f;
		entity->SetScale(Rainbow::Vector3f(scale, scale, scale));
		entity->PlayAnim(0);//seqType2ID(SEQ_STAND));
		Quaternionf selfquat;
		selfquat = AngleEulerToQuaternionf(Vector3f(0, actorbody->getRenderYawOffset(), 0));
		entity->SetRotation(selfquat);

		actorbody->setShapeAnimEntity(entity);
	}

	const RoleSkinDef* skindef = GetDefManagerProxy()->getRoleSkinDef(getSkinID());
	if (skindef && skindef->getEffect(3))
	{
		if (actorbody->getEntity())
		{
			actorbody->getEntity()->StopMotion(skindef->getEffect(3));
		}
	}
}

void ClientPlayer::showSpecailTryShapeAnim()
{
	World* pWorld = getWorld();
	if (!pWorld)
		return;

	setCanControl(false);
	if (!pWorld->isRemoteMode())//主机  需要发送消息通知客机播放
	{
		//send msg to client
		PB_ShapeAdditionAnimHC msg;
		msg.set_status(true);
		msg.set_uin(getUin());
		GetGameNetManagerPtr()->sendBroadCast(PB_SHAPE_ADDITION_ANIM_HC, msg);
	}

	showSpecailTryShapeAnimForActorBody();

	if (g_pPlayerCtrl && g_pPlayerCtrl->getUin() == getUin()) {
		g_pPlayerCtrl->setViewMode(CAMERA_TPS_FRONT, true);
		g_pPlayerCtrl->resetCameraPos();
	}
}

void ClientPlayer::setSpecialShapeNameHeight(float height) {
	ActorBody* actorbody = getBody();
	if (nullptr == actorbody)
	{
		return;
	}
	if (!actorbody->getIsNeedRecoverShapeHeight())
	{
		actorbody->setBeforeShapeHeight(actorbody->getNameObjHeight());
		actorbody->setNameObjHeight(height);
		actorbody->setIsNeedRecoverShapeHeight(true);
	}
}
void ClientPlayer::hideSpecailTryShapeAnimForActorBody()
{
	ActorBody* actorbody = getBody();
	if (nullptr == actorbody)
	{
		return;
	}
	if (actorbody->getShapeAnimEntity())
	{
		actorbody->getShapeAnimEntity()->DetachFromScene();
		actorbody->setShapeAnimEntity(nullptr);
	}
	setSpecialShapeNameHeight(200.0f);
}

void ClientPlayer::ReqAllSingleBuildData()
{
	PB_AllSingleBuildDataCH ch;
	ch.set_uin(getUin());

	GetGameNetManagerPtr()->sendToHost(PB_AllSingleBuildData_CH, ch);
}

bool ClientPlayer::isFishNeedUp()
{
	int toolId = getCurToolID();
	if (GetDefManagerProxy()->isFishNeedUp(toolId))
	{
		return true;
	}
	return false;
}
void ClientPlayer::hideSpecailTryShapeAnim()
{
	World* pWorld = getWorld();
	if (!pWorld)
		return;

	setCanControl(true);
	if (!pWorld->isRemoteMode())//主机  需要发送消息通知客机停止播放
	{
		//send msg to client
		PB_ShapeAdditionAnimHC msg;
		msg.set_status(false);
		msg.set_uin(getUin());
		GetGameNetManagerPtr()->sendBroadCast(PB_SHAPE_ADDITION_ANIM_HC, msg);
	}

	hideSpecailTryShapeAnimForActorBody();

	if (g_pPlayerCtrl && g_pPlayerCtrl->getUin() == getUin()) {
		g_pPlayerCtrl->setViewMode(CAMERA_TPS_BACK, true);
		g_pPlayerCtrl->resetCameraPos();
	}
}

ClientPlayer::~ClientPlayer()
{
	GetICloudProxyPtr()->SimpleSLOG("release %lld", getObjId());
	//LOG_WARNING("~ ClientPlayer #### : %p, %d", this, CurrentThreadIsMainThread());
	if (GetActorBodySafeHandle()->IsVaild(m_UIViewBody))
	{
		//LOG_WARNING("~ ClientPlayer set #### : %p", m_UIViewBody);
		m_UIViewBody->setOwnerActorNull();
	}

	if (--gDumpCatch[this] < 0)
	{
		memcpy(nullptr, nullptr, 1);
		auto iter = mNeedUpdateChunk.find(0);
		mNeedUpdateChunk.erase(iter);
	}
	if (m_nSpectatorUin && g_pPlayerCtrl && m_nSpectatorUin == g_pPlayerCtrl->getUin() && g_pPlayerCtrl->getAttrib())
	{
		MINIW::ScriptVM* scriptvm = MINIW::ScriptVM::game();
		scriptvm->setUserTypePointer("MainPlayerAttrib", "PlayerAttrib", g_pPlayerCtrl->getAttrib());
	}

	MINIW::ScriptVM::game()->callFunction("MpActorAvatarDel", "i", getUin());

	m_PlayerAttrib = NULL;

	for (auto iter = m_TackleEffects.begin(); iter != m_TackleEffects.end(); iter++)
	{
		if (iter->effect) {
			iter->effect->setNeedClear();
		}
	}
	m_TackleEffects.clear();
	for (auto iter = m_TriggerSounderMap.begin(); iter != m_TriggerSounderMap.end(); ++iter)
	{
		OGRE_DELETE(iter->second);
	}
	m_TriggerSounderMap.clear();

	OGRE_DELETE(m_CheatData);
	OGRE_DELETE(m_pTaskMgr);

	OGRE_DELETE(m_TriggerSounder);
	OGRE_DELETE(m_StateController);
	OGRE_DELETE(m_CameraModel);
	if (m_MoveControl)
	{
		delete m_MoveControl;
	}

	GetModEntryMgr().ClearEntryTrigListen(m_ObjId);
	//OGRE_DELETE(m_totemComp);
	//OGRE_DELETE(m_pOpenContainerComp);
	//OGRE_DELETE(m_pSkillCDComp);
	//OGRE_DELETE(m_pItemSkillComp);
	//OGRE_DELETE(m_pAccountHorseComp);
	//OGRE_DELETE(m_pPetFollowListComp);
	//OGRE_DELETE(m_pInteractTamedMobComp);
	//OGRE_DELETE(m_pPetAccountComp);
	//OGRE_DELETE(m_pPetSummonComp);
	//OGRE_DELETE(m_pChangeColorComp);
	//OGRE_DELETE(m_pTransformersSkinComp);
	tolua_clear_tousertype(MINIW::ScriptVM::game()->getLuaState(), this, "ClientPlayer");
}

bool ClientPlayer::init(int uin, const char* nickname, int playerindex, const char* customjson)
{
	GetICloudProxyPtr()->SimpleSLOG("ClientPlayer init %d name=%s", uin, nickname);
	if (playerindex < 0)
		playerindex = 1;

	SetObjId(uin);
	//m_ObjId = uin;
	m_Nickname = nickname;
	GetDefManagerProxy()->filterStringDirect((char*)m_Nickname.c_str());

	if (customjson)
		m_strCustomjson = customjson;
	else
		m_strCustomjson.clear();
	m_strOriginCustomJson = m_strCustomjson;
	if (getGunLogical())
	{
		getGunLogical()->unregisterThirdPersonEvent();
	}
	ENG_DELETE(m_Body);
	m_Body = ENG_NEW(ActorBody)(this);
	getBody()->initPlayer(playerindex, 0, customjson, 0, false, true);
	m_StateController = ENG_NEW(PlayerStateController)();
	//m_StateController->init(); 构造函数已经调用了，不需要主动调用
	m_StateController->setClientPlayer(this);

	m_originSkinId = getBody()->getSkinID();

	PlayerLocoMotion* loco = CreateComponent<PlayerLocoMotion>("PlayerLocoMotion");

	auto functionWrapper = getFuncWrapper();
	if (functionWrapper)
	{
		char buff[64] = {};
		sprintf(buff, "playerskin_%d", uin);
		functionWrapper->setActorFacade(buff);
	}

#ifndef IWORLD_SERVER_BUILD
	const RoleDef* def = GetDefManagerProxy()->getRoleDef(getBody()->getModelID(), getBody()->getGeniusLv());
#else
	const RoleDef* def = GetDefManagerProxy()->getRoleDef(PlayerIndex2Model(playerindex), PlayerIndex2Genius(playerindex)); ;
#endif 
	if (def != NULL)
	{
		//getLocoMotion()->setAttackBound(def->HitHeight, def->HitWidth, def->HitThickness);
		updateAttackBound(def->HitHeight, def->HitWidth, def->HitThickness);
		if (getLocoMotion())
			getLocoMotion()->setBaseCollideBoxs(def->CollideBoxs, def->SneakCollideBoxs);
	}

	//getLocoMotion()->setBound(180, 60);
	updateBound(180, 60);

	bool isremote = false;
	if (GetGameNetManagerPtr() && !GetGameNetManagerPtr()->isHost()) isremote = true;

	auto attrib = CreateComponent<PlayerAttrib>("PlayerAttrib", this);
	do //绑定效果事件
	{
		auto callBackAppend = [this](int buffid, int bufflvl) {
			this->onBuffAppend(buffid, bufflvl);
		};
		attrib->setDelegateBuffAppend(callBackAppend);

		auto callBackRemove = [this](int buffid, int bufflvl) {
			this->onBuffRemove(buffid, bufflvl);
		};
		attrib->setDelegateBuffRemove(callBackRemove);

	} while (false);
	//	m_Attrib = attrib;
	m_PlayerAttrib = static_cast<PlayerAttrib*>(attrib);

	ChangeNameObjHeight();

#ifndef IWORLD_SERVER_BUILD
	OGRE_DELETE(m_CameraModel);

	std::string sType, sID;
	parsePlayerBaseModelID(m_strCustomModel, sType, sID);
	if (sType == "importmodel")
	{
		m_CameraModel = ENG_NEW(CameraModel)(playerindex, 0, customjson, sID.c_str());
	}
	else
	{
		m_CameraModel = ENG_NEW(CameraModel)(playerindex, 0, customjson);
	}
#endif
	if (hasUIControl() || (GetGameNetManagerPtr() && GetGameNetManagerPtr()->isHost()))
	{
		m_MoveControl = new MoveControl(uin, hasUIControl());
	}

	m_biomeBeenTo.clear();
	if (getGunLogical())
	{
		getGunLogical()->registerThirdPersonEvent();
	}
	return true;
}

bool ClientPlayer::initAvatar()
{
	if (IsRunSandboxPlayer())
	{
		return false;
	}
	bool isSpeciMap = false;
	int uin = int(m_ObjId);
	MINIW::ScriptVM::game()->callFunction("IsArExhibitionMap", ">b", &isSpeciMap);
	MINIW::ScriptVM::game()->callFunction("ReqCurUseAchieveByUin", "i", getUin());
	if (isSpeciMap && uin && !GAMERECORD_INTERFACE_EXEC(isRecordPlaying(), false))
	{
		MINIW::ScriptVM::game()->callFunction("SetArExhibitionAvatar", "is", uin, m_strCustomjson.c_str());
		if (getBody() && getBody()->getIsNeedLoadCusAnim())
		{
			getBody()->loadCusAnim();
		}

		return true;
	}
	if (uin && m_strCustomjson.size() && !GAMERECORD_INTERFACE_EXEC(isRecordPlaying(), false))
	{
		int skin_id = 0;
		SandboxCoreDriver::GetInstance().GetLua().CallFunctionM("RoleSkin_Helper", "GetUseAvtarBodyModel", "is>i", getUin(), m_strCustomjson.c_str(), &skin_id);
		if (!getBody()) return false;
		if (skin_id == 0)
		{
			getBody()->setBodyType(3);
			//LOG_INFO("addAvatarPartModel loc3:%p ", getBody());
			getBody()->addDefaultAvatar();
		}
		MINIW::ScriptVM::game()->callFunction("ClientGetRoleAvatarInfo", "is", uin, m_strCustomjson.c_str());

		if (getBody()->getIsNeedLoadCusAnim())
		{
			getBody()->loadCusAnim();
		}

		return true;
	}
	return false;
}

void ClientPlayer::initCusMotion()
{
	if (!getBody()) return;
	if (getBody()->getMutateMob() != 0)
	{
		getBody()->setIsNeedLoadCusAnim(false);
		return;
	}

	getBody()->clearNeedLoadCusMotionID();
	int uin = int(m_ObjId);
	int cusMotionSeat = 0;
	char motionsData[256] = { 0 };
	MINIW::ScriptVM::game()->callFunction("CusMotionGetCurUseSeat", "iu[ActorBody]>i", uin, getBody(), &cusMotionSeat);
	if (0 == cusMotionSeat)
		return;

	getBody()->setIsNeedLoadCusAnim(true);
	return;
}

flatbuffers::Offset<FBSave::SectionActor> ClientPlayer::save(SAVE_BUFFER_BUILDER& builder)
{
	return flatbuffers::Offset<FBSave::SectionActor>(0);
}

bool ClientPlayer::load(const void* srcdata, int version)
{
	return false;
}

int ClientPlayer::getCurViewRange()
{
#ifndef DEDICATED_SERVER
	int viewRange = hasUIControl() ? m_ViewRangeSetting : 4;
#else
	int viewRange = Rainbow::GetICloudProxyPtr()->GetCloudViewRange();
#endif
#ifdef OPEN_CLOUD_VIEW_RANGE
	if (Config::GetSingleton().IsSandboxMode() && (m_attrViewRange > 0))
	{
		viewRange = m_attrViewRange;
	}
#endif // 
	if (g_pPlayerCtrl == nullptr || g_pPlayerCtrl == this)
	{
		GetMiniCraftRenderer().SetViewSectionRange(viewRange);
	}
	return viewRange;
}

void ClientPlayer::enterWorld(World* pworld)
{
	MNSandbox::GetGlobalEvent().Emit<long long, int, unsigned>("AchievementManager_onEnterWorld", pworld->getOWID(), getObjId(), pworld->getMapSpecialType());
	ActorLiving::enterWorld(pworld);
	/*if (TaskSubSystem::GetTaskSubSystem())
	{
		TaskSubSystem::GetTaskSubSystem()->OnEnterWorld(pworld->getOWID(), getObjId());
	}*/
	if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
		MNSandbox::SandboxContext sContext = MNSandbox::SandboxContext(nullptr).
			SetData_Number("worldId", pworld->getOWID()).
			SetData_Number("objId", getObjId());
		MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("TaskSubSystem_OnEnterWorld", sContext);
	}
	m_ChunkViewer.enterWorld(pworld, getPosition(), ClientPlayer::GetCurViewRange(this)/*getCurViewRange()*/);

	setOperate(PLAYEROP_NULL);
	m_DigEffect = NULL;
	//m_bFirstPlayReviveEffect = false;
	//人物进入场景记录时间戳
	m_PlayerStartTimes = std::chrono::duration_cast<std::chrono::seconds>(
		std::chrono::system_clock::now().time_since_epoch()).count();

	if (GetWorldManagerPtr() && GetWorldManagerPtr()->getSpecialType() == HOME_GARDEN_WORLD)
	{
		//家园地图 人物免伤 可以互相攻击 增加互动
		if (getAttrib())
		{
			getAttrib()->setImmuneType(ATTACK_ALL, true);
		}
	}

	if (getBody())
		getBody()->setNeedUpdateAnim(GAMERECORD_INTERFACE_EXEC(isRecordPlaying(), false) ? false : hasUIControl());
	//getBody()->setNeedUpdateAnim(true);

	if (!pworld->isRemoteMode())
	{
		ParticlesComponent::playParticles(this, "1001.ent");
	}

	m_RegionStartPos = WCoord(0, -1, 0);
	applyDisplayName();

	changeOPWay();

	auto RidComp = getRiddenComponent();
	ClientActor* actor = NULL;

	if (RidComp)
	{
		actor = RidComp->getRidingActor();
	}

	if (!getWorld()->isRemoteMode())
	{
		if (actor)
		{
			auto actorRidComp = actor->getRiddenComponent();
			if (actorRidComp && ((actorRidComp->findEmptyRiddenIndex() < 0 && actorRidComp->findRiddenIndex(this) < 0) || actor->getObjType() == OBJ_TYPE_SHAPESHIFT_HORSE))
				mountActor(NULL, true);

		}
		else if (!actor && /*m_RidingActor != 0*/!(RidComp && RidComp->checkRidingByActorObjId(0)))
			setRidingActor(NULL);

		if (!hasUIControl() && m_pTaskMgr)
			m_pTaskMgr->notifyTaskByEnterWorld();
	}

	if (actor && actor->getObjType() == OBJ_TYPE_SHAPESHIFT_HORSE && getBody() && !hasUIControl())
	{
		getBody()->shareShift(true);
	}


#ifdef USE_PHYSX
#ifdef USE_ACTOR_CONTROL
	//if (RidComp)
	{
		ClientActor* actor = RidComp ? RidComp->getRidingActor() : nullptr;
		if (actor && actor->getObjType() == OBJ_TYPE_VEHICLE)
		{
			if (!getWorld()->isRemoteMode())
			{
				VehicleAssembleLocoMotion* loc = dynamic_cast<VehicleAssembleLocoMotion*>(actor->getLocoMotion());
				if (loc)
				{
					loc->setFocus();
				}
			}
		}
		else
		{
			//static_cast<PlayerLocoMotion *>(getLocoMotion())->checkPhysWorld();
			if (getLocoMotion()) {
				static_cast<PlayerLocoMotion*>(getLocoMotion())->attachPhysActor();
			}
		}
		m_UsePhysics = true;
	}
#endif
#endif

	m_suriveDaysByWorld = getSurviveDay(pworld->getCurMapID());

	ReportSurviveDay();

	//add by navy
	//if (pworld->getCurMapID() == MAPID_LIEYANSTAR && m_PlayerAttrib)//去掉烈焰星炎热buff 温度系统
	//{
	//	if (getCurDorsumID() == ITEM_FIRESAFETY_PACK)
	//	{
	//		m_PlayerAttrib->addBuff(HOT_BUFF, 2);
	//	}
	//	else
	//	{
	//		m_PlayerAttrib->addBuff(HOT_BUFF, 1);
	//	}
	//}

	if (m_PlayerAttrib && m_PlayerAttrib->hasBuff(80, 1))
	{
		wakeUp(true, false, false);
	}

	if (GetWorldManagerPtr() && GetWorldManagerPtr()->isCreativeMode() && m_PlayerAttrib && m_PlayerAttrib->hasBuff(93))
	{
		m_PlayerAttrib->removeBuff(93);
	}

	MNSandbox::GetGlobalEvent().Emit<>("StatisticTerrgen_PlayerEnterGame");
	if (m_pWorld && m_pWorld->isRemoteMode())
	{
		jsonxx::Object context;
		context << "hidestatus" << 0;
		GetSandBoxManagerPtr()->sendToHost("PB_SCORPION_HIDE_STATUS_CH", context.bin(), context.binLen());
	}

	playAnim(SEQ_ENTERWORLD);

	MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("Client_Player_enterWorld", SandboxContext(nullptr).SetData_Userdata("player", static_cast<IClientPlayer*>(this)));

	if (g_WorldMgr && this == g_pPlayerCtrl)
	{
		if (g_WorldMgr->getGameMode() == 0 || g_WorldMgr->getGameMode() == OWTYPE_CREATE_RUNGAME || g_WorldMgr->getGameMode() == OWTYPE_EXTREMITY || g_WorldMgr->getGameMode() == OWTYPE_FREEMODE)
		{
			char sWorldId[64];
			sprintf(sWorldId, "%lld", pworld->getOWID());
			MINIW::ScriptVM::game()->callFunction("AutoRoleEquip", "si", sWorldId, getUin());
		}
	}
	if (g_WorldMgr && this == g_pPlayerCtrl)
	{
		g_WorldMgr->onEnterWorld(pworld);
	}

	if (m_pItemComponent)
	{
		m_pItemComponent->DoCurShotGunModEntry(false);
		m_pItemComponent->onSetCurShortcut(getCurShortcut());
	}
}



void ClientPlayer::leaveWorld(bool keep_inchunk)
{
	/*if (TaskSubSystem::GetTaskSubSystem())
	{
		TaskSubSystem::GetTaskSubSystem()->OnLeaveWorld(getObjId());
	}*/
	auto pCurPlat = GetPlatform();
	if (pCurPlat)
	{
		SetOnPlatform(nullptr, true);
		auto pCurPlatPC = pCurPlat->getPhysicsComponent();
		if (pCurPlatPC)
		{
			pCurPlatPC->UnBindPlayer();
			pCurPlatPC->RemoveWaitBindPlayers(getUin());
		}
	}

	WCoord pos = getPosition();
	if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
		MNSandbox::SandboxContext sContext = MNSandbox::SandboxContext(nullptr).
			SetData_Number("objid", getObjId());
		MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("TaskSubSystem_OnLeaveWorld", sContext);
	}
	if (getUsingEmitter())
	{
		setPosition(WCoord(0, 8, 0));
	}
	if (isSittingInStarStationCabin())
	{
		WCoord blockpos = CoordDivBlock(getPosition());
		WCoord newpos = blockpos;

		WorldStarStationTransferCabinContainer* container = dynamic_cast<WorldStarStationTransferCabinContainer*>(m_pWorld->getContainerMgr()->getContainer(blockpos));
		if (container)
		{
			container->setBindPlayerUin(0);
			int nStarStationId = container->getConnectedStarStationID();
			int nStatus = GetStarStationTransferMgr().getStarStationCabinStatus(nStarStationId, blockpos);
			if (isStarStationTeleporting())
			{
				if (nStatus == STARSTATION_TRANSFERING)
				{
					nStatus = STARSTATION_ACTIVATED;
					container->setStatus((StarStationTransferStatus)nStatus);
				}
			}

			//PB_LeaveStarStationCabin leaveCabin;
			//leaveCabin.set_uin(getUin());
			//leaveCabin.set_starstationid(nStarStationId);
			//leaveCabin.set_status(nStatus);
			//PB_Vector3* pos = leaveCabin.mutable_cabinpos();
			//if(pos)
			//{
			//	pos->set_x(blockpos.x);
			//	pos->set_y(blockpos.y);
			//	pos->set_z(blockpos.z);
			//}

			//if (m_pWorld->isRemoteMode())
			//{
			//	GetGameNetManagerPtr()->sendToHost(PB_LEAVE_STARSTATION_CABIN_CH, leaveCabin);
			//}
			//else
			//{
			//	GetGameNetManagerPtr()->sendBroadCast(PB_NOTIFY_LEAVE_STARSTATION_CABIN_HC, leaveCabin);
			//}
		}
	}

	restoreSkin();
	OGRE_RELEASE(m_CurPlaySnd);
	auto* ball = getCatchBall();
	if (nullptr != ball)
	{
		auto pComp = ball->getBindActorCom();
		if (nullptr != pComp)
		{
			pComp->setBindInfo(-getObjId(), WCoord(0, 0, 0));
		}
		else
		{
			auto pBasketballlComp = ball->getBindActorCom();
			if (nullptr != pBasketballlComp)
			{
				pBasketballlComp->setBindInfo(-getObjId(), WCoord(0, 0, 0));
			}
		}
	}

	if (m_pWorld && !m_pWorld->isRemoteMode())
	{
		auto CarryComp = getCarryComponent();
		if (CarryComp && CarryComp->isCarrying())
		{
			carryActor(NULL, getPosition());
		}
		auto fishCom = m_pFishingComponent;
		if (fishCom)
		{
			fishCom->forceQuitFishing();
		}
	}

	//add by navy
	//if (m_pWorld && m_pWorld->getCurMapID() == MAPID_LIEYANSTAR)
	//{
	//	if (m_PlayerAttrib)
	//		m_PlayerAttrib->removeBuff(HOT_BUFF);
	//}
	m_nFreezingFlag = FREEZING_STATE_CLEAN;
	if (m_PlayerAttrib)
	{
		if (m_PlayerAttrib->hasBuff(FREEZING_BUFF))
		{
			m_PlayerAttrib->removeBuff(FREEZING_BUFF);
		}
	}

	closeContainer();
	m_ChunkViewer.leaveWorld(m_pWorld);
	//getPetSummonComp()->onLeaveWorld(keep_inchunk);
	auto petSummonComp = GetComponent<PetSummonComponent>();
	if (petSummonComp) petSummonComp->onLeaveWorld(keep_inchunk);
	auto avatarSummonComp = GetComponent<AvatarSummonComponent>();
	if (avatarSummonComp) avatarSummonComp->onLeaveWorld();

	auto comp = GetComponentByName("InWaterComponent");
	if (comp)
	{
		comp->Event().Emit("OnLeaveWorld");
	}
	ActorLiving::leaveWorld(false);

#ifdef USE_PHYSX
#ifdef USE_ACTOR_CONTROL
	static_cast<PlayerLocoMotion*>(getLocoMotion())->detachPhysActor();
	m_UsePhysics = false;
#endif
#endif
	g_WorldMgr->getSkillManager()->removeSkillPlayerInfo(getUin());
	if (g_WorldMgr && this == g_pPlayerCtrl)
	{
		g_WorldMgr->onLeaveWorld();
	}
}

void ClientPlayer::updateBallEffect()
{
	stopMotion("ball_dribbling");
	stopMotion("ball_dribbling2");
	char effname[256];
	sprintf(effname, "ball_team_%d", getTeam());
	stopMotion(effname);

	if (PLAYEROP_WAY_FOOTBALLER == m_OPWay)
	{
		playMotion(effname, false, 1);
		if (getCatchBall())
		{
			playMotion("ball_dribbling", false, 1);
		}
	}
	else if (PLAYEROP_WAY_BASKETBALLER == m_OPWay)
	{
		playMotion(effname, false, 1);
		if (getCatchBall())
		{
			playMotion("ball_dribbling2", false, 1);
		}
	}
	else if (PLAYEROP_WAY_PUSHSNOWBALL == m_OPWay)
	{
		playMotion(effname, false, 1);
		if (getCatchBall())
		{
			playMotion("ball_dribbling", false, 1);
		}
	}
}

int ClientPlayer::getCurWorldMapId()
{
	if (m_pWorld) return m_pWorld->getCurMapID();

	return -1;
}

int ClientPlayer::checkPlayerReviveStar()//检查复活所需的星星数量
{
	unsigned long StarDebuffTime = getPlayerAttrib()->getStarDebuffTime();
	int Revive_Need_Star_New = 1;
	if (StarDebuffTime <= 0)
	{
		getPlayerAttrib()->setStarDebuffStage(0);
		getPlayerAttrib()->setStarDebuffTime(0);
	}
	int StarDebuffStage = getPlayerAttrib()->getStarDebuffStage();
	int tick2sec = 20;
	if (StarDebuffStage < GetLuaInterfaceProxy().get_lua_const()->revive_in_place_consume_buff_num_stage1)//原地复活星星消耗增加BUFF第一段分割次数
	{
		Revive_Need_Star_New = Revive_Need_Star_New + StarDebuffStage * GetLuaInterfaceProxy().get_lua_const()->revive_in_place_consume_buff_star_extra1;
	}

	else if (StarDebuffStage <= GetLuaInterfaceProxy().get_lua_const()->revive_in_place_consume_buff_num_stage2)//原地复活星星消耗增加BUFF第二段分割次数
	{
		Revive_Need_Star_New = ((StarDebuffStage - GetLuaInterfaceProxy().get_lua_const()->revive_in_place_consume_buff_num_stage1) + 2) * GetLuaInterfaceProxy().get_lua_const()->revive_in_place_consume_buff_star_extra2;
	}
	else
	{
		Revive_Need_Star_New = (GetLuaInterfaceProxy().get_lua_const()->revive_in_place_consume_buff_num_stage2 - GetLuaInterfaceProxy().get_lua_const()->revive_in_place_consume_buff_num_stage1 + 2) * GetLuaInterfaceProxy().get_lua_const()->revive_in_place_consume_buff_star_extra2;
	}
	return Revive_Need_Star_New;
}

void ClientPlayer::setPlayerReviveStar()//设置星星惩罚所需星星
{
	unsigned long StarDebuffTime = getPlayerAttrib()->getStarDebuffTime();
	if (StarDebuffTime <= 0)
	{
		getPlayerAttrib()->setStarDebuffStage(0);
		getPlayerAttrib()->setStarDebuffTime(0);
	}
	int StarDebuffStage = getPlayerAttrib()->getStarDebuffStage();
	int tick2sec = 20;
	if (StarDebuffStage == 0)//第一次
	{
		StarDebuffTime = GetLuaInterfaceProxy().get_lua_const()->revive_in_place_consume_buff_duration * tick2sec;
		StarDebuffStage = 1;
	}
	else if (StarDebuffStage < GetLuaInterfaceProxy().get_lua_const()->revive_in_place_consume_buff_num_stage1)//原地复活星星消耗增加BUFF第一段分割次数
	{
		StarDebuffTime = StarDebuffTime + GetLuaInterfaceProxy().get_lua_const()->revive_in_place_consume_buff_duration_extra1 * tick2sec;
		StarDebuffStage = StarDebuffStage + 1;
	}
	else if (StarDebuffStage <= GetLuaInterfaceProxy().get_lua_const()->revive_in_place_consume_buff_num_stage2)//原地复活星星消耗增加BUFF第一段分割次数
	{
		StarDebuffTime = StarDebuffTime + GetLuaInterfaceProxy().get_lua_const()->revive_in_place_consume_buff_duration_extra2 * tick2sec;
		StarDebuffStage = StarDebuffStage + 1;
	}
	else
	{
		StarDebuffTime = StarDebuffTime + GetLuaInterfaceProxy().get_lua_const()->revive_in_place_consume_buff_duration_extra2 * tick2sec;
	}
	getPlayerAttrib()->setStarDebuffStage(StarDebuffStage);
	getPlayerAttrib()->setStarDebuffTime(StarDebuffTime);
}


bool ClientPlayer::revive(int reviveType/* =0 */, int x/* =0 */, int y/* =-1 */, int z/* =0 */)
{
	uint64_t survival_time = getSurvivalTime();

	//复活完重新记录时间戳
	m_PlayerStartTimes = std::chrono::duration_cast<std::chrono::seconds>(
		std::chrono::system_clock::now().time_since_epoch()).count();

	getLocoMotion()->m_Motion.x = 0;
	getLocoMotion()->m_Motion.y = 0;
	getLocoMotion()->m_Motion.z = 0;

	setHPProgressDirty();
	setAccumulatorState(1);
	setAccumulatorState(-1);
	float nowStrength = 100.0f;
	if (m_PlayerAttrib)
	{
		nowStrength = m_PlayerAttrib->getStrength();//获取当前体力值
	}
	if (m_CameraModel)
	{
		m_CameraModel->stopHandAnim();
	}

	if (m_pWorld->isRemoteMode())
	{
		getAttrib()->revive();
		getBody()->onRevive();
		//通知ui隐藏复活界面
		MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("SocReviveSuccess",
			MNSandbox::SandboxContext(nullptr)
		);

		if (reviveType != 3)
		{
			//玩法模式地图的无敌Buff特效
			if (GetWorldManagerPtr()->isGameMakerRunMode() || GetWorldManagerPtr()->isAdventureMode())
			{
				// 玩家复活会把最大生命值重置掉
				float maxHP = getAttrib()->getMaxHP();
				float maxStrength = m_PlayerAttrib->getBasicMaxStrength();
				if (GetWorldManagerPtr()->isGameMakerRunMode())
				{
					GetWorldManagerPtr()->m_RuleMgr->onPlayerInit(this, true);
				}
				getAttrib()->setBasicMaxHP(maxHP);
				getAttrib()->setHP(maxHP);
				if (reviveType == 1)
				{
					m_PlayerAttrib->setBasicMaxStrength(maxStrength);
					static_cast<ClientPlayer*>(m_PlayerAttrib->GetOwnerPlayer())->setPlayerReviveStar();
					//LOG_INFO("ClientPlayer::revive(): 1 maxStrength = %.2f", maxStrength);
					if (nowStrength < 20)
					{
						m_PlayerAttrib->setStrength(m_PlayerAttrib->getReviveStrength() * ((float)GetLuaInterfaceProxy().get_lua_const()->actor_revive_in_place_strength_min / 100.0f));
					}
					else
					{
						m_PlayerAttrib->setStrength(nowStrength);
					}
				}
				else
				{
					m_PlayerAttrib->setBasicMaxStrength(maxStrength);
					//LOG_INFO("ClientPlayer::revive(): 1 maxStrength = %.2f", maxStrength);
					m_PlayerAttrib->setStrength(m_PlayerAttrib->getReviveStrength());
				}
			}
		}
		return true;
	}
	else
	{
		if (!isDead()) return false;
		OnReviveForTrigger(reviveType);
		int curMapId = m_pWorld->getCurMapID();
		if (isInSpectatorMode())
		{
			// int cleartype = 0;
			// if (GetWorldManagerPtr()->isGodMode() || m_pWorld->getOWID() == NEWBIEWORLDID) cleartype = 2;
			// else if (GetWorldManagerPtr()->isGameMakerRunMode())
			// {
			// 	cleartype = (int)GetWorldManagerPtr()->m_RuleMgr->getRuleOptionVal(GMRULE_PLAYERDIE_DROPS);
			// }
			// else if (GetWorldManagerPtr()->isSurviveMode())
			// {
			// 	cleartype = 4; // 死亡掉落死亡盒子
			// }
			//
			// if (!checkActionAttrState(ENABLE_DEATHDROPITEM))
			// 	cleartype = 2;
			// getLivingAttrib()->clearrevive(cleartype);
			getAttrib()->revive();
			getBody()->onRevive();
			return true;
		}

		bool bNeedReviveEquipBuff = false;

		if (reviveType == 0) //重新尝试
		{
			int cleartype = 0; //全部掉落
			// if (GetWorldManagerPtr()->isGodMode() || m_pWorld->getOWID() == NEWBIEWORLDID) cleartype = 2;
			// else if (GetWorldManagerPtr()->isGameMakerRunMode())
			// {
			// 	cleartype = (int)GetWorldManagerPtr()->m_RuleMgr->getRuleOptionVal(GMRULE_PLAYERDIE_DROPS); //按照玩法模式规则掉落
			// }
			// else if (GetWorldManagerPtr()->isSurviveMode())
			// {
			// 	cleartype = 4; //死亡掉落死亡盒子
			// }
			//
			// if (!checkActionAttrState(ENABLE_DEATHDROPITEM))
			// 	cleartype = 2; //不掉落

			// float reliveTime = 0;
			// int mode = m_pWorld->getReviveMode(reliveTime);
			// if (!GetWorldManagerPtr()->isGameMakerRunMode() || mode == 0) {
			// 	getLivingAttrib()->clearrevive(cleartype);
			// }

			// replacePlayer(WCoord(x, y, z));
			//auto pos = g_WorldMgr->getTeamPrePoint(this);

			//if (!m_pWorld->isRemoteMode()) //todo 支持活着的玩家再打开这个功能
			//	ActorPlayerCorpse::CreateFromPlayer(this);//创建尸体，把所有装备转移到尸体
			

			auto pos = g_WorldMgr->getRandomRespawnPoint(m_pWorld);
			if (pos.y < 0)
				pos = g_WorldMgr->getSpawnPointEx(m_pWorld);
			
			//获取选择床的坐标
			SocRevivePointComponent* SocRevive = GetComponent<SocRevivePointComponent>();
			bool b_bed = false;
			if (SocRevive && SocRevive->IsSelect())
			{
				pos = SocRevive->GetSelectPos();
				b_bed = true;
			}
			if (b_bed &&getLocoMotion()) //选择床复活
			{
				getLocoMotion()->gotoPosition(BlockBottomCenter(pos), 0, 0);
			}
			else {
				gotoBlockPos(m_pWorld, pos, false);
			}
			getAttrib()->revive();
			getBody()->onRevive();

			std::string location = "[";
			location += std::to_string(pos.x);
			location += ",";
			location += std::to_string(pos.y);
			location += ",";
			location += std::to_string(pos.z);
			location += "]";

			GameAnalytics::TrackEvent("player_respawn", {
				{"respawn_type",b_bed == true ? 1 : 0},
				{"respawn_id",m_pWorld->getBlockID(pos)},
				{"respawn_location",location}
			});

			//不掉落装备的情况下, 恢复装备的效果
			if (GetWorldManagerPtr()->isGameMakerRunMode() && cleartype == 2)
			{
				bNeedReviveEquipBuff = true;
				//reviveEquipEffect(1);
			}

			int customtype = ModPackMgr::GetInstancePtr()->GetCustomReviveFlag(); //自定义复活
			if (customtype > 0)
				bNeedReviveEquipBuff = true;

			// 掉落盒子模式添加保护和跑快快BUFF
			if (GetWorldManagerPtr()->isSurviveMode())
			{
				getLivingAttrib()->addBuff(REVIVE_RUN_FAST, 1);
				getLivingAttrib()->addBuff(INVULNERABLE_BUFF, 2);
			}
		}
		// 105广告位和原官方2号广告位做同样处理
		else if (reviveType == 1 || reviveType == 2 || reviveType == 3 || reviveType == 105)
		{
			if (reviveType == 1)
			{
				int customtype = ModPackMgr::GetInstancePtr()->GetCustomReviveFlag();
				if (customtype  != 0)
				{
					//customtype 1 开启 2 启用惩罚
					int switchStarConsumMode = (customtype >> 1);
					if (switchStarConsumMode > 0)
					{
						int Revive_Need_Star_New = checkPlayerReviveStar();
						Revive_Need_Star_New = Revive_Need_Star_New * ModPackMgr::GetInstancePtr()->GetCustomReviveCostRate();
						int starNum = 0;
						MINIW::ScriptVM::game()->callFunction("GetCustomRevieCost", "i>i",getUin(), &starNum);
						if (starNum < Revive_Need_Star_New)//数量不足则返回false
						{
							return false;
						}
						setPlayerReviveStar();
					}
				}
				else if (g_WorldMgr->isAdvNewStarConsumMode())//设置星星惩罚等级
				{
					int  starNum = (getPlayerAttrib()->getExp() / EXP_STAR_RATIO);
					int Revive_Need_Star_New = static_cast<ClientPlayer*>(getPlayerAttrib()->GetOwnerPlayer())->checkPlayerReviveStar();
					if (starNum < Revive_Need_Star_New)//星星不足则返回false
					{
						return false;
					}
					static_cast<ClientPlayer*>(getPlayerAttrib()->GetOwnerPlayer())->setPlayerReviveStar();
					getPlayerAttrib()->addExp(-Revive_Need_Star_New * EXP_STAR_RATIO);
				}
				else
				{
					if (getPlayerAttrib()->getExp() < 1 * EXP_STAR_RATIO)
						return false;
					getPlayerAttrib()->addExp(-1 * EXP_STAR_RATIO);
				}

			}
			else if (reviveType == 3)
			{
				getLivingAttrib()->clearrevive(2);
			}

			//如果是玩法模式or编辑模式，并且玩家位置是-6400以下，则传送至复活点
			bool reviveCurBlockPos = true;

			if ((g_WorldMgr->isGameMakerRunMode() || g_WorldMgr->isGameMakerMode()) && getPosition().y < -64 * BLOCK_SIZE) {
				reviveCurBlockPos = false;
			}

			WCoord curpos = getPosition();
			if (reviveCurBlockPos)
			{
				if (curpos.y > 0)
				{
					gotoBlockPos(m_pWorld, CoordDivBlock(curpos), false);
				}
				else
				{
					gotoBlockPos(m_pWorld, CoordDivBlock(WCoord(curpos.x, 0, curpos.z)), true);
				}
			}
			else
			{
				//优先以5个方块半径螺旋向上查找复活点
				WCoord pos = findNearestVerticalReviveBlockPos(m_pWorld, CoordDivBlock(WCoord(curpos.x, 0, curpos.z)), 5);
				if (pos.y > 0)
					gotoBlockPos(m_pWorld, pos, false);
				else
					replacePlayer(WCoord(x, y, z));
			}

			ParticlesComponent::playParticles(this, "1001.ent");

			getAttrib()->revive();
			getBody()->onRevive();

			//不掉落装备的情况下, 恢复装备的效果
			if (GetWorldManagerPtr()->isGameMakerRunMode() || GetWorldManagerPtr()->isSurviveMode())
				reviveEquipEffect(reviveType);
		}
		auto soundComp = getSoundComponent();
		if (soundComp)
		{
			soundComp->playSound("misc.rebirth", 1.0f, 1.0f);
		}

		PlayerAttrib* playerAttrib = getPlayerAttrib();
		if (playerAttrib)
		{
			// 同步给客机的人血量变化(第一时间通知一次，在addbuff，也就是onPlayerInit前）
			PB_ActorAttrChangeHC actorAttrChangeHC;
			actorAttrChangeHC.set_objid(getObjId());
			actorAttrChangeHC.set_hp(playerAttrib->getHP());
			actorAttrChangeHC.set_behurttarget(getBeHurtTargetID());
			if (m_pWorld && m_pWorld->getMpActorMgr())
				m_pWorld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_ACTOR_ATTR_CHANGE_HC, actorAttrChangeHC, this, false);
		}
		if (reviveType != 3)
		{
			if (GetWorldManagerPtr()->isGameMakerRunMode() || GetWorldManagerPtr()->isAdventureMode())
			{
				// 玩家复活会把最大生命值重置掉
				float maxHP = getAttrib()->getBasicMaxHP();
				if (maxHP <= 0.0f) maxHP = 100.0f;
				float maxStrength = m_PlayerAttrib->getBasicMaxStrength();
				if (maxStrength <= 0.0f) maxStrength = 100.0f;
				if (GetWorldManagerPtr()->isGameMakerRunMode())
				{
					GetWorldManagerPtr()->m_RuleMgr->onPlayerInit(this, true);
				}
				getAttrib()->setBasicMaxHP(maxHP);
				//getAttrib()->setHP(maxHP);
				int reviveHp = GetDefManagerProxy()->getPlayerAttribCsvDef(PlayerAttributeType::Life)->ReviveVal;
				getAttrib()->setHP(reviveHp);
			}
		}

		//getTransformersSkinCom()->onPlayerRevive();
		auto transformsSkinComp = GetComponent<TransformersSkinComponent>();
		if (transformsSkinComp) transformsSkinComp->onPlayerRevive();

		if (!hasUIControl())
		{
			PB_ActorReviveHC actorReviveHC;
			actorReviveHC.set_objid(getObjId());
			actorReviveHC.set_revivetype(reviveType);
			actorReviveHC.set_reviveyaw(getLocoMotion()->m_RotateYaw);
			actorReviveHC.set_revivepitch(getLocoMotion()->m_RotationPitch);
			actorReviveHC.set_mapid(getCurMapID());

			PB_Vector3* revivePos = actorReviveHC.mutable_reviveposition();			
			revivePos->set_x(getPosition().x);
			revivePos->set_y(getPosition().y);
			revivePos->set_z(getPosition().z);

			GetGameNetManagerPtr()->sendToClient(getUin(), PB_ACTOR_REVIVE_HC, actorReviveHC);
			setTeleportPos(getPosition());
		}

		if (reviveType == 1)
		{
			getLivingAttrib()->addBuff(INVULNERABLE_BUFF, 1);
		}

		if (bNeedReviveEquipBuff)
		{
			reviveEquipEffect(1);
		}
#ifdef IWORLD_UNIVERSE_BUILD
		playAnim(SEQ_REVIVE, true);
#else
		playAnim(SEQ_REVIVE_BASIC, true);
#endif

		//复活后需要判断是否还在玩法模式 by：Jeff
		m_OPWay = PLAYEROP_WAY_NORMAL;
		changeOPWay();
		//if (g_pPlayerCtrl && curMapId >= MAPID_EARTHCORE)
		//{
		//	char sMapID[4];
		//	sprintf(sMapID, "%d", curMapId);
		//	char sReviveType[2];
		//	sprintf(sReviveType, "%d", reviveType);
		//	g_pPlayerCtrl->statisticToWorld(getUin(), 30007, "", g_pPlayerCtrl->getCurWorldType(), sMapID, sReviveType);
		//}

		return true;
	}
}

uint64_t ClientPlayer::getSurvivalTime() const
{
	uint64_t current_time = std::chrono::duration_cast<std::chrono::seconds>(
		std::chrono::system_clock::now().time_since_epoch()).count();

	return current_time - m_PlayerStartTimes;
}

//动作，移动相关  begin
bool ClientPlayer::setJumping(bool b)
{
	//跳跃是否中断自动寻路
	if (getNavigator() && getNavigator()->getPath()) {
		if (canControl() && b) {
			getNavigator()->clearPathEntity();
		}
	}

	//篮球模式下不可以跳跃
	bool canJump = true;
	if (getOPWay() == PLAYEROP_WAY_BASKETBALLER)
	{
		canJump = false;
	}
	if (!canJump && b)
	{
		return false;
	}

	if (b)
	{
		auto temperatureComponent = getTemperatureComponent();
		if (temperatureComponent) temperatureComponent->ShakeImpactDuration();
	}

	LivingAttrib* livingAttrib = getLivingAttrib();
	if (livingAttrib && livingAttrib->getBuffEffectBankInfo(BuffAttrType::BUFFATTRT_FORBID_OPERATE)) {
		return false;
	}

	auto RidComp = getRiddenComponent();
	if (/*!m_pWorld->isRemoteMode() && */RidComp && RidComp->isRiding())
	{
		ActorHorse* riding = dynamic_cast<ActorHorse*>(RidComp->getRidingActor());
		if (riding && (riding->getRiddenByActorID() == getObjId()))
		{
			if (b)
			{
				riding->startCharge();
			}
			else
			{
				riding->endCharge();
			}
		}
	}

	//蓄力弹跳组件，兼容老移动逻辑
	if (!(isNewMoveSyncSwitchOn() || isMoveControlActive()))
	{
		if (m_pChargeJumpComponent)
		{
			m_pChargeJumpComponent->updatePlayerChargeJump(b);
		}
	}

	static_cast<LivingLocoMotion*>(getLocoMotion())->setJumping(b);
	return true;
}

void ClientPlayer::tryMoveToActor(long long targetobj, float speed)
{
	ClientActor* pTarget = m_pWorld->getActorMgr()->ToCastMgr()->findActorByWID(targetobj);
	if (!pTarget)
		return;

	m_MotionCtrl.pushback_walkTo(targetobj);
}

void ClientPlayer::tryMoveToPos(int x, int y, int z, float speed)
{
	m_MotionCtrl.pushback_walkTo(WCoord(x, y, z));
}

void ClientPlayer::setMoveForward(float speed)
{
	static_cast<LivingLocoMotion*>(getLocoMotion())->m_MoveForward = speed;
}

void ClientPlayer::setMoveStrafing(float speed)
{
	static_cast<LivingLocoMotion*>(getLocoMotion())->m_MoveStrafing = speed;
}

void ClientPlayer::onMotionCtrlOver(const MotionCtrlParam& param)
{

}

void ClientPlayer::walkForward(int param, int exid)
{
	m_MotionCtrl.pushback_walkForward(param, exid);
}

void ClientPlayer::walkStrafing(int param, int exid)
{
	m_MotionCtrl.pushback_walkStrafing(param, exid);
}

void ClientPlayer::jumpOnce()
{
	ClientActor::jumpOnce();

	//玩家受限于外设输入导致跳跃失败
	LivingLocoMotion* living = static_cast<LivingLocoMotion*>(getLocoMotion());
	if (living != NULL && living->canDoJump()) {
		playAnim(SEQ_JUMP);
		living->doJump();

		living->setJumping(true);
		living->m_JumpingTicks = 10;
	}
}

void ClientPlayer::UseSelectTechBlueprints()
{
	if (isRemote())
	{
		PB_TechBlueprintCH ch;
		GetGameNetManagerPtr()->sendToHost(PB_TechBlueprint_CH, ch);
		return;
	}

	PB_TechBlueprintHC hc;
	WorkbenchTechCsvDef* TechCsvDef;
	TechTree* tree;
	BackPackGrid* grid = getEquipGrid(EQUIP_WEAPON);
	int unlockitemid = 0;

	if (!grid)
	{
#ifdef IWORLD_SERVER_BUILD
		hc.set_code(1);
		GetGameNetManagerPtr()->sendToClient(getUin(), PB_TechBlueprint_HC, hc);
#else
		SandboxEventDispatcherManager::GetGlobalInstance().Emit("TechTree_blueprintlock",
			SandboxContext(nullptr)
			.SetData_Number("code", 1)
		);
#endif
		return;
	}
	if (grid->getItemID() != 2020119)
	{
#ifdef IWORLD_SERVER_BUILD
		hc.set_code(1);
		GetGameNetManagerPtr()->sendToClient(getUin(), PB_TechBlueprint_HC, hc);
#else
		SandboxEventDispatcherManager::GetGlobalInstance().Emit("TechTree_blueprintlock",
			SandboxContext(nullptr)
			.SetData_Number("code", 1)
		);
#endif
		return;
	}

	try
	{
		unlockitemid = std::stoi(grid->getUserdataStr());
	}
	catch (...)
	{

	}
	if (unlockitemid == 0)
	{
#ifdef IWORLD_SERVER_BUILD
		hc.set_code(1);
		GetGameNetManagerPtr()->sendToClient(getUin(), PB_TechBlueprint_HC, hc);
#else
		SandboxEventDispatcherManager::GetGlobalInstance().Emit("TechTree_blueprintlock",
			SandboxContext(nullptr)
			.SetData_Number("code", 1)
		);
#endif
		return;
	}

	TechCsvDef = GetDefManagerProxy()->findItembenchTech(unlockitemid);
	if (!TechCsvDef)
	{
#ifdef IWORLD_SERVER_BUILD
		hc.set_code(1);
		GetGameNetManagerPtr()->sendToClient(getUin(), PB_TechBlueprint_HC, hc);
#else
		SandboxEventDispatcherManager::GetGlobalInstance().Emit("TechTree_blueprintlock",
			SandboxContext(nullptr)
			.SetData_Number("code", 1)
		);
#endif
		return;
	}
	tree = GetDefManagerProxy()->getWorkbenchTechTree(TechCsvDef->WorkbenchLevel);
	if (!tree)
	{
#ifdef IWORLD_SERVER_BUILD
		hc.set_code(1);
		GetGameNetManagerPtr()->sendToClient(getUin(), PB_TechBlueprint_HC, hc);
#else
		SandboxEventDispatcherManager::GetGlobalInstance().Emit("TechTree_blueprintlock",
			SandboxContext(nullptr)
			.SetData_Number("code", 1)
		);
#endif
		return;
	}

	miniw::tech_tree_node* tree_node = getTechTreeNode(TechCsvDef->WorkbenchLevel, TechCsvDef->ID);
#ifdef IWORLD_SERVER_BUILD
	//已经解锁
	if (tree_node && tree_node->is_unlocked())
	{
		return;
	}
#endif
	unlockTechNode(TechCsvDef->WorkbenchLevel, tree->RootId, TechCsvDef->ID);

	//shortcutItemUsed();
	int index = getBackPack()->getShortcutStartIndex() + getPlayerAttrib()->getCurShotcut();
	getBackPack()->removeItem(index, 1);

#ifdef IWORLD_SERVER_BUILD
	hc.set_code(0);
	hc.set_level(TechCsvDef->WorkbenchLevel);
	hc.set_nodeid(TechCsvDef->ID);
	GetGameNetManagerPtr()->sendToClient(getUin(), PB_TechBlueprint_HC, hc);
#else
	SandboxEventDispatcherManager::GetGlobalInstance().Emit("TechTree_blueprintlock",
		SandboxContext(nullptr)
		.SetData_Number("code", 0)
		.SetData_Number("level", TechCsvDef->WorkbenchLevel)
		.SetData_Number("nodeid", TechCsvDef->ID)
	);
#endif

	return;
}

void ClientPlayer::replacePlayer(WCoord pos /* = WCoord(0, -1, 0) */)
{
	if (pos.y > 0)
	{
		getLocoMotion()->gotoPosition(pos, 0, 0);
		m_pWorld->syncLoadChunk(CoordDivSection(getLocoMotion()->m_Position.x), CoordDivSection(getLocoMotion()->m_Position.z));
		CollideAABB box;
		for (;;)
		{
			getCollideBox(box);
			if (m_pWorld->checkNoCollisionBoundBox(box, this)) break;
			getLocoMotion()->setPosition(getLocoMotion()->m_Position.x, getLocoMotion()->m_Position.y + BLOCK_SIZE, getLocoMotion()->m_Position.z);
		}

		return;
	}

	World* newworld = m_pWorld;
	addRef();

	bool bSetWorldMgr = g_pPlayerCtrl && g_pPlayerCtrl == this;
	bool bGameMakerRunMode = GetWorldManagerPtr()->isGameMakerRunMode();

	SandboxContext context(nullptr);
	context.SetData_Number("curToolId", (double)getCurToolID());
	context.SetData_Bool("bSetWorldMgr", bSetWorldMgr);
	context.SetData_Bool("bGameMakerRunMode", bGameMakerRunMode);
	bool succeed_revivept = Event().Emit("replacePlayer", context).IsExecSuccessed();

	if (succeed_revivept)
	{
		if (getLocoMotion()->m_Position.y < -64 * BLOCK_SIZE) //跌落到最低位置 会导致客机复活失效
		{
			gotoSpawnPoint(m_pWorld);
		}
		else
		{
			newworld->syncLoadChunk(CoordDivSection(getLocoMotion()->m_Position.x), CoordDivSection(getLocoMotion()->m_Position.z));
			CollideAABB box;
			int id = 0;
			for (;;)
			{
				getCollideBox(box);

				id = newworld->getBlockID(CoordDivBlock(getLocoMotion()->getPosition()));
				if (newworld->checkNoCollisionBoundBox(box, this) && !IsLavaBlock(id)) break;
				//getLocoMotion()->m_Position.y += BLOCK_SIZE;
				getLocoMotion()->setPosition(getLocoMotion()->m_Position.x, getLocoMotion()->m_Position.y + BLOCK_SIZE, getLocoMotion()->m_Position.z);
			}
		}
	}
	else
	{
		gotoSpawnPoint(newworld);
	}

	//if (changeworld) newworld->getActorMgr()->spawnPlayerAddRef(this);
	release();
}

void ClientPlayer::calUnmountPos(ClientActor* actor)
{
	if (actor == nullptr) return;
	CollideAABB box;
	getCollideBox(box);
	WCoord actorpos = actor->getPosition();

	AntiSetting::ExecAtQuit at_quit;
	at_quit.addExec([&](){
		if (!m_pWorld->isRemoteMode() && !hasUIControl())
			syncPos2Client(true);
	});
	int minx = -150, minz = -150;
	int maxx = 200, maxz = 200;
	ActorVehicleAssemble* vehicle = dynamic_cast<ActorVehicleAssemble*>(actor);
	if (vehicle)
	{
		VehicleAssembleLocoMotion* loc = static_cast<VehicleAssembleLocoMotion*>(vehicle->getLocoMotion());
		vehicle->resetRiddenBindPos();
		actorpos = vehicle->getRiddenBindPos(this);
		if (loc)
		{
			//minx = -350;
			//maxx = 400;
			//minz = -350;
			//maxz = 400;
			getLocoMotion()->setPosition(actorpos);
			for (;;)
			{
				getCollideBox(box);
				if (m_pWorld->checkNoCollisionBoundBox(box, this)) break;
				getLocoMotion()->setPosition(getLocoMotion()->m_Position.x, getLocoMotion()->m_Position.y + BLOCK_SIZE, getLocoMotion()->m_Position.z);
			}
		}
	}
	box = box.getOffsetBox(actorpos - getPosition());
	WCoord retpos = actorpos + WCoord(0, actor->getLocoMotion()->m_BoundHeight + 1, 0);
	if (vehicle)
	{
		// 创建载具时，如果车身的位置过高，会出现下车摔死的情况，优化下车位置，改为驾驶座正上方最近的两格高的空位。
		retpos = WCoord(actorpos.x, 0, actorpos.z) + WCoord(0, actorpos.y + 2 * BLOCK_SIZE + 1, 0);
		getLocoMotion()->setPosition(retpos);
		return;
	}

	if (actor->getObjType() != OBJ_TYPE_SHAPESHIFT_HORSE)
	{
		for (int dx = minx; dx < maxx; dx += 100)
		{
			for (int dz = minz; dz < maxz; dz += 100)
			{
				if (dx == 0 && dz == 0) continue;

				CollideAABB tmpbox = box.getOffsetBox(dx, BLOCK_SIZE, dz);
				if (m_pWorld->checkNoGroundCollision(tmpbox))
				{
					WCoord pos = actorpos + WCoord(dx, 0, dz);
					WCoord blockpos = CoordDivBlock(pos);

					if (m_pWorld->doesBlockHaveSolidTopSurface(blockpos))
					{
						getLocoMotion()->setPosition(pos + WCoord(0, BLOCK_SIZE, 0));
						return;
					}

					WCoord downpos = DownCoord(blockpos);
					if (m_pWorld->doesBlockHaveSolidTopSurface(downpos) || IsWaterBlockID(m_pWorld->getBlockID(downpos)))
					{
						retpos = pos + WCoord(0, BLOCK_SIZE, 0);
					}
					//载具一般比较高，多做两次downpos判断
					if (vehicle)
					{
						CollideAABB vehicleBox;
						vehicle->getHitCollideBox(vehicleBox);
						if (vehicleBox.dim.y < 200)
							continue;
						WCoord downpos1 = DownCoord(downpos);
						if (m_pWorld->doesBlockHaveSolidTopSurface(downpos1) || IsWaterBlockID(m_pWorld->getBlockID(downpos1)))
						{
							retpos = pos + WCoord(0, BLOCK_SIZE, 0);
						}
						else
						{
							WCoord downpos2 = DownCoord(downpos);
							if (m_pWorld->doesBlockHaveSolidTopSurface(downpos2) || IsWaterBlockID(m_pWorld->getBlockID(downpos2)))
							{
								retpos = pos + WCoord(0, BLOCK_SIZE, 0);
							}
						}
					}
				}
			}
		}
	}

	getLocoMotion()->setPosition(retpos);
	if (m_MoveControl && m_MoveControl->isActive())
	{
		m_MoveControl->clearNeedCheck();
	}
}

void ClientPlayer::calFallMotion(float motiony, bool onground)
{
	bool bCanRebounce = (m_pChargeJumpComponent && m_pChargeJumpComponent->canRebounce());

	if ((hasUIControl() && !m_pWorld->isRemoteMode()) || isMoveControlActive() || bCanRebounce)
	{
		FallComponent::calFallMotion_Base(this, motiony, onground);
	}
}

void ClientPlayer::playDigAnim(DIG_METHOD_T digmethod)
{
	int seqid = 0;
	const ToolDef* tooldef = GetDefManagerProxy()->getToolDef(getCurToolID());
	if (tooldef)
	{
		seqid = digmethod == DIG_METHOD_NORMAL ? tooldef->BodyDigSeq : tooldef->BodyAtkSeq;
	}

	if (seqid == 0) seqid = 100105;
	if (seqid == 100105 && isDoubleWeapon(getCurToolID()))
	{
		seqid = TPS_DOUBLEWEAPON_ATTACK;//播放双持武器动作
	}
	if (getBody() && !getBody()->hasAnimPlaying(seqid))
	{
		getBody()->playAnimBySeqId(seqid);
	}
}

void ClientPlayer::setMotionChange(const Rainbow::Vector3f& motion, bool addmotion, bool changepos, bool sync_pos)
{
	m_MotionChangeSyncPos = sync_pos;
	ActorLiving::setMotionChange(motion, addmotion, changepos, sync_pos);
	PlayerLocoMotion* pLocoMotion = static_cast<PlayerLocoMotion*>(getLocoMotion());
	if (pLocoMotion && pLocoMotion->getPhysType() == PHYS_RIGIDBODY)
	{
		pLocoMotion->SetLinearVelocity(pLocoMotion->m_Motion * 2.0f * MOTION2VELOCITY);
	}

	m_LastClientInputMotionY = MAX_INT;
}

void ClientPlayer::gotoPos(World* pworld, const WCoord& targetPos, bool sync2Client)
{
	if (!pworld) return;

	WCoord blockpos = targetPos;
	int x = CoordDivSection(blockpos.x);
	int z = CoordDivSection(blockpos.z);
	if (!m_pWorld->isRemoteMode())
		pworld->syncLoadChunk(x, z);

	float yaw = getLocoMotion()->m_RotateYaw;
	float pitch = getLocoMotion()->m_RotationPitch;
	getLocoMotion()->gotoPosition(blockpos, yaw, pitch);
	auto RidComp = getRiddenComponent();
	if (RidComp && RidComp->isRiding())
	{
		auto ridingActor = RidComp->getRidingActor();
		if (ridingActor && ridingActor->getObjType() != OBJ_TYPE_MINECART)
		{
			ridingActor->getLocoMotion()->gotoPosition(blockpos);
		}
	}

	if (sync2Client && !hasUIControl())
	{
		//LogStringMsg("Bryan gotoPos %d %d %d", targetPos.x, targetPos.y, targetPos.z);
		m_gotoPosChunkXZ.x = x;
		m_gotoPosChunkXZ.z = z;
		m_gotoPosChunkXZ.y = 1;//是标志位，表示要check客机上报位置
		if (m_MoveControl && m_MoveControl->isActive())
			syncPos2Client(true);
		else
			syncPos2Client();
		
	}
}

// 此函数仅支持本地图位置切换
void ClientPlayer::gotoPosEx(World* pworld, int x, int y, int z)
{
	gotoPos(pworld, WCoord(x, y, z), true);
	if (m_CheatData != nullptr)
	{
		m_CheatData->OnGotoPosEx(WCoord(x, y, z));
	}
}

// 客户端无调用了 如果玩家脚本中调用，则也是在服务器触发
void ClientPlayer::teleportPos(int x, int y, int z)
{
	if (!m_pWorld)
		return;

	WCoord targetPos = CoordDivBlock(WCoord(x, y, z));
	m_pWorld->getNearestEmptyChunkCoordinates(targetPos, targetPos);

	targetPos = BlockBottomCenter(targetPos);
	gotoPos(m_pWorld, targetPos, true);
}

//向上螺旋从内向外查找可以复活的点
WCoord ClientPlayer::findNearestVerticalReviveBlockPos(World* pworld, const WCoord& startblock, int radius)
{
	WCoord blockpos = startblock;
	int r = radius ? radius : 5;

	pworld->syncLoadChunk(blockpos, 16);
	if (pworld->getChunk(blockpos) == NULL)
	{
		blockpos = startblock;
	}

	if (g_WorldMgr)
	{
		//寻找合适的位置
		auto FindProperPos = [=](WCoord tmppos) -> int {
			BlockMaterial* materialup = pworld->getBlockMaterial(tmppos + WCoord(0, 1, 0));
			BlockMaterial* materialup2 = pworld->getBlockMaterial(tmppos + WCoord(0, 2, 0));
			BlockMaterial* materialdown = pworld->getBlockMaterial(tmppos);
			if (materialup->isReplaceable() && materialup2->isReplaceable()
				&& !IsLavaBlock(materialup->getBlockResID()) && !IsLavaBlock(materialup2->getBlockResID()) && materialdown->isSolid())
			{
				return tmppos.y + 1;
			}
			return -1;
		};

		//从中心开始，螺旋寻找合适的传送点
		int PosMaxX = 1, PosMaxZ = 1; //边界
		int tempy = -1; //高度
		int tempdir = 0, tempTurnFlag = blockpos.x; //遍历方向及上次转向的节点
		WCoord tempPos = blockpos;
		for (int curY = blockpos.y; curY <= 70; curY++) // 消耗星星复活 0 < y <= 70 找到落脚点，复活到此位置
		{
			tempPos.y = curY;
			tempPos.x = blockpos.x;
			tempPos.z = blockpos.z;
			for (int i = 0; i < r * r; i++)
			{
				tempy = FindProperPos(tempPos);
				if (tempy != -1)
				{
					blockpos.setElement(tempPos.x, tempy, tempPos.z);
					break;
				}

				//x方向移动遍历
				if (tempdir % 2 == 0)
				{
					int offset = tempdir / 2 == 1 ? 1 : -1;

					tempPos.x += offset;
					if (Abs(tempTurnFlag - tempPos.x) == PosMaxX)
					{
						tempTurnFlag = tempPos.z;
						tempdir++;
						PosMaxX++;
					}
				}
				//z方向移动遍历
				else
				{
					int offset = tempdir / 2 == 1 ? 1 : -1;

					tempPos.z += offset;
					if (Abs(tempTurnFlag - tempPos.z) == PosMaxZ)
					{
						tempTurnFlag = tempPos.x;
						tempdir++;
						tempdir %= 4;
						PosMaxZ++;
					}
				}
			}

			if (tempy > 0) //找到了合适的复活位置
				break;
		}
	}

	return blockpos;
}


void ClientPlayer::gotoBlockPos(World* pworld, const WCoord& targetblock, bool random_offset)
{
	WCoord blockpos = targetblock;
	if (random_offset)
	{
		int r = 5;

		SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_getRandSpawnMode",
			SandboxContext(nullptr));
		bool getRandSpawnModeFlag = false;
		if (result.IsExecSuccessed())
		{
			getRandSpawnModeFlag = result.GetData_Bool();
		}
		if (getRandSpawnModeFlag)
		{
			r = 16;
		}

		blockpos.x += GetWorldManagerPtr()->m_DefRandGen->get(-r, r);
		blockpos.z += GetWorldManagerPtr()->m_DefRandGen->get(-r, r);

		pworld->syncLoadChunk(blockpos, 16);
		if (pworld->getChunk(blockpos) == NULL)
		{
			blockpos = targetblock;
		}

		if (g_WorldMgr && g_WorldMgr->isSurviveMode())
		{
			//寻找合适的位置
			auto FindProperPos = [=](WCoord tmppos) -> int {
				tmppos.y += 2;
				BlockMaterial* material = pworld->getBlockMaterial(tmppos);
				while (0 < tmppos.y && blockpos.y - tmppos.y <= 2 && material->isReplaceable())
				{
					tmppos.y--;
					material = pworld->getBlockMaterial(tmppos);
				}

				if (tmppos.y <= blockpos.y + 5)
				{
					BlockMaterial* materialup = pworld->getBlockMaterial(tmppos + WCoord(0, 1, 0));
					BlockMaterial* materialup2 = pworld->getBlockMaterial(tmppos + WCoord(0, 1, 0));
					if (materialup->isReplaceable() && materialup2->isReplaceable()
						&& !IsLavaBlock(materialup->getBlockResID()) && !IsLavaBlock(materialup2->getBlockResID()))
					{
						return tmppos.y + 1;
					}

				}
				return -1;
			};

			//从中心开始，螺旋寻找合适的传送点
			int PosMaxX = 1, PosMaxZ = 1; //边界
			int tempy = -1; //高度
			int tempdir = 0, tempTurnFlag = blockpos.x; //遍历方向及上次转向的节点
			WCoord tempPos = blockpos;
			for (int i = 0; i < 5 * 5; i++)
			{
				tempy = FindProperPos(tempPos);
				if (tempy != -1)
				{
					blockpos.setElement(tempPos.x, tempy, tempPos.z);
					break;
				}

				//x方向移动遍历
				if (tempdir % 2 == 0)
				{
					int offset = tempdir / 2 == 1 ? 1 : -1;

					tempPos.x += offset;
					if (Abs(tempTurnFlag - tempPos.x) == PosMaxX)
					{
						tempTurnFlag = tempPos.z;
						tempdir++;
						PosMaxX++;
					}
				}
				//z方向移动遍历
				else
				{
					int offset = tempdir / 2 == 1 ? 1 : -1;

					tempPos.z += offset;
					if (Abs(tempTurnFlag - tempPos.z) == PosMaxZ)
					{
						tempTurnFlag = tempPos.x;
						tempdir++;
						tempdir %= 4;
						PosMaxZ++;
					}
				}

			}
		}
	}
	else pworld->syncLoadChunk(BlockDivSection(blockpos.x), BlockDivSection(blockpos.z));

	m_nPosXZ = 0;
	m_nPosYPos = 0;
	m_nPosYNeg = 0;
	m_nGuardTick = 0;
	m_nGuardSafeTick = 3000;
	m_nGuardTick = Timer::getSystemTick();

	if (getLocoMotion() == NULL) return;
	if (getLocoMotion())
	{
		getLocoMotion()->gotoPosition(BlockBottomCenter(blockpos), 0, 0);

		//只在冒险模式,卡在方块里设置偏移
		if (g_WorldMgr && (g_WorldMgr->getGameMode() == OWTYPE_SINGLE || g_WorldMgr->getGameMode() == OWTYPE_EXTREMITY || g_WorldMgr->getGameMode() == OWTYPE_FREEMODE))
		{
			CollideAABB tempbox;
			int range = 1;
			getLocoMotion()->getCollideBox(tempbox);
			if (!pworld->checkNoCollisionBoundBox(tempbox, this))
			{
				WCoord temppos;
				for (int i = -range; i <= range; i++)
				{
					for (int j = -range; j <= range; j++)
					{
						temppos.x = getLocoMotion()->m_Position.x + i * BLOCK_SIZE;
						temppos.y = getLocoMotion()->m_Position.y;
						temppos.z = getLocoMotion()->m_Position.z + j * BLOCK_SIZE;

						WCoord temppos2(temppos.x, temppos.y + BLOCK_SIZE, temppos.z);
						if (pworld->isAirBlock(CoordDivBlock(temppos)) && pworld->isAirBlock(CoordDivBlock(temppos2)))
						{
							getLocoMotion()->setPosition(temppos.x, temppos.y, temppos.z);
							break;
						}
					}
				}
			}
		}
	}

	CollideAABB box;
	for (;;)
	{
		if (getLocoMotion())
		{
			getLocoMotion()->getCollideBox(box);
			if (pworld->checkNoCollisionBoundBox(box, this)) break;

			//getLocoMotion()->m_Position.y += BLOCK_SIZE;
			getLocoMotion()->setPosition(getLocoMotion()->m_Position.x, getLocoMotion()->m_Position.y + BLOCK_SIZE, getLocoMotion()->m_Position.z);
		}
	}
}

void ClientPlayer::gotoTeleportPos(World* pworld, const WCoord& blockpos, WCoord& realpos)
{
	if (isStarStationTeleporting())
	{
		pworld->syncLoadChunk(blockpos, 16);
		for (int i = 1; i <= 3; i++)
		{
			pworld->syncLoadChunk(blockpos, 16 * i);
			if (GetStarStationTransferMgr().hasStarStationInCurMap(pworld->getCurMapID()))
			{
				WCoord pos = GetWorldManagerPtr()->getLandingPoint(pworld->getCurMapID());
				if (pos.y >= 0)
				{
					realpos = pos;
					getLocoMotion()->gotoPosition(BlockBottomCenter(pos));
					return;
				}
			}
		}

		//找不到星站，找个安全的位置不卡住角色
		WCoord pos = GetWorldManagerPtr()->getLandingPoint(pworld->getCurMapID());
		while (pos.y < CHUNK_BLOCK_Y)
		{
			if (IsAirBlockID(pworld->getBlockID(pos)) && IsAirBlockID(pworld->getBlockID(pos + WCoord(0, 1, 0))))
			{
				realpos = pos;
				getLocoMotion()->gotoPosition(BlockBottomCenter(pos));
				return;
			}

			pos.y++;
		}
	}

	int r = 5;
	for (int i = 0; i < 20; i++)
	{
		WCoord pos;;
		pos.x = blockpos.x + GenRandomInt(-r, r);
		pos.z = blockpos.z + GenRandomInt(-r, r);

		if (!pworld->syncLoadChunk(BlockDivSection(pos.x), BlockDivSection(pos.z)))
		{
			pos.x = blockpos.x;
			pos.z = blockpos.z;
		}

		for (int y = -1; y <= 1; y++)
		{
			pos.y = blockpos.y + y;

			if (pworld->doesBlockHaveSolidTopSurface(DownCoord(pos)) && !pworld->getBlockMaterial(pos)->defBlockMove() && !pworld->getBlockMaterial(TopCoord(pos))->defBlockMove())
			{
				realpos = pos;
				getLocoMotion()->gotoPosition(BlockBottomCenter(pos));

				CollideAABB box;
				getLocoMotion()->getCollideBox(box);
				if (pworld->checkNoCollisionBoundBox(box, this)) return;
			}
		}
	}

	realpos = blockpos;
	getLocoMotion()->gotoPosition(BlockBottomCenter(blockpos));
}

void ClientPlayer::gotoTransferPos(World* pworld, const WCoord& blockpos)
{
	//从控制台转换到传送仓前
	WCoord temppos = blockpos;
	temppos.x -= 1;
	temppos.z += 1;

	for (int j = 0; j <= 4; j++)
		for (int i = 0; i <= 4; i++)
		{
			WCoord pos;;
			pos.x = temppos.x;
			pos.z = temppos.z;

			if (!pworld->syncLoadChunk(BlockDivSection(pos.x), BlockDivSection(pos.z)))
			{
				pos.x = temppos.x;
				pos.z = temppos.z;
			}

			switch (i)
			{
			case 0:
				break;
			case 1:
			case 3:
				pos.x += i / 2 + 1;
				break;
			case 2:
			case 4:
				pos.x -= i / 2;
				break;
			}

			switch (j)
			{
			case 0:
				break;
			case 1:
			case 3:
				pos.z += j / 2 + 1;
				break;
			case 2:
			case 4:
				pos.z -= j / 2;
				break;
			}

			for (int y = -1; y <= 1; y++)
			{
				pos.y = temppos.y + y;

				if (pworld->doesBlockHaveSolidTopSurface(DownCoord(pos)) && !pworld->getBlockMaterial(pos)->defBlockMove() && !pworld->getBlockMaterial(TopCoord(pos))->defBlockMove())
				{
					getLocoMotion()->gotoPosition(BlockBottomCenter(pos));

					//当前星球如果没有设置复活点，设置第一次传送到星球的星站落地点作为复活点
					GetWorldManagerPtr()->SetDefaultRevivePoint(this, pworld, pos);

					CollideAABB box;
					getLocoMotion()->getCollideBox(box);
					if (pworld->checkNoCollisionBoundBox(box, this)) return;
				}
			}
		}

	getLocoMotion()->gotoPosition(BlockBottomCenter(blockpos));

	GetWorldManagerPtr()->SetDefaultRevivePoint(this, pworld, blockpos);
}

void ClientPlayer::gotoSpawnPoint(World* pworld, bool bRandom /* = true */)
{
	//家园出生点的特殊处理
	SandboxResult homelandSpawnRet = SandboxEventDispatcherManager::GetGlobalInstance().
		Emit("Homeland_gotoSpawnPoint",
			SandboxContext(nullptr)
			.SetData_Userdata("ClientPlayer", "clientplayer", this)
			.SetData_Userdata("World", "world", pworld));
	if (homelandSpawnRet.IsSuccessed())
	{
		return;
	}

	if (pworld == NULL) return;

	if (GetWorldManagerPtr())
	{
		bool random_offset = bRandom && pworld->hasSky();
		if (pworld->getOWID() == NEWBIEWORLDID)
			random_offset = false;
		if (!pworld->isRemoteMode())
			random_offset = false;
		if (pworld->getTerrainType() == TERRAIN_EMPTY_FLAT)
			random_offset = false;
		gotoBlockPos(pworld, GetWorldManagerPtr()->getSpawnPointEx(pworld), random_offset);
	}
}

void ClientPlayer::gotoBlockPos(WCoord& pos)
{
	gotoPos(m_pWorld, BlockBottomCenter(pos));
}

//动作，移动相关 end

void ClientPlayer::tickOperate()
{
	SANDBOXPROFILING_LOG(log, "ClientPlayer::tickOperate");
	if (m_CurOperate != PLAYEROP_DIG) {
		if (g_WorldMgr == NULL) return;
		LivingAttrib* attrib = dynamic_cast<LivingAttrib*> (getAttrib());
		if (attrib && attrib->hasBuff(77, 2))
		{
			attrib->removeBuff(77);
		}
	}
	if (m_CurOperate == PLAYEROP_NULL)
	{
		return;
	}
	m_OperateTicks++;

	if (m_CurOperate == PLAYEROP_EATFOOD || m_CurOperate == PLAYEROP_ATTACK_BOW || m_CurOperate == PLAYEROP_DRINKWATER)
	{
		if (getCurToolID() != m_OperateData)
		{
			setOperate(PLAYEROP_NULL);
			return;
		}
	}
	if (m_CurOperate == PLAYEROP_DIG)
	{
		PlayerAttrib* playerAttrib = m_PlayerAttrib;
		if (!playerAttrib->isStrengthEnough(GetLuaInterfaceProxy().get_lua_const()->strength_consumption_of_digging_per_second))
		{
			setOperate(PLAYEROP_NULL);
			GetLuaInterfaceProxy().showGameTips(1571);
			return;
		}

		//空手挖凝浆块每秒会受到伤害 code by: yangjie
		if (m_CurDigBlockID == BLOCK_COAGULATION && getCurToolID() == 0 && m_OperateTicks % 20 == 0 && !isDead() && !needClear())
		{
			ActorAttrib* attrib = getAttrib();
			if (attrib)
			{
				if (attrib->immuneToFire() <= 0) setFire(100, 1);
				if (attrib->immuneToFire() <= 1)
				{
					auto component = getAttackedComponent();
					if (component)
					{
						component->attackedFromType_Base(ATTACK_FIRE, 1.0f * GetLuaInterfaceProxy().get_lua_const()->yanjiang_shanghai_beilv);
					}
				}
			}
		}
		//空手挖仙人掌会停止当前操作并受到伤害 code by: chenkaite
		else if ((m_CurDigBlockID == BLOCK_CACTUS || m_CurDigBlockID == BLOCK_CACTUSBRANCH || m_CurDigBlockID == BLOCK_CACTUSSEED) && getCurToolID() == 0 && m_OperateTicks % 10 == 0 && !isDead() && !needClear())
		{
			ActorAttrib* attrib = getAttrib();

			if (attrib)
			{
				auto component = getAttackedComponent();
				if (component)
				{
					setFire(77, 1);
					component->attackedFromType(ATTACK_CACTUS, 3.0f);
					Rainbow::Vector3f motion = getLocoMotion()->m_Motion;
					motion.x += 5;
					motion.z += 5;
					setMotionChange(motion);
				}
			}
			m_CurOperate = PLAYEROP_NULL;
			m_OperateTicks = 0;
			//m_CurOperate = PLAYEROP_NULL;
		}
		//挖咒岩每秒会受到伤害 code by: wuweiwei
		else if (m_CurDigBlockID == BLOCK_CURSE_STONE && !isDead())
		{
			LivingAttrib* attrib = dynamic_cast<LivingAttrib*> (getAttrib());
			if (attrib && !attrib->hasBuff(77, 2))
			{
				attrib->addBuff(77, 2, -1, 0);
			}
		}
		else if (m_CurDigBlockID == BLOCK_COCONUE_WOOD && m_OperateTicks > 0 && m_OperateTicks % 20 == 0 && !isDead() && !needClear())
		{
			if (m_pWorld && !m_pWorld->isRemoteMode())
			{
				SandboxCoreSubsystem* module = PluginManager::GetInstancePtr()->FindSubsystem<SandboxCoreSubsystem>();
				if (module == nullptr)
				{
					return;
				}
				if (m_OperateData <= 0)
				{
					if (GenRandomInt(10) >= 4)
					{
						module->clearCoconue();
						module->setCoconutKnock(true);
						m_pWorld->notifyBlock(m_CurDigBlockPos, m_CurDigBlockID);
					}
				}
			}
		}
		//空手挖掘 锯齿蕨
		else if ((m_CurDigBlockID == BLOCK_SAWTOOTH || m_CurDigBlockID == BLOCK_NO_SAWTOOTH) && getCurToolID() == 0 && m_OperateTicks % 10 == 0 && !isDead() && !needClear())
		{
			if (m_CurDigBlockID == BLOCK_SAWTOOTH)
			{
				if (!m_pWorld->isRemoteMode())
				{
					//转换成 无球锯齿蕨
					int blockData = m_pWorld->getBlockData(m_CurDigBlockPos);
					m_pWorld->setBlockAll(m_CurDigBlockPos, BLOCK_NO_SAWTOOTH, blockData & 3);
					ClientActorThornBall::createSawtooth(getWorld(), m_CurDigBlockPos, this);
				}
			}
			auto thornComponent = sureThornBallComponent();
			if (thornComponent != nullptr)
			{
				//弹开
				int tmepdir = thornComponent->checkCrashDir();
				thornComponent->reboundsAttackedRound(3.0f, tmepdir);
			}
			m_CurOperate = PLAYEROP_NULL;
			m_OperateTicks = 0;
		}
		//空手挖掘 锯齿墙
		else if ((m_CurDigBlockID == BLOCK_STONE_WALL
			|| m_CurDigBlockID == BLOCK_MOSSYSTONE_WALL
			|| m_CurDigBlockID == BLOCK_HORASROCK_WALL) && getCurToolID() == 0 && m_OperateTicks % 10 == 0 && !isDead() && !needClear())
		{
			//弹开
			auto thornComponent = sureThornBallComponent();
			if (thornComponent != nullptr)
			{
				int tmepdir = thornComponent->checkCrashDir();
				thornComponent->reboundsAttackedRound(6.0f, tmepdir);
			}
			m_CurOperate = PLAYEROP_NULL;
			m_OperateTicks = 0;
		}
		else if (m_CurDigBlockID == BLOCK_WEAK_ICICLE && m_OperateTicks > 0 && m_OperateTicks % 10 == 0 && !isDead() && !needClear())//挖冰凌
		{
			if (GenRandomInt(100) < 10)
			{
				int blockcount = 0;
				int upblockcount = 0;
				vector<WCoord> posArray;
				WCoord _downblockpos = m_CurDigBlockPos;
				while (m_pWorld->getBlockID(_downblockpos) == BLOCK_WEAK_ICICLE)
				{
					posArray.emplace_back(_downblockpos);
					_downblockpos = _downblockpos - WCoord(0, 1, 0);
					blockcount++;
				}
				WCoord _upblockpos = m_CurDigBlockPos + WCoord(0, 1, 0);
				while (m_pWorld->getBlockID(_upblockpos) == BLOCK_WEAK_ICICLE)
				{
					posArray.emplace_back(_upblockpos);
					_upblockpos = _upblockpos + WCoord(0, 1, 0);
					blockcount++;
					upblockcount++;
				}
				//下方有至少1格空间则坠落并且敲击概率10%
				if (blockcount > 0 && m_pWorld->getBlockID(_downblockpos) == BLOCK_AIR)
				{
					for (int i = 0; i < posArray.size(); i++)
					{
						m_pWorld->setBlockAir(posArray[i]);
					}
					WCoord shootPos = (upblockcount > 0) ? DownCoord(_upblockpos) : m_CurDigBlockPos;
					auto pos = BlockBottomCenter(shootPos);
					Rainbow::GetMusicManager().PlaySound("sounds/item/32/prepare.ogg", pos.toVector3(), 1.0f, 1.0f);
					ClientActorIcicle::shootIcicleAuto(ITEM_WEAK_ICICLE, m_pWorld, BlockBottomCenter(shootPos), blockcount);
				}
			}
		}
	}
	//LOG_INFO("ClientPlayer::tickOperate(): m_OperateTicks = %d", m_OperateTicks);

	if (m_CurOperate == PLAYEROP_EATFOOD)
	{
		const FoodDef* fooddef = GetDefManagerProxy()->getFoodDef(getCurToolID());
		if (fooddef)
		{
			if ((m_OperateTicks % 10) == 1)
			{
				auto soundComp = getSoundComponent();
				if (soundComp)
				{
					if (fooddef->ID == ITEM_BANDAGE)
						soundComp->playSound("misc.bandage", 1.0f, 1.0f);
					else
						soundComp->playSound("misc.eat", 1.0f, 1.0f);
				}
			}
		}
	}
	if (m_CurOperate == PLAYEROP_DRINKWATER)
	{
		const FoodDef* fooddef = GetDefManagerProxy()->getFoodDef(getCurToolID());
		if (fooddef)
		{
			if ((m_OperateTicks % 10) == 1)
			{
				auto soundComp = getSoundComponent();
				if (soundComp)
				{
					soundComp->playSound("misc.drink", 1.0f, 1.0f);
				}
			}
		}
	}
	else if (m_CurOperate == PLAYEROP_DIG)
	{
		//是否可以破坏方块
		if (m_pWorld->IsBrokenBlockEnable(m_CurDigBlockID, getObjId()))
		{
			DIG_METHOD_T digmethod = (DIG_METHOD_T)m_OperateData;
			if (digmethod != DIG_METHOD_CHARGE && (m_OperateTicks % 10) == 0)
			{
				playDigAnim(digmethod);
			}
			if (((m_OperateTicks + 1) % 5) == 0)
			{
				WCoord centerpos = BlockCenterCoord(m_CurDigBlockPos);

				const ToolDef* tooldef = GetDefManagerProxy()->getToolDef(getCurToolID());
				const char* sndname = NULL;
				if (tooldef)
				{
					if (tooldef->Type == 1 && tooldef->Level == 5) sndname = "item.11005.use";
					else if (tooldef->Type == 2 && tooldef->Level == 5) sndname = "item.11015.charge3";
				}

				if (sndname == NULL)
				{
					const BlockDef* def = GetDefManagerProxy()->getBlockDef(m_CurDigBlockID);
					if (def)
						sndname = !def->WalkSound.empty() ? def->WalkSound.c_str() : "blocks.grass";
				}
				if (sndname)
					m_pWorld->getEffectMgr()->playSound(centerpos, sndname, GSOUND_DIG);
			}

			//????????? PLAYEROP_DIG  ?????????????
			if (!isExploiting())
			{
				int stage = 0;
				if (m_OperateTotalTicks > 0) stage = m_OperateTicks * 10 / m_OperateTotalTicks;

				m_pWorld->getEffectMgr()->playBlockCrackEffect(m_CurDigBlockPos, stage, getObjId());
			}
		}
	}
	else if (m_CurOperate == PLAYEROP_SHOOT || m_CurOperate == PLAYEROP_PASS_BALL)
	{
		if (m_OperateData <= GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.ignore_delay_max_charge || m_OperateTicks == 10)
		{
			if (m_pWorld != NULL)
			{
				ClientActor* ball = m_pWorld->getActorMgr()->ToCastMgr()->findActorByWID(m_lOperrateData);
				if (ball)
				{
					kickBall(m_CurOperate, (float)m_OperateData, ball);
				}
			}
		}
	}
	else if (m_CurOperate == PLAYEROP_PUSHSNOWBALL_SHOOT)
	{
		if (m_OperateData <= GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.ignore_delay_max_charge || m_OperateTicks == 10)
		{
			if (m_pWorld != NULL) {
				ClientActor* ball = m_pWorld->getActorMgr()->ToCastMgr()->findActorByWID(m_lOperrateData);
				if (ball)
				{
					kickPushSnowBall(m_CurOperate, (float)m_OperateData, ball);
				}
			}
		}
	}
	else if (PLAYEROP_WAY_BASKETBALLER == m_OPWay)
	{
		ActorBasketBall* pBasketBall = dynamic_cast<ActorBasketBall*>(getCatchBall());
		if (pBasketBall)
		{
			LivingLocoMotion* loc = dynamic_cast<LivingLocoMotion*>(getLocoMotion());
			if (loc && !loc->m_InWater)
			{
				auto functionWrapper = getFuncWrapper();
				bool isflying = functionWrapper ? functionWrapper->getCanFly() : false;
				if (!isflying)
				{
					getBody()->setCurAnim(SEQ_BASKETBALL_DRIBBLE, 1);
					pBasketBall->playDribbleMotion();
				}
			}
			else
			{
				stopAnim(SEQ_BASKETBALL_DRIBBLE);
				pBasketBall->stopDribbleMotion();
			}
		}
		else
		{
			stopAnim(SEQ_BASKETBALL_DRIBBLE);
		}
	}
	/*else if(m_CurOperate == PLAYEROP_USE_ITEM_SKILL)
	{
		 const ItemSkillDef* skilldef = GetDefManagerProxy()->getItemSkillDef(m_nCurItemSkillID);
		 if(skilldef)
		 {
			if(((m_OperateTicks+1)%20) == 0)
			{
				if(skilldef->SoundLoop.size())
				playSound(skilldef->SoundLoop.c_str(), 1.0f, 1.0f);
			}
		 }
	}*/
}


WCoord GetBlockFaceCenter(const WCoord& blockpos, DirectionType dir)
{
	if (dir <= DIR_NOT_INIT || dir >= DIR_COUNT)
	{
		return blockpos * BLOCK_SIZE;
	}

	const int BS = BLOCK_SIZE - 1;
	const int HBS = BLOCK_SIZE / 2;
#if OGRE_PLATFORM == OGRE_PLATFORM_HARMONY
	WCoord facecenter[6] = { WCoord(0,HBS,HBS), WCoord(BS,HBS,HBS), WCoord(HBS,HBS,0), WCoord(HBS,HBS,BS), WCoord(HBS,0,HBS), WCoord(HBS,BS,HBS) };
#else
	static WCoord facecenter[6] = { WCoord(0,HBS,HBS), WCoord(BS,HBS,HBS), WCoord(HBS,HBS,0), WCoord(HBS,HBS,BS), WCoord(HBS,0,HBS), WCoord(HBS,BS,HBS) };
#endif

	return blockpos * BLOCK_SIZE + facecenter[dir];
}


// bound ???  begin
void ClientPlayer::updateBound(int height/*=-1*/, int width/*=-1*/)
{
#ifdef _UPDATE_BOUND_BY_SCALE_
	if (height > 0)
		m_BoundHeight = height;
	if (width > 0)
		m_BoundWidth = width;
	if (getLocoMotion())
	{
		float scale = 1.0f * getCustomScale();
		if (getLocoMotion()->m_BoundHeight != (m_BoundHeight * scale) || getLocoMotion()->m_BoundSize != (m_BoundWidth * scale))
		{
			getLocoMotion()->setBound((int)(m_BoundHeight * scale), (int)(m_BoundWidth * scale));
			// ????????????????????
			if (m_pWorld)
			{
				static_cast<PlayerLocoMotion*>(getLocoMotion())->detachPhysActor();
				static_cast<PlayerLocoMotion*>(getLocoMotion())->attachPhysActor();
			}
		}
	}
#endif
}

void ClientPlayer::updateAttackBound(int height/*=-1*/, int width/*=-1*/, int thickness/*=-1*/)
{
#ifdef _UPDATE_BOUND_BY_SCALE_
	if (height > 0)
		m_AttackBoundHeight = height;
	if (width > 0)
		m_AttackBoundWidth = width;
	if (thickness > 0)
		m_AttackBoundThickness = thickness;
	if (getLocoMotion())
	{
		float scale = 1.0f * getCustomScale();
		getLocoMotion()->setAttackBound((int)(m_AttackBoundHeight * scale), (int)(m_AttackBoundWidth * scale), (int)(m_AttackBoundThickness * scale));
	}
#endif
}

void ClientPlayer::onCollideWithPlayer(ClientActor* player)
{
	ClientActor::onCollideWithPlayer(player);
	auto pTempPlayer = dynamic_cast<ClientPlayer*>(player);
	if (nullptr != pTempPlayer)
	{
		// 	if (this->getCurOperate() == PLAYEROP_BASKETBALL_OBSTRUCT && player->getCurOperate() == PLAYEROP_BASKETBALL_OBSTRUCT)
	// 	{
	// 		this->obstructActorsCollision(player);
	// 		player->obstructActorsCollision(this);
	// 	}
		if (this->getCurOperate() != PLAYEROP_BASKETBALL_OBSTRUCT && pTempPlayer->getCurOperate() == PLAYEROP_BASKETBALL_OBSTRUCT)
		{
			// 		if (this->getCatchBall())
			// 		{
			// 			this->obstructActorsCollision(player);
			// // 			player->obstructActorsCollision(this);
			// 		}
			// 		else
			{
				pTempPlayer->obstructActorCollision(this);
			}
		}
		// 	else if (player->getCurOperate() == PLAYEROP_BASKETBALL_OBSTRUCT)
		// 	{
		// 		player->obstructActorCollision(this);
		// 	}
	}
}

void ClientPlayer::onCollideWithActor(ClientActor* pActor)
{
	if (pActor->isSleeping() || isSleeping() || isRestInBed())
	{
		return;
	}
	if ((getCurOperate() == PLAYEROP_TACKLE ||
		getCurOperate() == PLAYEROP_BASKETBALL_DRIBBLERUN || getCurOperate() == PLAYEROP_BASKETBALL_GRAB))
	{
		ClientActor::applyActorElasticCollision(this);
	}

	doThornBall(pActor);
}

bool ClientPlayer::applyActorElasticCollision(ClientActor* actor)
{
	auto pTempPlayer = dynamic_cast<ClientPlayer*>(actor);
	if (pTempPlayer && (pTempPlayer->getCurOperate() == PLAYEROP_TACKLE || pTempPlayer->getCurOperate() == PLAYEROP_BASKETBALL_DRIBBLERUN
		|| pTempPlayer->getCurOperate() == PLAYEROP_BASKETBALL_GRAB))
	{
		return ClientActor::applyActorElasticCollision(actor);
	}
	return false;
}

bool ClientPlayer::applyActorObstructCollision(ClientActor* actor, ClientActor* player)
{
	auto pTempPlayer = dynamic_cast<ClientPlayer*>(player);
	if (pTempPlayer && pTempPlayer->getCurOperate() == PLAYEROP_BASKETBALL_OBSTRUCT && pTempPlayer->getOPWay() == PLAYEROP_WAY_BASKETBALLER)
	{
		return ClientActor::applyActorObstructCollision(actor, player);
	}
	return false;
}

void ClientPlayer::OnPlayerCustomMessage(int type, const std::string& data)
{
	//暂时只有床的复活功能,后续有其他可以改成注册type的形式
	if (type == 1)
	{
		SocRevivePointComponent *SocRevive = GetComponent<SocRevivePointComponent>();
		if (SocRevive)
			SocRevive->OnNetMessage(data);
		return;
	}
	
	if (type == 2)
	{
		LockCtrlComponent* LockCtrl = GetComponent<LockCtrlComponent>();
		if (LockCtrl)
			LockCtrl->OnNetMessage(data);
		return;
	}
}

void ClientPlayer::obstructActorCollision(ClientPlayer* player)
{
	//if (player->m_RidingActor != getObjId() && player->m_RiddenByActor != getObjId() && !player->getLocoMotion()->m_bIsBlocked)
	auto playerRidComp = player->getRiddenComponent();
	if (!(playerRidComp && (playerRidComp->checkRidingByActorObjId(getObjId()) || playerRidComp->checkRiddenByActorObjId(getObjId()))) && !player->getLocoMotion()->m_bIsBlocked)
	{
		float minDirect = 1.5 * BLOCK_SIZE;
		WCoord dpos = player->getLocoMotion()->getPosition() - getLocoMotion()->getPosition();
		Rainbow::Vector3f dir((float)dpos.x, (float)dpos.y, (float)dpos.z);
		float ddir = sqrtf((float)(dpos.x * dpos.x + dpos.z * dpos.z));
		if (ddir >= minDirect)
		{
			return;
		}
		float direct = minDirect - ddir;
		if (ddir > EPSILON)
		{
			dir.x /= ddir;
			dir.z /= ddir;
		}
		dir.x *= direct;
		dir.z *= direct;
		dir.y = 0;
		player->setMotionChange(Rainbow::Vector3f(dir.x, dir.y, dir.z));
		// 		player->getLocoMotion()->addMotion(dir.x, dir.y, dir.z);
		// 		player->getLocoMotion()->m_Position += dir;

		// 		if (!m_pWorld->isRemoteMode() && player != g_pPlayerCtrl)
		// 		{
		// 			if (player->getWorld() == NULL) return;
		// 			MpActorTrackerEntry *entry = player->getWorld()->getMpActorMgr()->getTrackerEntry(player->getObjId());
		// 			if (entry == NULL) return;
		// 			entry->sendActorMovementToClient(player->getUin(), player, player->getLocoMotion()->m_RotateYaw, player->getLocoMotion()->m_RotationPitch);
		// 		}
	}
}

void ClientPlayer::obstructActorsCollision(ClientPlayer* player)
{
	/*if (player->m_RidingActor != getObjId() && player->m_RiddenByActor != getObjId() &&
		this->m_RidingActor != getObjId() && this->m_RiddenByActor != getObjId())*/
	auto playerRidComp = player->getRiddenComponent();
	auto RidComp = getRiddenComponent();
	if (!(playerRidComp && (playerRidComp->checkRidingByActorObjId(getObjId()) || playerRidComp->checkRiddenByActorObjId(getObjId())))
		&& RidComp && RidComp->checkRidingByActorObjId(getObjId()) && RidComp->checkRidingByActorObjId(getObjId()))
	{
		WCoord dpos = player->getLocoMotion()->getPosition() - this->getLocoMotion()->getPosition();
		Rainbow::Vector3f dir((float)dpos.x, (float)dpos.y, (float)dpos.z);
		float ddir = sqrtf((float)(dpos.x * dpos.x + dpos.z * dpos.z));

		float direct = 3 * BLOCK_SIZE - ddir;

		if (ddir > EPSILON)
		{
			dir.x /= ddir;
			dir.z /= ddir;
		}

		dir.x *= direct;
		dir.z *= direct;
		dir.y = 0;

		player->getLocoMotion()->m_Position += dir;
		this->getLocoMotion()->m_Position -= dir;
	}
}

void ClientPlayer::preMoveTick()
{
	if (m_MoveControl && !hasUIControl())
	{
		checkMoveResult();
	}
	if (isNewMoveSyncSwitchOn() || isMoveControlActive())
		updatePlayer();
}
void ClientPlayer::afterMoveTick()
{
	if (m_MoveControl)
		m_MoveControl->endTick();
}

void ClientPlayer::tick()
{
	if (GetWorldManagerPtr()->isNewSandboxNodeGame())
	{
		return;
	}
	SANDBOXPROFILING_LOG(log, "ClientPlayer::tick");
	//static const std::string ev_srf("StatisticRainforest_PlayerMove"), ev_st("StatisticTerrgen_PlayerMove"), ev_strcm("StatisticTerrgen_ReportChunkMonster");
	//MNSandbox::GetGlobalEvent().Emit<World*, int, const WCoord&>(ev_srf, m_pWorld, getUin(), CoordDivBlock(getLocoMotion()->getPosition()));
	//MNSandbox::GetGlobalEvent().Emit<World*, int, const WCoord&>(ev_st, m_pWorld, getUin(), CoordDivBlock(getLocoMotion()->getPosition()));
	//MNSandbox::GetGlobalEvent().Emit<>(ev_strcm);

	WCoord stBlockPos = CoordDivBlock(getLocoMotion()->getPosition());
	MINIW::StatisticRainforestInterface::getInstance()->onCondition_PlayerMove(m_pWorld, getUin(), stBlockPos);
	MINIW::StatisticTerrgenInterface::getInstance()->onCondition_PlayerMove(m_pWorld, getUin(), stBlockPos);
	MINIW::StatisticTerrgenInterface::getInstance()->onCondition_ReportChunkMonster();

#ifndef IWORLD_SERVER_BUILD
	float scale = (1.0f + getPlayerAttrib()->getModAttrib(MODATTR_ACTOR_SCALE)) * getBody()->getBodyScale() * getCustomScale();

	if (getBody() && scale != getBody()->getRealScale() && !getPlayerAttrib()->hasStatusEffect(STATUS_EFFECT_MODELVOLUME))
	{
		getBody()->setRealScale(scale);
	}

	if (isInSpectatorMode() && g_pPlayerCtrl != this)
	{
		if (getBody())
		{
			getBody()->show(isVisible());
		}
	}

#endif
	ActorLiving::tick();
	//if (GetWorldManagerPtr() && GetWorldManagerPtr()->IsRentServerHost())
	//{
	//	// 云服主机不调update, 所以技能updat放这里调用一下
	//	//技能update
	//	if (getSkillComponent())
	//	{
	//		getSkillComponent()->OnUpdate(GetWorldManagerPtr()->getTickDelta());
	//	}
	//}
	if (getGunLogical())
		getGunLogical()->onUpdate(GetWorldManagerPtr()->getTickDelta());
	
	if (getLocoMotion())
	{
		if (isSleeping() || isRestInBed() || isUseHearth())
		{
			static_cast<LivingLocoMotion*>(getLocoMotion())->setMoveForward(0);
			static_cast<LivingLocoMotion*>(getLocoMotion())->setMoveStrafing(0);
			getLocoMotion()->m_Motion.Set(0.0f, 0.0f, 0.0f);
		}
		else
		{
			if ((!getPlayerAttrib()->isNewStatus() && getPlayerAttrib()->hasBuff(MOVEREVERSE_BUFF)) || getPlayerAttrib()->hasStatusEffect(STATUS_EFFECT_MOVEREVERSE))
			{
				static_cast<LivingLocoMotion*>(getLocoMotion())->m_MoveForward = -m_MoveForward;
				static_cast<LivingLocoMotion*>(getLocoMotion())->m_MoveStrafing = -m_MoveRight;
			}
			else
			{
				static_cast<LivingLocoMotion*>(getLocoMotion())->m_MoveForward = m_MoveForward;
				static_cast<LivingLocoMotion*>(getLocoMotion())->m_MoveStrafing = m_MoveRight;
			}
			if (isFlying())
			{
				float speed = 15.0f;

				//GM模式以及登录账号为外审人员账号的情况下, 使上升下降的速度和移动速度一样
				if (GetClientInfoProxy()->isGMMode() || GetClientInfoProxy()->IsCurrentUserOuterChecker())
				{
					auto funcWrapper = getFuncWrapper();
					speed = funcWrapper ? funcWrapper->getAIMoveSpeed() : 200;
				}

				if (m_MoveUp > 0 && getLocoMotion())
					getLocoMotion()->m_Motion.y += speed;
				else if (m_MoveUp < 0 && getLocoMotion())
					getLocoMotion()->m_Motion.y -= speed;
			}
		}
	}
	//m_totemComp->onTick();
	tickOperate();
	suddenIllnessBuffAttack();
	if (m_SuspicionValue > 0) m_SuspicionValue--;
	if (isCoconutSkipNight())
	{
		m_SkipNightTime++;
	}
	else
	{
		m_SkipNightTime = 0;
	}
	if (isCoconutHit())
	{
		m_TickInHit++;
		if (m_TickInHit > 100)
		{
			m_IsCoconutHit = false;
		}
	}
	else
	{
		m_TickInHit = 0;
	}

	//睡觉暂时给客机加回tick计数
	if (!m_pWorld->isRemoteMode())
	{
		auto sleepState = dynamic_cast<SleepState*>(getLocoCurActionStatePtr("Sleep"));
		if (sleepState)
		{
			sleepState->OnTick(0);
		}
	}

#ifdef IWORLD_DEV_BUILD
	const int TEST_STEP = 10; //10
	if (m_TestItemIconIndex > 0)
	{
		int oldid = m_TestItemIconIndex / TEST_STEP;
		m_TestItemIconIndex++;
		int newid = m_TestItemIconIndex / TEST_STEP;
		if (newid >= GetDefManagerProxy()->getItemNum())
		{
			m_TestItemIconIndex = 0;
			newid = oldid;
			//ge GetGameEventQue().postChatEvent(0, NULL, "end.");
			MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
				SetData_Number("chattype", 0).
				SetData_String("speaker", "").
				SetData_String("content", "end.").
				SetData_Number("uin", 0).
				SetData_Number("language", 0).
				SetData_String("chatextend", "");
			if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
				MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_UPDATE_CHATMSG", sandboxContext);
			}
		}
		if (oldid != newid)
		{
			BackPack* bp = getBackPack();
			onSetCurShortcut(0);
			bp->discardItem(getShortcutStartIndex(), -1);
			while (GetDefManagerProxy()->getItemDef(newid) == NULL)
			{
				m_TestItemIconIndex += TEST_STEP;
				newid++;
				if (newid >= GetDefManagerProxy()->getItemNum())
				{
					m_TestItemIconIndex = 0;
					break;
				}
			}

			if (m_TestItemIconIndex > 0)
			{
				getBackPack()->addItem(newid, 1);
				char content[256];
				sprintf(content, "Item_%d: %s", newid, GetDefManagerProxy()->getItemDef(newid)->Name.c_str());
				//ge GetGameEventQue().postChatEvent(0, NULL, content);
				MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
					SetData_Number("chattype", 0).
					SetData_String("speaker", "").
					SetData_String("content", content).
					SetData_Number("uin", 0).
					SetData_Number("language", 0).
					SetData_String("chatextend", "");
				if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
					MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_UPDATE_CHATMSG", sandboxContext);
				}
			}
		}
	}
#endif
#ifdef IWORLD_SERVER_BUILD
	if (getSurviveDay() > m_surviveDays)
	{
		if (m_surviveDays != -1)
		{
			addAchievement(3, ACHIEVEMENT_SURVIVEDAY, -1);
			if (m_pWorld && !m_pWorld->isRemoteMode())
			{
				addSFActivity(SFACTIVITY_SAFEDAY, 1, 1, !this->hasUIControl());
				//updateTaskSysProcess(TASKSYS_SURVIVE_DAY, m_pWorld->getCurMapID());
			}
			auto def = GetDefManagerProxy()->getExtremityScoreDef(ACHIEVEMENT_SURVIVEDAY);
			if (def)
				addOWScore(def->Score);

			//?????, ????????????????, ?????1????????(?????????e??)
			ReportSurviveDay();
		}

		m_surviveDays = getSurviveDay();
	}

	if (m_pWorld && getSurviveDay(m_pWorld->getCurMapID()) > m_suriveDaysByWorld)
	{
		if (m_suriveDaysByWorld != -1)
		{
			addAchievement(3, ACHIEVEMENT_SURVIVEDAY, m_pWorld->getCurMapID());
			if (m_pWorld && !m_pWorld->isRemoteMode())
			{
				addSFActivity(SFACTIVITY_SAFEDAY, 1, 1, !this->hasUIControl());
				updateTaskSysProcess(TASKSYS_SURVIVE_DAY, m_pWorld->getCurMapID());
			}
		}

		m_suriveDaysByWorld = getSurviveDay(m_pWorld->getCurMapID());
	}

	//m_pSkillCDComp->onUpdate(1.0 / 20.0f);
	auto skillCDComp = m_SkillCDComponent;
	if (skillCDComp) skillCDComp->onUpdate(1.0 / 20.0f);
#endif

	if (m_CurPlaySnd)
	{
		if (m_CurPlaySndToolID > 0 && m_CurPlaySndToolID != getCurToolID())
			playToolSound(-1, true);
		else
			m_CurPlaySnd->setPosition(getPosition().toVector3());
	}

	if (m_CurPlayEffectToolID > 0 && m_CurPlayEffectToolID != getCurToolID())
		playToolEffect(-1, true);

	//getItemSkillComponent()->onTick();

	if (!m_pWorld->isRemoteMode())
	{
		m_WorldTimes[getWorld()->getCurMapID()]++;
		if ((m_WorldTimes[getWorld()->getCurMapID()] % 201) == 0) sendWorldTimesUpdate();

		//if ((m_WorldTimes[getWorld()->getCurMapID()] % 1200) == 0 && g_pPlayerCtrl)
		//{
		//	auto worldDesc = GetClientAccountMgr().getCurWorldDesc();
		//	if (worldDesc)
		//	{
		//		char sWorldId[64];
		//		sprintf(sWorldId, "%lld", worldDesc->worldid);

		//		char sMapId[4];
		//		sprintf(sMapId, "%d", getWorld()->getCurMapID());

		//		//g_pPlayerCtrl->statisticToWorld(getUin(), 30009, "", worldDesc->worldtype, sWorldId, sMapId);
		//	}
		//}
	}

	//m_pChangeColorComp->onTick();

	std::string cmurl, mapmd5;
	long long mapID;

	//???????????
	if (!m_pWorld->isRemoteMode() && !GetIClientGameManagerInterface()->getCMURL(cmurl, mapmd5, mapID))
	{
		if (m_SyncCustomModelTick >= 2 && CustomModelMgr::GetInstancePtr())
		{
			m_SyncCustomModelTick = 0;
			int cal = m_bLoadSpeedUp ? 3 : 1;
			for (int i = 0; i < cal; i++)
			{
				if (CustomModelMgr::GetInstancePtr()->syncCustomModelDataPre(this, m_CurSyncCustomModelIndex))
					m_CurSyncCustomModelIndex++;
				else
					break;
			}
		}
		m_SyncCustomModelTick++;
	}
	//????崫??????
	if (!m_pWorld->isRemoteMode() && m_SynTransferTick >= 0)
	{
		if (m_SynTransferTick % 5 == 0 && TransferMgr::GetInstancePtr())
		{
			bool bSyncTransferData = TransferMgr::GetInstancePtr()->syncTransferData(this, m_CurSynTransferIndex);
			if (bSyncTransferData)
				m_CurSynTransferIndex++;
			else
				m_SynTransferTick = -1;
		}
		m_SynTransferTick++;
	}

	////?????????
	StarStationTransferMgr::getSingleton().Tick(this);

	//???????λ??
	//getActorBindVehicle()->updatePosByVehicle();
	auto comp = getActorBindVehicle();//by__Logo
	if (comp)
		comp->updatePosByVehicle();
	m_PlayerStateMgr.tick(); // 玩家状态管理

	if (!m_InteractPoseidonStatue)
	{
		m_InteractPoseidonStatueTime++;
		if (m_InteractPoseidonStatueTime >= GetLuaInterfaceProxy().get_lua_const()->interact_poseidon_status_time)//24000
		{
			m_InteractPoseidonStatue = true;
			m_InteractPoseidonStatueTime = -1;
		}
	}
	if (isAttrShapeShift())
	{
		if (getPlayerAttrib() && getPlayerAttrib()->m_AttrShapeShiftDef && getPlayerAttrib()->m_AttrShapeShiftDef->ID == 3224)
		{
			if (m_Body && m_Body->getModel())// 海灵守卫需要一直保持最亮模型
			{
				m_Body->getModel()->SetInstanceData(Rainbow::Vector4f(0.8f, 0.8f, 0.8f, 0));
			}
		}
	}
	if (getAge() % 5 == 0)
	{
		if (isPlayerControl())
		{
			bool needRefresh = true;
			PlayerControl* player = dynamic_cast<PlayerControl*>(this);
			if (!player->isSightMode())
			{
				resetBusinessIcon();
				if (GetClientInfoProxy()->isMobile())
				{
					refreshBusinessmanHeadIcon();
				}
			}
			else
			{
				auto objId = player->m_PickResult.m_PickObjId;
				auto actor =m_pWorld->getActorMgr()->ToCastMgr()->findActorByWID(objId);
				if (player->m_PickResult.intersect_actor && checkIsBusinessman(actor))
				{
					if (objId != curShowDialogId)
					{
						resetBusinessIcon();
					}
					else
					{
						needRefresh = false;
					}
					curShowDialogId = objId;
				}
				else
				{
					resetBusinessIcon();
				}

			}
			if (needRefresh && curShowDialogId != 0)//商人加上对话框提示
			{
				auto actor = m_pWorld->getActorMgr()->ToCastMgr()->findActorByWID(curShowDialogId);
				if (actor)
				{
					if (actor->getDefID() == 3222)
					{
						auto man = dynamic_cast<ActorIslandBusInessMan*>(actor);
						if (man && !man->checkHasAttackTarget())
						{
							actor->setHeadIconByPath("", "particles/texture/tipIcon", 54, 54, false);
						}
					}
					else
					{
						bool isShowHeadIcon = true;
						//商人攻击、逃跑状态不打开交易界面	
						ClientMob* mob = dynamic_cast<ClientMob*>(actor);
						if (mob)
						{
							if (mob->getPanic() || mob->getHasAttackTarget())
							{
								isShowHeadIcon = false;
							}
						}
						if (isShowHeadIcon)
						{
							actor->setHeadIconByPath("", "particles/texture/tipIcon", 54, 54, false);
						}
						else
						{
							actor->setHeadIconByPath("", "", 0, 0, false);
						}
					}
				}
			}
		}

	}
	//移动是否停止自动寻路
	if (getNavigator() && getNavigator()->getPath()) {
		if (canControl())
		{
			if (isMoveControlActive())
			{
				if (m_MoveControl)
				{
					const auto& opera = m_MoveControl->getCurrentStatus();
					if (opera.count(IMT_Forward) || opera.count(IMT_Back) || opera.count(IMT_Right) || opera.count(IMT_Left))
						getNavigator()->clearPathEntity();
				}
			}
			else if (hasUIControl())
			{
				if (m_MoveForward != 0 || m_MoveRight != 0)
					getNavigator()->clearPathEntity();
			}
		}
	}

	ChargeJumpComponent* chargeJumpComp = getChargeJumpComponent();
	if (chargeJumpComp)
	{
		chargeJumpComp->tickCharge();

		if (m_OldJumpCharge != chargeJumpComp->getCurCharge())
		{
			m_OldJumpCharge = chargeJumpComp->getCurCharge();

			if (MNSandbox::SandboxCoreDriver::GetInstancePtr() && hasUIControl())
			{
				MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr);
				sandboxContext.SetData_Number("curCharge", chargeJumpComp->getCurCharge());
				sandboxContext.SetData_Number("maxCharge", chargeJumpComp->getMaxChargeSpeed());
				MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_JUMP_CHARGE_UPDATE", sandboxContext);
			}
		}
	}
	if (m_pWorld->getCurWorldTick() % 50 == 0)//每50tick上报玩家所在子地形
	{
		WCoord pos = CoordDivBlock(getPosition());
		int biomeId = m_pWorld->getBiomeId(pos.x, pos.z);
		if (m_biomeBeenTo.find(biomeId) == m_biomeBeenTo.end())
		{
			m_biomeBeenTo[biomeId] = true;
			if (biomeId >= BIOME_PLAINS_ARID && biomeId <= BIOME_ISLAND_SHORE_TULIP)
			{
				//上报 6月花海版本子地形上报统计
				MINIW::ScriptVM::game()->callFunction("FlowersActivity_GameEventReport", "sii", "arrive_biome", 1202, biomeId);
			}
			string strOWID = to_string(m_pWorld->getOWID());
			MINIW::ScriptVM::game()->callFunction("SetMapBiomeWDescEx", "sss", strOWID.c_str(), "biome_str", to_string(biomeId).c_str());
		}
	}
	if(g_pPlayerCtrl == this)
	{
		Rainbow::GetLegacyGlobalShaderParamManager()->m_PlayerPosition = getPosition().toVector3();
	}
	if (m_pWorld->isRemoteMode())
	{
		return;
	}
	if (isAttrShapeShift())
	{
		attrShapeShiftTick();
	}
	else
	{
		if (m_AttrShapeShiftTick > 0)
		{
			m_AttrShapeShiftTick = 0;
			m_AttrStopRecoverTick = 0;
			m_AttrRightClickTick = 0;
		}
	}
	//getAccountHorseComponent()->onTick();

	if (!isDead())
	{
		CollideAABB box;
		getCollideBox(box);
		if (m_pWorld->getOWID() == NEWBIEWORLDID && GetClientInfoProxy()->getCurGuideLevel() == 1 && GetClientInfoProxy()->getCurGuideStep() == 10)
			box.expand((int)(BLOCK_SIZE * 1.5), (int)(BLOCK_SIZE / 2), (int)(BLOCK_SIZE));
		else if (getOPWay() == PLAYEROP_WAY_FOOTBALLER)
		{
			if (getCurOperate() == PLAYEROP_TACKLE)
				box.expand(
					(int)GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.tackle_box_ex_size,
					(int)GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.tackle_box_ex_height,
					(int)GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.tackle_box_ex_size);
			else
				box.expand(5, 0, 5);
		}
		else if (getOPWay() == PLAYEROP_WAY_BASKETBALLER)
		{
			if (getCurOperate() == PLAYEROP_BASKETBALL_GRAB)
			{
				box.expand(GetWorldManagerPtr()->m_SurviveGameConfig->basketballConfig.grab_box_ex_size, GetWorldManagerPtr()->m_SurviveGameConfig->basketballConfig.grab_box_ex_height, GetWorldManagerPtr()->m_SurviveGameConfig->basketballConfig.grab_box_ex_size);
			}
			else if (getCurOperate() == PLAYEROP_BASKETBALL_BLOCK_SHOT)
			{
				//??????????????е???
				box.expand(5, BLOCK_SIZE, 5);
			}
			else if (getCurOperate() == PLAYEROP_BASKETBALL_OBSTRUCT)
			{
				box.expand(BLOCK_SIZE, 0, BLOCK_SIZE);
			}
			else
			{
				box.expand(5, 0, 5);
			}
		}
		else if (getOPWay() == PLAYEROP_WAY_PUSHSNOWBALL)
		{
			if (getCurOperate() == PLAYEROP_TACKLE)
				box.expand(
					(int)GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.tackle_box_ex_size,
					(int)GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.tackle_box_ex_height,
					(int)GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.tackle_box_ex_size);
			else
				box.expand(5, 0, 5);
		}
		else
			box.expand(BLOCK_SIZE * m_checkboxscale, BLOCK_SIZE / 2 * m_checkboxscale, BLOCK_SIZE * m_checkboxscale);

		static std::vector<IClientActor*>actors;
		actors.clear();
		m_pWorld->getActorsInBoxExclude(actors, box, this);
		for (size_t i = 0; i < actors.size(); i++)
		{
			ClientActor* actor = static_cast<ClientActor*>(actors[i]);
			if (!actor->needClear())
			{
				actor->onCollideWithPlayer(this);
			}
		}


	}

	if (isInvulnerable(NULL))
	{
		getLivingAttrib()->addOxygen(20);
	}

	checkDungeonPos();

	for (auto it = m_TackleEffects.begin(); it != m_TackleEffects.end();)
	{
		it->ticks--;
		if (it->ticks <= 0)
		{
			it->effect->setNeedClear();
			it->effect = NULL;
			it = m_TackleEffects.erase(it);
		}
		else
			++it;
	}

	if (getCurOperate() == PLAYEROP_TACKLE || getCurOperate() == PLAYEROP_BASKETBALL_GRAB)
	{
		WCoord pos = CoordDivBlock(getPosition());
		pos.y -= 1;
		int blockid = m_pWorld->getBlockID(pos);
		if (blockid > 0)
		{
			WCoord pos1 = GetBlockFaceCenter(pos, DIR_POS_Y);
			TackleEffectData data;
			data.ticks = 8;
			data.effect = m_pWorld->getEffectMgr()->playBlockDestroyEffect(1, pos1, DIR_POS_Y, MAX_INT);
			if (data.effect)
				m_TackleEffects.push_back(data);
		}
	}

	if (m_OpenDialogueMobID > 0)
	{
		ClientMob* mob = dynamic_cast<ClientMob*>(m_pWorld->getActorMgr()->ToCastMgr()->findActorByWID(m_OpenDialogueMobID));
		if (mob && getPosition().squareDistanceTo(mob->getPosition()) >= 300000) //??????????
		{
			closePlotDialogue();
			if (hasUIControl())
			{
				//ge GetGameEventQue().postClosePlotDialogue();
				if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
					MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GIE_CLOSE_DIALOGUE", MNSandbox::SandboxContext(nullptr));
			}
			else
			{
				PB_CloseDialogueHC closeDialogueHC;
				GetGameNetManagerPtr()->sendToClient(getUin(), PB_CLOSEDIALOGUE_HC, closeDialogueHC);
			}
		}
	}

	m_helloTick--;
	//getTransformersSkinCom()->onTick();
	if (m_CheatData)
	{
		m_CheatData->Tick();
	}
	if (m_FlagBitChanged)
	{ // 向客机同步flag 2023.10.30 by huanglin
		if (GetGameNetManagerPtr() && GetGameNetManagerPtr()->isHost() && !hasUIControl())
		{  // 为节约只主机向其它客机发送
			game::hc::PB_ResetRoleFlagsHC pbHC;
			pbHC.set_flags(m_Flags);
			pbHC.set_types(m_FlagBitChanged);
			GetGameNetManagerPtr()->sendToClient(getUin(), PB_RESET_ROLE_FLAGS, pbHC);
		}
		m_FlagBitChanged = 0;
	}
}

//ActorBindVehicle* ClientPlayer::getActorBindVehicle()
//{
//	if(m_pActorBindVehicle)
//		return m_pActorBindVehicle;
//	m_pActorBindVehicle = new PlayerBindVehicle(this);
//	return m_pActorBindVehicle;
//}

void ClientPlayer::update(float dtime)
{
	if (GetWorldManagerPtr()->isNewSandboxNodeGame())
	{
		return;
	}
	ActorLiving::update(dtime);
	for (auto item : mNeedUpdateChunk)
	{
		(item.second)(dtime);
	}

	//	if(m_UIModelView) m_UIViewBody->update(dtime);
#ifndef IWORLD_SERVER_BUILD
	//if (getSurviveDay() > m_surviveDays)
	//{
	//	if (m_surviveDays != -1)
	//	{
	//		addAchievement(3, ACHIEVEMENT_SURVIVEDAY, -1);
	//		if (m_pWorld && !m_pWorld->isRemoteMode())
	//			addSFActivity(SFACTIVITY_SAFEDAY, 1, 1, !this->hasUIControl());
	//		auto def = GetDefManagerProxy()->getExtremityScoreDef(ACHIEVEMENT_SURVIVEDAY);
	//		if (def)
	//			addOWScore(def->Score);

	//		//?????, ????????????????, ?????1????????(?????????e??)
	//		ReportSurviveDay();
	//	}

	//	m_surviveDays = getSurviveDay();
	//}

	//if (m_pWorld && getSurviveDay(m_pWorld->getCurMapID()) > m_suriveDaysByWorld)
	//{
	//	if (m_suriveDaysByWorld != -1)
	//	{
	//		addAchievement(3, ACHIEVEMENT_SURVIVEDAY, m_pWorld->getCurMapID());
	//		if (m_pWorld && !m_pWorld->isRemoteMode())
	//			addSFActivity(SFACTIVITY_SAFEDAY, 1, 1, !this->hasUIControl());
	//	}

	//	m_suriveDaysByWorld = getSurviveDay(m_pWorld->getCurMapID());
	//}

	//if (m_pGunComponent != NULL && m_pGunComponent->getGunDef() != NULL)
	//	m_pGunComponent->update(dtime);

	//m_pSkillCDComp->onUpdate(dtime);
	//auto skillCDComp = GetComponent<SkillCDComponent>();
	//if (skillCDComp) skillCDComp->onUpdate(dtime);
#endif

	m_MotionCtrl.update(dtime);

	//if (m_TriggerSounder != NULL) {
	//	m_checkTime += dtime;
	//	if (m_checkTime >= 0.5f) {
	//		bool isLooping = m_TriggerSounder->getIsLoop();
	//		bool isPlaying = m_TriggerSounder->isPlaying();
	//		if (!isLooping && !isPlaying) {
	//			OGRE_DELETE(m_TriggerSounder);

	//			//post a finished play event
	//			GetGameEventQue().postFinishPlaySound();
	//		}
	//		m_checkTime = 0.0f;
	//	}
	//}

	//getTransformersSkinCom()->onUpdate(dtime);
	checkShowMusicClubChatBubble(dtime);	//�������������� 2021-09-25 codeby: luoshuai
//#ifndef IWORLD_SERVER_BUILD
//	//技能更新
//	if (getSkillComponent())
//	{
//		getSkillComponent()->OnUpdate(dtime);
//	}
//#endif

	if (m_pFishLineComponent && m_pFishLineComponent->IsHoldFishPole())
		m_pFishLineComponent->OnUpdate();
}

void ClientPlayer::renderUI()
{
	if (m_TemperatureComponent == nullptr)
	{
		m_TemperatureComponent = getTemperatureComponent();
	}
	if (m_TemperatureComponent) m_TemperatureComponent->RenderUI();

	if (m_RadiationComponent == nullptr)
	{
		m_RadiationComponent = getRadiationComponent();
	}
	if (m_RadiationComponent) m_RadiationComponent->RenderUI();

	/*if (m_VacantEffectComponent == nullptr)
	{
		m_VacantEffectComponent = GetComponent<VacantEffectComponent>();
	}*/
	if (m_VacantEffectComponent) m_VacantEffectComponent->RenderUI();
}

bool ClientPlayer::ProcessDie()
{
	onDie();
	return true;
}

// 重新进房间的时候，如果血量为0，也会进这个函数，但是由于在数据加载过程中，m_pWorld还没有设置 实际不会执行后续逻辑
// 所以不会出现重复掉落的问题
void ClientPlayer::onDie()
{
	if (m_pWorld == NULL || GetWorldManagerPtr() == NULL) return;
	if (!m_pWorld->isRemoteMode())
	{
		auto component = getThornBallComponent();
		if (component)
		{
			//刺球掉落
			int ownerCount = component->getThornAnchorNum();
			if (ownerCount > 0)
			{
				component->removeThornBallModel(ownerCount);
				component->dropThornBall(ownerCount);
			}
		}
	}
	if (!m_pWorld->isRemoteMode() && isAttrShapeShift() && m_PlayerAttrib)
	{
		m_PlayerAttrib->setHP(1);//锁血防止死亡。
		m_PlayerAttrib->clearBuff(true);
		return;
	}

	if (!m_pWorld->isRemoteMode())
	{
		this->OnDieForTrigger();
	}
	SetOffline(true);//设置成睡觉状态
	if (!m_pWorld->isRemoteMode() && GetWorldManagerPtr()->isGameMakerRunMode())
	{
		int killedby = 0;
		WORLD_ID byobjid = 0;
		if (OnHurtByActor()) killedby = killedByActor(&byobjid);

		GetWorldManagerPtr()->m_RuleMgr->addTeamDieTimes(getTeam());
		static_cast<GameMode*>(GetWorldManagerPtr()->m_RuleMgr)->callEventScript(WES_PLAYER_DIE, this, (void*)long(killedby));

		if (!m_pWorld->isRemoteMode())
		{
			// 直接死亡的时候就掉落 点击复活的时候不用掉落了
			getLivingAttrib()->clearrevive(0);  

			ActorPlayerCorpse::CreateFromPlayer(this);//创建尸体，把所有装备转移到尸体  todo 支持活着的玩家再打开这个功能 移除
			// 将玩家传送到虚空（高空位置 y=300）
			WCoord currentPos = getPosition();//CoordDivBlock(getPosition());
			WCoord voidPos(currentPos.x, 300, currentPos.z); // 设置到虚空高度300
			gotoPos(m_pWorld, voidPos, true);
		}
		if (GetCheatHandler())
			GetCheatHandler()->onPlayerDie();
	}

	ClientMob* mob = dynamic_cast<ClientMob*>(m_pWorld->getActorMgr()->ToCastMgr()->findActorByWID(getBeHurtTargetID()));
	if (mob) {
		MNSandbox::GetGlobalEvent().Emit<const int>("StatisticRainforest_MonsterKillPlayer", mob->getDefID());
		MNSandbox::GetGlobalEvent().Emit<const int, int>("StatisticTerrgen_PlayerKillMonster", mob->getDefID(), 2);
		ObserverEvent_Actor obevent(mob->getObjId(), getObjId());
		obevent.SetData_Actor(mob->getDefID());
		if (IsTriggerCreature())
		{
			obevent.SetData_TargetActorID(getObjType());
		}
		GetObserverEventManager().OnTriggerEvent("Actor.Beat", &obevent);
	}

	m_DieTimes++;

	//if (!checkRidingByActorObjId(0)) mountActor(NULL);   //这个分支基类里少了很多东西，暂时屏蔽
	auto RidComp = getRiddenComponent();
	if (RidComp && !(RidComp->checkRidingByActorObjId(0))) mountActor(NULL);
	//死亡时掉下发射器
	if (getUsingEmitter())
	{
		disMountEmitter(getUsingEmitterBlockPos());
	}
	m_CWKills = 0;
	getLocoMotion()->onDie();
	getAttrib()->onDie();
	getBody()->onDie();

	closeContainer();

	ParticlesComponent::playParticles(this, "1002.ent");

	// int cleartype = 0;
	// if (GetWorldManagerPtr()->isGodMode() || m_pWorld->getOWID() == NEWBIEWORLDID) cleartype = 2;
	// else if (GetWorldManagerPtr()->isGameMakerRunMode())
	// {
	// 	cleartype = (int)GetWorldManagerPtr()->m_RuleMgr->getRuleOptionVal(GMRULE_PLAYERDIE_DROPS);
	// 	if (!checkActionAttrState(ENABLE_DEATHDROPITEM))
	// 		cleartype = 2;
	// }
	// float reliveTime = 0;
	// int mode = getWorld()->getReviveMode(reliveTime);
	// if (GetWorldManagerPtr()->isGameMakerRunMode() && mode == 1)
	// 	getLivingAttrib()->clearrevive(cleartype);

	m_nPosYPos = 0.0f;
	m_nPosYNeg = 0.0f;
	m_nPosXZ = 0.0f;
	m_nGuardSafeTick = 10000000;
	m_nGuardTick = Timer::getSystemTick();

	auto* ball = getCatchBall();
	if (ball)
	{
		auto pComp = ball->getBindActorCom();
		if (nullptr != pComp)
		{
			pComp->setBindInfo(-getObjId(), WCoord(0, 0, 0));
		}
		else
		{
			auto pBasketballlComp = ball->getBindActorCom();
			if (nullptr != pBasketballlComp)
			{
				pBasketballlComp->setBindInfo(-getObjId(), WCoord(0, 0, 0));
			}
		}
	}

	ClientActor* actor = getCatchGravityActor();
	if (actor)
		doPutGravityActor(actor);

	if (!m_pWorld->isRemoteMode())
	{
		if (RidComp && RidComp->getRidingActor())
			mountActor(NULL, true);

		auto CarryComp = getCarryComponent();
		if (CarryComp && CarryComp->getCarringActor())
			carryActor(NULL, getPosition());

		if (isSittingInStarStationCabin())
			standUpFromChair();

		//if (g_pPlayerCtrl && m_pWorld->getCurMapID() >= MAPID_MENGYANSTAR)
		//{
		//	char sMapID[4];
		//	sprintf(sMapID, "%d", m_pWorld->getCurMapID());
		//	g_pPlayerCtrl->statisticToWorld(getUin(), 30006, "", g_pPlayerCtrl->getCurWorldType(), sMapID);
		//}
	}
	//地心人偷取标记重置
	setCurStealMonster(0);

	this->tryStopPianoSoundAndPaticle();

	// 联机的客机死亡，主机给他的超时时间提升一下，避免因为广告掉线
	if (GameNetManager::getInstance() && !isSimPlayer())
	{
#ifdef IWORLD_SERVER_BUILD
		GameNetManager::getInstance()->m_HostRecvHeartBeart[getUin()] = Timer::getSystemTick() + 60000;
#else
		if (GetClientInfoProxy()->getMultiPlayer() == GAME_NET_MP_GAME_HOST_AND_CLIENT &&
			getUin() != GameNetManager::getInstance()->getServerUin())
		{
			GameNetManager::getInstance()->m_HostRecvHeartBeart[getUin()] = Timer::getSystemTick() + 60000;
		}
#endif
	}
}


//block 相关 begin
bool ClientPlayer::placeBlock(int blockid, int x, int y, int z, int face, float facept_x /*= 0*/, float facept_y /*= 0*/, float facept_z /*= 0*/, bool placeinto /*= false*/, bool isagainplace /*= false*/, int extendData/* = 0*/, World* world/* = NULL*/)
{
	World* pworld = world ? world : getWorld();
	if (pworld == NULL) return false;
	WCoord placepos(x, y, z);
	if (m_pWorld->IsBlockProtected(placepos, blockid))
	{
		//char content[256];
		//sprintf(content, "%s", "You Are Not allow place block here");
		/*	PB_ChatHC chatHC;
			chatHC.set_chattype(5);
			chatHC.set_content(content);
			chatHC.set_speaker("");
			chatHC.set_uin(0);
			GetGameNetManagerPtr()->sendToClient(getUin(), PB_CHAT_HC, chatHC, 0, false);*/
		notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 10000200);
		//notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 0, 0, content);
		return false;
	}

	std::ostringstream oss;
	oss << "[" << x << "," << y << "," << z << "]";
	std::string location = oss.str();

	GameAnalytics::TrackEvent("block_place", {
		{"block_id",blockid},
		{"location",location}
	});


	bool canplace = false;
	if (hasUIControl() || true) //主客机都走这里
	{
		if (!pworld->canPlaceActorOnSide(blockid, placepos, false, face, NULL, Rainbow::Vector3f(facept_x, facept_y, facept_z), placeinto, this))
		{
			if (pworld->canPlaceActorOnSide(blockid, placepos, false, face, this, Rainbow::Vector3f(facept_x, facept_y, facept_z), placeinto, this) && getLocoMotion()->m_OnGround)
			{
				CollideAABB box, box2;
				getCollideBox(box);
				box = box.getOffsetBox(0, BLOCK_SIZE, 0);
				box2.setPosDim(placepos * BLOCK_SIZE, WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
				BlockMaterial* newmtl = g_BlockMtlMgr.getMaterial(blockid);
				if (newmtl == NULL) return false;
				//if (!hasUIControl()) box.pos.y += 5;//客机偶尔会低一点
				if (!box.intersect(box2) && pworld->checkNoCollisionBoundBox(box, this) || (box.intersect(box2) && newmtl->BlockTypeId() == BlockMaterial::BlockType::BlockType_Slab))
				{
					static_cast<PlayerLocoMotion*>(getLocoMotion())->moveUpByUnderFootPlaceBlock(blockid);
					canplace = true;
				}
			}
		}
		else canplace = true;
	}
	//else canplace = pworld->canPlaceActorOnSide(blockid, placepos, false, face, NULL, Rainbow::Vector3f(facept_x, facept_y, facept_z), placeinto, this);

	if (!GetCheatHandler()->CheckPlaceBlockInstance(placepos))
		canplace = false;

	//使用区域工具时不放置方块
	if (GetWorldManagerPtr() && (GetWorldManagerPtr()->isAreaToolMode() || GetWorldManagerPtr()->isDisplayBoardToolMode()))
		canplace = false;

	//家园区域，限定区域摆放

	SandboxResult homelandRet = SandboxEventDispatcherManager::GetGlobalInstance().
		Emit("Homeland_canEditBlock", SandboxContext(nullptr).SetData_UserObject("placepos", placepos));
	if (homelandRet.IsSuccessed())
	{
		if (!homelandRet.GetData_Bool("canplace"))
			canplace = false;
	}

	/*
	if(pworld->canPlaceActorOnSide(blockid, placepos, false, face, this))
	{
	if(!pworld->canPlaceActorOnSide(blockid, placepos, false, face, NULL)) //�Լ����赲
	{
	CollideAABB box;
	getCollideBox(box);
	box.pos.y = (y+1)*BLOCK_SIZE;
	if(pworld->checkNoCollisionBoundBox(box, this))
	{
	canplace = true;
	static_cast<LivingLocoMotion *>(getLocoMotion())->doJump();
	setMotionChange(true);
	//jumpOnce();
	//getLocoMotion()->m_Motion.y += 40.0f;
	//getLocoMotion()->m_Position.y = y*BLOCK_SIZE;
	}
	}
	else canplace = true;
	}*/

	if (canplace)
	{
		BlockMaterial* newmtl = g_BlockMtlMgr.getMaterial(blockid);

		if (newmtl == NULL) return false;

		int blockdata = newmtl->getPlaceBlockData(pworld, placepos, (DirectionType)face, facept_x, facept_y, facept_z, 0);
		if (blockdata < 0) return false;
		blockdata = newmtl->getPlaceBlockDataWithPlayer(pworld, this, placepos, (DirectionType)face, facept_x, facept_y, facept_z, 0);

		if (newmtl->hasContainer())
		{
			std::ostringstream oss;
			oss << "[" << placepos.x << "," << placepos.y << "," << placepos.z << "]";
			std::string location = oss.str();

			GameAnalytics::TrackEvent("chest_place", {
				{"chest_id",blockid},
				{"chest_type",newmtl->getClassName()},
				{"location",location}
			});
		}

		bool sucPlace = false; //记录方块是否真正放下去 家园有判断自建区才能放

		if (!isagainplace)
		{
			sucPlace = pworld->setBlockAll(placepos, blockid, blockdata, 3);
		}

		if (pworld->getBlockID(placepos) == blockid)
		{
			int flag = (strcmp(newmtl->getClassName(), "BlockOneQuarter") == 0) ? 1 : 0;
			//if (strcmp(newmtl->getClassName(), "BlockOneQuarter") == 0) //1/4方块可以在一个格子内放置多次
			//{
			//	newmtl->onBlockPlacedBy(pworld, placepos, this, Rainbow::Vector3f(facept_x, facept_y, facept_z), placeinto, face);
			//}
			//else
			//{
			//	newmtl->onBlockPlacedBy(pworld, placepos, this);
			//}
			newmtl->DoOnBlockPlacedBy(pworld, placepos, this, flag, Rainbow::Vector3f(facept_x, facept_y, facept_z), placeinto, face);
			// 观察者事件接口
			ObserverEvent_ActorBlock obevent((long long)getObjId(), newmtl->getBlockResID(), placepos.x, placepos.y, placepos.z);
			GetObserverEventManager().OnTriggerEvent("Block.PlaceBy", &obevent);
			if (blockid == ITEM_BLOCKMODELCRAFT ||
				blockid == ITEM_ITEMMODELCRAFT ||
				blockid == ITEM_CUSTOMMODELMAKER)
			{
				MINIW::ScriptVM::game()->callFunction("ItemPlace_OnBlock", "i", newmtl->getBlockResID());
			}
			// 用来保存改变的block坐标
			std::vector<WCoord> changeBlocksPos;
			changeBlocksPos.push_back(placepos);

			SandboxEventDispatcherManager::GetGlobalInstance().Emit("VehicleMgr_resetWorkshopConnectCoreBlock",
				SandboxContext(nullptr)
				.SetData_UserObject("changeBlocksPos", changeBlocksPos));

			//染色方块，放置时将userdata_str解析为colordata传入block by：Jeff
			if (getCurToolID() == blockid && IsDyeableBlock(blockid))
			{
				BackPackGrid* pCurTool = getPlayerAttrib()->getEquipGrid(EQUIP_WEAPON);
				int colordata = 0;
				sscanf(pCurTool->userdata_str.c_str(), "%d", &colordata);
				TriggerBlockAddRemoveDisable Tmp2(m_pWorld); //染色方块不多次触发创建和删除
				m_pWorld->setBlockAll(placepos, blockid, colordata, 2);
			}
		}
		if (GetWorldManagerPtr() && GetWorldManagerPtr()->getSpecialType() == HOME_GARDEN_WORLD)
		{
			if (sucPlace)
			{
				shortcutItemUsed();
			}
		}
		else
		{
			if (blockid == BLOCK_CANVAS)
			{
				int stage = extendData;
				if (stage > 2 || stage < 0)
				{
					stage = 0;
				}

				WorldCanvas* container = dynamic_cast<WorldCanvas*>(pworld->getContainerMgr()->getContainer(placepos));
				if (!container)
				{
					container = ENG_NEW(WorldCanvas)(placepos);
					pworld->getContainerMgr()->spawnContainer(container);
				}
				if (container)
				{
					container->setStage(stage);
				}
			}

			if (blockid == BLOCK_GIANT_SCALLOPS_CLOSE)
			{
				GiantScallopsContainer* container = dynamic_cast<GiantScallopsContainer*>(pworld->getContainerMgr()->getContainer(placepos));
				if (!container)
				{
					container = ENG_NEW(GiantScallopsContainer)(placepos);
					pworld->getContainerMgr()->spawnContainer(container);
				}
			}

			if (blockid == BLOCK_FAR_DRIFTBOTTLE || blockid == BLOCK_DRIFTBOTTLE)
			{
				WorldStringContainer* container = dynamic_cast<WorldStringContainer*>(pworld->getContainerMgr()->getContainer(placepos));
				if (container && getPlayerAttrib())
				{
					BackPackGrid* grid = getPlayerAttrib()->getEquipGrid(EQUIP_WEAPON);
					if (grid)
					{
						container->m_Str = grid->userdata_str;
					}
				}
			}

			bool ignoreDurable = false;
			if (IsVoidMelonBlock(blockid))
			{
				ignoreDurable = true;
			}

			shortcutItemUsed(ignoreDurable);
		}


		if (blockid == BLOCK_CRYSTAL)
		{
			if (pworld->getOWID() == NEWBIEWORLDID && GetClientInfoProxy()->getCurGuideLevel() == 1 && GetClientInfoProxy()->getCurGuideStep() == 14)
			{
				checkNewbieWorldProgress(1, 14);
				MINIW::ScriptVM::game()->callFunction("UpdateOperateFinger2Info", "iii", placepos.x * 100 + 50, placepos.y * 100 + 50, placepos.z * 100 + 50);
			}
		}

		if (blockid == BLOCK_SAPLING_HOLY && GetWorldManagerPtr() && m_pWorld)  //神圣树苗
		{
			GetWorldManagerPtr()->addHolyTreePlantInfo(placepos, m_pWorld->getCurMapID());
		}

		const BlockDef* def = GetDefManagerProxy()->getBlockDef(blockid);
		if (def == NULL) return false;

		//由Block def中的高度，设置方块的data，第三个bit代表 block的上面
		for (int i = 1; i < def->Height; i++)
		{
			pworld->setBlockAll(placepos + WCoord(0, i, 0), blockid, blockdata | 8, 3);
		}

		MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
			SetData_UserObject("placePos", placepos);
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYER_PLACE_BLOCK", sandboxContext);
		}

		playBlockPlaceSound(blockid, placepos.x, placepos.y, placepos.z);
		//查找定义添加时间
		pworld->setPlantTime(placepos.x, placepos.y, placepos.z, pworld->getCurMapID());
		return true;
	}

	return false;
}

static void RecursiveVisitBlocks(World* pworld, const WCoord& curblock, int targetblockid, const ToolDef* tooldef, std::map<WCoord, int>& visitedblocks, bool include_leaf)
{
	if (visitedblocks.find(curblock) != visitedblocks.end()) return; //�Ѿ����ʹ�

	int maxCheckNum = 256; //��������� ����Ǵ�Ҷ�ӵ� ������һ��������
	if (include_leaf)
		maxCheckNum = 512;

	if (visitedblocks.size() >= (unsigned int)maxCheckNum)
		return;

	visitedblocks[curblock] = targetblockid;

	for (int i = 0; i < 6; i++)
	{
		WCoord ng = NeighborCoord(curblock, i);
		int ngid = pworld->getBlockID(ng);

		if (IsWoodBlockID(ngid) || IsBranchBlockID(ngid)) //ľͷ�����ľͷ����
		{
			if (!IsWoodBlockID(targetblockid) && !IsBranchBlockID(targetblockid)) continue;
		}
		else
		{
			if (include_leaf)
			{
				if (!IsLeavesBlockID(ngid)) continue;
			}
			else continue;
		}

		RecursiveVisitBlocks(pworld, ng, ngid, tooldef, visitedblocks, include_leaf);
	}
}
int GetMultiDigBlocks(World* pworld, const WCoord& targetblock, int targetblockid, const ToolDef* tooldef, std::vector<WCoord>* retblocks)
{
	int num = 0;
	if (tooldef == NULL) return 0;
	if (tooldef->Level == 3)
	{
		if (IsWoodBlockID(targetblockid))
		{
			num++;
			if (retblocks)
				retblocks->push_back(targetblock);

			WCoord checkBlocksArray[3] = { targetblock, TopCoord(targetblock), DownCoord(targetblock) };
			for (int i = 0; i < 3; i++)
			{
				int tmpBlockId = pworld->getBlockID(checkBlocksArray[i]);
				if (IsWoodBlockID(tmpBlockId))
				{
					num++;
					if (retblocks)
						retblocks->push_back(checkBlocksArray[i]);

					//����ľͷ ��Χ2Ȧ �Ƿ�����֦ �еĻ� �͸ɵ�  ��һȦ����������  �ڶ�Ȧ����
					for (int j = 0; j < 4; j++)
					{
						WCoord ngBlock = NeighborCoord(checkBlocksArray[i], j);
						int blockid = pworld->getBlockID(ngBlock);
						if (IsBranchBlockID(blockid))
						{
							num++;
							if (retblocks)
								retblocks->push_back(ngBlock);

							for (int k = 0; k < 6; k++)
							{
								WCoord ngBlock2 = NeighborCoord(ngBlock, k);
								int blockid2 = pworld->getBlockID(ngBlock2);
								if (IsBranchBlockID(blockid2))
								{
									num++;
									if (retblocks)
										retblocks->push_back(ngBlock2);
								}
							}
						}
					}
				}
			}
		}
		// 只能对树干右键，对树枝右键不行 20220531 shitengkai
		//else if (IsBranchBlockID(targetblockid))
		//{
		//	num++;
		//	if (retblocks)
		//	{
		//		retblocks->push_back(targetblock);
		//	}
		//	for (int i = 0; i < 6; i++)
		//	{
		//		WCoord ngBlock = NeighborCoord(targetblock, i);
		//		int blockid = pworld->getBlockID(ngBlock);
		//		if (IsBranchBlockID(blockid))
		//		{
		//			num++;
		//			if (retblocks)
		//			{
		//				retblocks->push_back(ngBlock);
		//			}
		//			for (int j = 0; j < 6; j++)
		//			{
		//				WCoord ngBlock2 = NeighborCoord(ngBlock, j);
		//				int blockid2 = pworld->getBlockID(ngBlock2);
		//				if (IsBranchBlockID(blockid2))
		//				{
		//					num++;
		//					if (retblocks)
		//					{
		//						retblocks->push_back(ngBlock2);
		//					}
		//				}
		//			}
		//		}
		//	}
		//}
	}
	else if (tooldef->Level > 3)
	{
		if (IsWoodBlockID(targetblockid)) {		// 只能对树干右键，对树枝右键不行 20220531 shitengkai
			std::map<WCoord, int>visitedblocks;
			RecursiveVisitBlocks(pworld, targetblock, targetblockid, tooldef, visitedblocks, tooldef->Level == 4 || tooldef->Level == 5);

			std::map<WCoord, int>::iterator iter = visitedblocks.begin();
			for (; iter != visitedblocks.end(); iter++)
			{
				if (IsWoodBlockID(iter->second) || IsBranchBlockID(iter->second))
				{
					num++;
				}
				if (retblocks)
				{
					retblocks->push_back(iter->first);
				}
			}
		}
	}

	return num;
}


bool BlockByScoop(int blockid)
{
	return blockid == BLOCK_DIRT
		|| blockid == BLOCK_SAND
		|| blockid == BLOCK_SOLIDSAND
		|| blockid == BLOCK_GRAVEL
		|| blockid == BLOCK_GRASS
		|| blockid == BLOCK_FARMLAND
		|| blockid == BLOCK_BURYLAND
		|| blockid == BLOCK_HOTSAND
		|| blockid == BLOCK_REDSOIL
		|| blockid == BLOCK_SNOW
		|| blockid == BLOCK_FARMLAND_PIT
		|| blockid == BLOCK_FARMLAND_RED
		|| blockid == BLOCK_DIRT_FREEZE
		|| blockid == BLOCK_DIRT_FREEZE_PIT;
}

bool ClientPlayer::isSkillCD()
{
	int toolid = getCurToolID();
	if (toolid == 0) return false;

	if (getSkillCD(toolid) > 0) return true;

	return false;
}

//bool ClientPlayer::checkCanDigBlcok(const WCoord &targetblock, int status, DIG_METHOD_T digmethod)
//{
//	if (!m_pWorld)
//		return false;
//
//	// 这两种挖掘模式需要验证CD
//	if (digmethod == DIG_METHOD_CHARGE || digmethod == DIG_METHOD_MULTI)
//	{
//		if (isSkillCD())
//			return false;
//	}	
//
//	int blockid = m_pWorld->getBlockID(targetblock);
//	if (m_pWorld->getCurMapID() == MAPID_LIEYANSTAR)
//	{
//		if (blockid == BLOCK_KEY_OF_BROKEN_SWORD && (status == PLAYEROP_STATUS_BEGIN || digmethod == DIG_METHOD_CHARGE))
//		{
//			return m_pWorld->blockKeyOnTrigger(targetblock, this);
//			//WorldMapData *mapdata = g_WorldMgr->getMapData(m_CurMapID);
//			//if (mapdata == NULL || mapdata->bosses.empty() || mapdata->bosses[0].defid == 3502)	 //没有boss,或者是第一阶段的boss（未激活状态）
//			//{
//			//	
//			//	return false;
//			//}
//
//		}
//	}
//
//	if (!GetCheatHandler()->CheckDigInstance(targetblock, digmethod))
//		return false;
//
//	return true;
//}

bool ClientPlayer::checkGameRule(ActorLiving* atkliving)
{
	//判断当前规则是否为自由攻击
	bool allow = false;
	if (GetWorldManagerPtr() && GetWorldManagerPtr()->m_RuleMgr)
	{
		int opt = GetWorldManagerPtr()->m_RuleMgr->getRuleOptionVal(GMRULE_ATTACKPLAYER);
		if (opt == 0)
		{
			allow = true;
		}
	}
	if (!allow)
	{
		//不是自由攻击的话，同队之间不能攻击
		if (atkliving != NULL && isSameTeam(atkliving))
		{
			return false;
		}
	}
	return true;
}

//bool ClientPlayer::checkCanDigBlcokWithVehicle(const WCoord& targetblock, int status, DIG_METHOD_T digmethod, ActorVehicleAssemble* pVeh)
//{
//	if (!pVeh || !pVeh->getWorld() || !pVeh->getVehicleWorld())
//		return false;
//	auto pWorld = pVeh->getWorld();
//	auto pVehWorld = pVeh->getVehicleWorld();
//	// 这两种挖掘模式需要验证CD
//	if (digmethod == DIG_METHOD_CHARGE || digmethod == DIG_METHOD_MULTI)
//	{
//		if (isSkillCD())
//			return false;
//	}
//
//	int blockid = pVehWorld->getBlockID(targetblock);
//	if (pWorld->getCurMapID() == MAPID_LIEYANSTAR)
//	{
//		if (blockid == BLOCK_KEY_OF_BROKEN_SWORD && (status == PLAYEROP_STATUS_BEGIN || digmethod == DIG_METHOD_CHARGE))
//		{
//			return pVehWorld->blockKeyOnTrigger(targetblock, this);
//			//WorldMapData *mapdata = g_WorldMgr->getMapData(m_CurMapID);
//			//if (mapdata == NULL || mapdata->bosses.empty() || mapdata->bosses[0].defid == 3502)	 //没有boss,或者是第一阶段的boss（未激活状态）
//			//{
//			//	
//			//	return false;
//			//}
//
//		}
//	}
//	
//	WCoord blockPos = CoordDivBlock(pVeh->getRealWorldPosWithPos(targetblock));;
//
//	if (!GetCheatHandler()->CheckDigInstance(blockPos, digmethod))
//		return false;
//
//	return true;
//}

int ClientPlayer::getCurPlaceDir()
{
	int curdir = ClientActor::getCurPlaceDir();
	if (getCurToolID() != ItemIDs::BLUEPRINT)//不是 建筑图纸 ，走原来的逻辑
		return curdir;

	PlayerAttrib* playerAttrib = getPlayerAttrib();
	if (!playerAttrib)
		return curdir;
    int dirArray[4]= {0, 3, 1, 2};
	// 找到当前方向在dirArray中的索引
	int cur_idx = 0;
	for (int i = 0; i < 4; ++i) {
		if (dirArray[i] == curdir) {
			cur_idx = i;
			break;
		}
	}
	int rotate = playerAttrib->getRotateBuildingPreview();
	int new_idx = (cur_idx + rotate) % 4; // 0-3
	return dirArray[new_idx];
}

void ClientPlayer::setFaceYaw(float yaw, bool needSync /*= false*/)
{
	ClientActor::setFaceYaw(yaw, needSync);
	if (needSync)
	{
		PB_PlayerCommonSetHC playerfaceHC;
		playerfaceHC.set_uin(getUin());
		playerfaceHC.set_value(yaw);
		if (g_WorldMgr && !g_WorldMgr->isRemote())
		{
			// ??????
			//	1????PlayerControl?????????; 2????PlayerControl???player???????????????
			if (!isPlayerControl())
			{
				GameNetManager::getInstance()->sendToClient(getUin(), PB_PLAYER_FACE_YAW_HC, playerfaceHC);
			}
		}
	}
}

void ClientPlayer::checkCollideOnTrigger(ClientActor* actor)
{
	if (!g_WorldMgr || !m_pWorld || m_pWorld->isRemoteMode() || !actor)
	{
		return;
	}

	if (actor->isDead() || this->isDead())
	{
		return;
	}

	auto RidComp = getRiddenComponent();
	if (!(RidComp && (RidComp->checkRidingByActorObjId(getObjId()) || RidComp->checkRiddenByActorObjId(getObjId()))))
	{
		auto triggerComponent = getTriggerComponent();
		if (triggerComponent)
		{
			auto iter = triggerComponent->getCollideActors().find(actor->getObjId());
			if (iter == triggerComponent->getCollideActors().end() || iter->second >= 10)
			{
				ObserverEvent_Player obevent(getUin(), actor->getObjId());
				ObserverEventManager::getSingleton().OnTriggerEvent("Player.Collide", &obevent);
				if (iter == triggerComponent->getCollideActors().end())
				{
					triggerComponent->getCollideActors()[actor->getObjId()] = 0;
					triggerComponent->BindOnTick();
				}
				else
				{
					iter->second = 0;
				}
			}
		}
	}
}

bool ClientPlayer::attackedFromTypeByPlayer(ATTACK_TYPE atktype, float atkpoints, ClientActor* attacker /*= NULL*/)
{
	OneAttackData atkdata;
	//memset(&atkdata, 0, sizeof(atkdata));
	atkdata.damage_armor = true;
	atkdata.atktype = atktype;
	atkdata.atkpoints = atkpoints;
	atkdata.fromplayer = dynamic_cast<ClientPlayer*>(attacker);
	auto component = getAttackedComponent();
	if (component)
	{
		return component->attackedFrom_Base(atkdata, attacker);
	}
	return false;
}

bool ClientPlayer::attackedVehicleFromTypeByPlayer(ATTACK_TYPE atktype, float atkpoints, CollideAABB& box, ClientActor* attacker /*= NULL*/)
{
	OneAttackData atkdata;
	//memset(&atkdata, 0, sizeof(atkdata));
	atkdata.damage_armor = true;
	// 新伤害计算系统 code-by:liya
	if (GetLuaInterfaceProxy().get_lua_const()->isOpenNewHpdecCalculate && ((atktype >= ATTACK_PUNCH && atktype <= MAX_MAGIC_ATTACK) || atktype == PHYSICS_ATTACK))
	{
		const int index = AtkType2ArmorIndex(atktype);
		atkdata.atkTypeNew = (1 << index);
		if (atktype == ATTACK_EXPLODE)
			atkdata.explodePoints[0] = atkpoints;
		else
			atkdata.atkPointsNew[index] = atkpoints;
	}
	else
	{
		atkdata.atktype = atktype;
		atkdata.atkpoints = atkpoints;
	}
	atkdata.fromplayer = dynamic_cast<ClientPlayer*>(attacker);
	return false;
	//return ClientActor::attackedVehicleFrom(atkdata, attacker, box);
}

//bool ClientPlayer::checkCanDigBlcok(const WCoord &targetblock, int status, DIG_METHOD_T digmethod)
//{
//	if (!m_pWorld)
//		return false;
//
//	// ?????????????????CD
//	if (digmethod == DIG_METHOD_CHARGE || digmethod == DIG_METHOD_MULTI)
//	{
//		if (isSkillCD())
//			return false;
//	}	
//
//	int blockid = m_pWorld->getBlockID(targetblock);
//	if (m_pWorld->getCurMapID() == MAPID_LIEYANSTAR)
//	{
//		if (blockid == BLOCK_KEY_OF_BROKEN_SWORD && (status == PLAYEROP_STATUS_BEGIN || digmethod == DIG_METHOD_CHARGE))
//		{
//			return m_pWorld->blockKeyOnTrigger(targetblock, this);
//			//WorldMapData *mapdata = g_WorldMgr->getMapData(m_CurMapID);
//			//if (mapdata == NULL || mapdata->bosses.empty() || mapdata->bosses[0].defid == 3502)	 //???boss,??????????ε?boss??δ????????
//			//{
//			//	
//			//	return false;
//			//}
//
//		}
//	}
//
//	return true;
//}

//bool ClientPlayer::digBlock(const WCoord& targetblock, DirectionType targetface, int status, DIG_METHOD_T digmethod)
//{
//
//	// ???
//	SandboxResult homelandRet = SandboxEventDispatcherManager::GetGlobalInstance().
//		Emit("Homeland_canEditBlock", SandboxContext(nullptr).SetData_UserObject("placepos", targetblock));
//	if (homelandRet.IsSuccessed())
//	{
//		if (!homelandRet.GetData_Bool("canplace"))
//			return false;
//	}
//
//	if (m_pWorld && !m_pWorld->isRemoteMode() && !checkCanDigBlcok(targetblock, status, digmethod))
//		return false;
//
//	PlayerAttrib* playerAttrib = getPlayerAttrib();
//	if (!playerAttrib->isStrengthEnough(GetLuaInterfaceProxy().get_lua_const()->strength_consumption_of_digging_per_second))
//	{
//		setOperate(PLAYEROP_NULL);
//		GetLuaInterfaceProxy().showGameTips(1571);
//		return false;
//	}
//
//	//LOG_INFO("digBlock(): status = %2d | digmethod = %2d", status, digmethod);
//
//	setCurItemSkillID(0);
//	if (status == PLAYEROP_STATUS_BEGIN)
//	{
//		int mineticks;
//		int blockid = 0;
//		if (GetClientInfoProxy()->IsCurrentUserOuterChecker()) //?????????????鲻???????, ??????????????????
//		{
//			if (digmethod == DIG_METHOD_CHARGE || digmethod == DIG_METHOD_MULTI)
//			{
//				digmethod = DIG_METHOD_NORMAL;
//			}
//		}
//
//		if (digmethod == DIG_METHOD_CHARGE)
//		{
//			m_MineType = BLOCK_MINE_NONE;
//			mineticks = g_WorldMgr->isGodMode() ? 5 : 3 * 20;
//			const ToolDef *tooldef = GetDefManagerProxy()->getToolDef(getCurToolID());
//			if (tooldef && tooldef->Type == 2 && tooldef->Level == 5) playSound("item.11015.charge1", 1.0f, 1.0f);
//		}
//		else
//		{
//			blockid = m_pWorld->getBlockID(targetblock);
//			if (blockid == 0) return false;
//			if (GetClientInfoProxy()->IsCurrentUserOuterChecker())
//			{
//				mineticks = 0;
//			}
//			else
//			{
//				mineticks = getMineBlockTicks(getCurToolID(), blockid, m_pWorld->getBlockData(targetblock), &m_MineType);
//			}
//
//			if (digmethod == DIG_METHOD_MULTI)
//			{
//				int nblock = GetMultiDigBlocks(m_pWorld, targetblock, blockid, GetDefManagerProxy()->getToolDef(getCurToolID()));
//				if (nblock == 0) return false;
//				mineticks = (mineticks * nblock) * 2 / 3;
//			}
//		}
//
//		if (m_pWorld && !m_pWorld->isRemoteMode())
//		{
//			// ???????????
//			ObserverEvent_PlayerBlock obevent(getUin(), m_pWorld->getBlockID(targetblock), targetblock.x, targetblock.y, targetblock.z);
//			ObserverEventManager::getSingleton().OnTriggerEvent("Block.Dig.Begin", &obevent);
//		}
//
//		setOperate(PLAYEROP_DIG, mineticks, digmethod);
//		m_CurDigBlockID = blockid;
//		m_CurDigBlockPos = targetblock;
//
//		if (digmethod != DIG_METHOD_CHARGE) playDigAnim(digmethod);
//
//		if (m_OperateTotalTicks > 0 && digmethod != DIG_METHOD_CHARGE)
//		{
//			WCoord pos = GetBlockFaceCenter(targetblock, targetface);
//			assert(m_DigEffect == NULL);
//			if (!m_DigEffect)
//			{
//				m_DigEffect = m_pWorld->getEffectMgr()->playBlockDestroyEffect(1, pos, targetface, MINIW::MAX_INT);
//			}
//			m_pWorld->getEffectMgr()->playBlockCrackEffect(targetblock, 0, getObjId());
//		}
//	}
//	else
//	{
//		assert(status == PLAYEROP_STATUS_END || status == PLAYEROP_STATUS_CANCEL);
//		const ToolDef *tooldef = GetDefManagerProxy()->getToolDef(getCurToolID());
//		if (tooldef && tooldef->Type == 2 && tooldef->Level == 5) playSound("item.11015.charge2", 1.0f, 1.0f);
//
//		bool target_checkok = true;
//		int targetblockid = m_pWorld->getBlockID(targetblock);
//		if (m_pWorld && !m_pWorld->isRemoteMode())
//		{
//			// ???????????
//			ObserverEvent_ActorBlock obevent(getObjId(), targetblockid, targetblock.x, targetblock.y, targetblock.z);
//			ObserverEventManager::getSingleton().OnTriggerEvent(status == PLAYEROP_STATUS_END ? "Block.Dig.End" : "Block.Dig.Cancel", &obevent);
//		}
//		if (digmethod != DIG_METHOD_CHARGE)
//		{
//			target_checkok = (m_CurDigBlockPos == targetblock && m_CurDigBlockID == targetblockid);
//			if (status == PLAYEROP_STATUS_END && m_OperateTicks + 6 < m_OperateTotalTicks)
//			{
//				if (m_OperateTicks + 10 < m_OperateTotalTicks) m_SuspicionValue += 10 * 20;
//				else m_SuspicionValue += 5 * 20;
//			}
//			if (m_SuspicionValue > 25 * 20) target_checkok = false;
//		}
//
//		if (status == PLAYEROP_STATUS_END && (digmethod == DIG_METHOD_MULTI || digmethod == DIG_METHOD_CHARGE) && hasUIControl())
//			MINIW::ScriptVM::game()->callFunction("ShowScreenEffect", "i", 2);
//
//		if (status == PLAYEROP_STATUS_END && target_checkok)
//		{
//			getLivingAttrib()->removeBuff(INVULNERABLE_BUFF);
//
//			std::vector<CSPermitBitType> csPermitType;
//			csPermitType.push_back(CS_PERMIT_DESTROY_BLOCK);
//
//			if (!PermitsManager::GetInstance().canInteractorBlock(getUin(), getCurToolID(), targetblockid, csPermitType)) {
//				if (!m_pWorld->isRemoteMode()) {
//					if (!PermitsManager::GetInstance().canInteractorActor(getUin())) {
//						notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 411);
//					}
//					else { //?????????
//						notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 451);
//					}
//				}
//			}
//			else
//			{
//				bool gamerule_forbid = !checkActionAttrState(ENABLE_DESTROYBLOCK);
//				//bool gamerule_forbid = g_WorldMgr->isGameMakerRunMode() && g_WorldMgr->m_RuleMgr->getRuleOptionVal(GMRULE_BLOCKDESTROY) == 0;
//
//				int vibrate = 40;
//				int maxeffects = 5;
//				BackPackGrid *grid = getPlayerAttrib()->getEquipGrid(EQUIP_WEAPON);
//
//				if (!g_WorldMgr->isGodMode() && grid->getDuration() > 0)
//					grid->addDuration(1000000); //???????????????
//
//				std::vector<WCoord>retblocks;
//				const ToolDef *tooldef = GetDefManagerProxy()->getToolDef(getCurToolID());
//				if (digmethod == DIG_METHOD_MULTI)
//				{
//					int nblock = GetMultiDigBlocks(m_pWorld, targetblock, targetblockid, GetDefManagerProxy()->getToolDef(getCurToolID()), &retblocks);
//					for (size_t i = 0; i < retblocks.size(); i++)
//					{
//						destroyBlock(retblocks[i], digmethod, ((int)i < maxeffects), gamerule_forbid);
//					}
//					vibrate += nblock * 10;
//
//					if (tooldef && tooldef->SkillCD > 0)
//					{
//						setSkillCD(tooldef->ID, tooldef->SkillCD);
//						syncSkillCD(tooldef->ID, tooldef->SkillCD);
//					}
//				}
//				else if (digmethod == DIG_METHOD_CHARGE)
//				{
//					playDigAnim(DIG_METHOD_CHARGE);
//					if (tooldef && tooldef->Type == 2 && tooldef->Level == 5) playSound("item.11015.charge4", 1.0f, 1.0f);
//
//					getMineBlockTicks(getCurToolID(), targetblockid, m_pWorld->getBlockData(targetblock), &m_MineType);
//					int nblock = GetChargeDigBlocks(m_pWorld, targetblock, targetface, targetblockid, GetDefManagerProxy()->getToolDef(getCurToolID()), &retblocks);
//					for (size_t i = 0; i < retblocks.size(); i++)
//					{
//						destroyBlock(retblocks[i], digmethod, ((int)i < maxeffects), gamerule_forbid);
//					}
//					vibrate += nblock * 10;
//
//					if (tooldef && tooldef->SkillCD > 0)
//					{
//						setSkillCD(tooldef->ID, tooldef->SkillCD);
//						syncSkillCD(tooldef->ID, tooldef->SkillCD);
//					}
//				}
//				else
//				{
//					int playerdir = getCurPlaceDir();
//					if (tooldef && tooldef->Type == 3 && tooldef->Level == 4)
//					{
//						destroyBlock(targetblock, digmethod, true, gamerule_forbid);
//						for (int i = 1; i < 6; i++)
//						{
//							if (playerdir == DIR_POS_X)
//							{
//								int blockid = m_pWorld->getBlockID(targetblock + WCoord(-1 * i, 0, 0));
//								if ((m_pWorld->getBlockID(targetblock + WCoord(-1 * i, 1, 0)) == 0 || isPlant(m_pWorld->getBlockID(targetblock + WCoord(-1 * i, 1, 0))))
//									&& BlockByScoop(blockid))
//									destroyBlock(targetblock + WCoord(-1 * i, 0, 0), digmethod, true, gamerule_forbid);
//								else
//									break;
//							}
//							else if (playerdir == DIR_NEG_X)
//							{
//								int blockid = m_pWorld->getBlockID(targetblock + WCoord(1 * i, 0, 0));
//								if ((m_pWorld->getBlockID(targetblock + WCoord(1 * i, 1, 0)) == 0 || isPlant(m_pWorld->getBlockID(targetblock + WCoord(1 * i, 1, 0))))
//									&& BlockByScoop(blockid))
//									destroyBlock(targetblock + WCoord(1 * i, 0, 0), digmethod, true, gamerule_forbid);
//								else
//									break;
//							}
//							else if (playerdir == DIR_POS_Z)
//							{
//								int blockid = m_pWorld->getBlockID(targetblock + WCoord(0, 0, -1 * i));
//								if ((m_pWorld->getBlockID(targetblock + WCoord(0, 1, -1 * i)) == 0 || isPlant(m_pWorld->getBlockID(targetblock + WCoord(0, 1, -1 * i))))
//									&& BlockByScoop(blockid))
//									destroyBlock(targetblock + WCoord(0, 0, -1 * i), digmethod, true, gamerule_forbid);
//								else
//									break;
//							}
//							else if (playerdir == DIR_NEG_Z)
//							{
//								int blockid = m_pWorld->getBlockID(targetblock + WCoord(0, 0, 1 * i));
//								if ((m_pWorld->getBlockID(targetblock + WCoord(0, 1, 1 * i)) == 0 || isPlant(m_pWorld->getBlockID(targetblock + WCoord(0, 1, 1 * i))))
//									&& BlockByScoop(blockid))
//									destroyBlock(targetblock + WCoord(0, 0, 1 * i), digmethod, true, gamerule_forbid);
//								else
//									break;
//							}
//						}
//					}
//					else
//						destroyBlock(targetblock, digmethod, true, gamerule_forbid);
//				}
//				if (!g_WorldMgr->isGodMode() && grid->getDuration() > 0)
//					getPlayerAttrib()->onCurToolUsed(-1000000);
//
//				if (hasUIControl() && GetClientInfoProxy()->getGameData("vibrate") == 1)
//				{
//					if (vibrate > 100) vibrate = 100;
//					MINIW::GameVibrate(vibrate);
//				}
//				GetGameEventQue().postPlayerDigBlockEnd(targetblockid);
//			}
//		}
//
//		if (m_DigEffect)
//		{
//			m_DigEffect->setNeedClear();
//			m_DigEffect = NULL;
//		}
//		m_pWorld->getEffectMgr()->playBlockCrackEffect(targetblock, -1, getObjId());
//		onOperateEnded();
//	}
//
//	notifyPunchBlock2Tracking(targetblock, targetface, status, digmethod);
//	return true;
//}

bool ClientPlayer::farmOpenBlock(const WCoord& targetblock, DirectionType targetface, int status)
{
	SandboxResult recoverBlockRet = SandboxEventDispatcherManager::GetGlobalInstance().
		Emit("Homeland_farmOpenBlock", SandboxContext(nullptr).
			SetData_UserObject("blockPos", targetblock).
			SetData_Number("targetface", targetface).
			SetData_Number("status", status).
			SetData_Userdata("ClientPlayer", "clientplayer", this));
	if (recoverBlockRet.IsSuccessed())
	{
		return recoverBlockRet.GetData_Bool("farmOpen");
	}
	return false;
}
bool ClientPlayer::refreshBusinessmanHeadIcon()
{
	CollideAABB box;
	getCollideBox(box);
	std::vector<IClientActor*> actors;

	box.expand(BLOCK_SIZE * 8, BLOCK_SIZE / 2 * m_checkboxscale, BLOCK_SIZE * 8);
	m_pWorld->getActorsInBoxExclude(actors, box, this);
	float length = 640001.0f; //距离超过8格
	WCoord& playerPos = this->getPosition();
	int index = 0;
	for (size_t i = 0; i < actors.size(); i++)
	{
		auto actor = static_cast<ClientActor*>(actors[i]);
		if (!actor->needClear() && checkIsBusinessman(actor))
		{
			if (playerPos.squareDistanceTo(actor->getPosition()) < length)
			{
				length = playerPos.squareDistanceTo(actor->getPosition());

				curShowDialogId = actor->getObjId();
			}
		}
	}

	return false;
}

bool ClientPlayer::checkIsBusinessman(ClientActor* actor)
{
	if (!actor)
	{
		return false;
	}
	int actorId = actor->getDefID();
	return (actorId >= 3010 && actorId <= 3020) || (actorId >= 3210 && actorId <= 3211) ||
		(actorId >= 3222 && actorId <= 3223) || actorId == 3229 || actorId == 3022;
}
void ClientPlayer::resetBusinessIcon()
{
	if (curShowDialogId > 0)
	{
		auto actor = m_pWorld->getActorMgr()->ToCastMgr()->findActorByWID(curShowDialogId);
		if (actor)
		{
			actor->setHeadIconByPath("", "", 0, 0);
		}
	}
	curShowDialogId = 0;
}
//回收家园方块(目前回收未成熟植物和动物)
bool ClientPlayer::recoverBlock(const WCoord& targetblock, DirectionType targetface, int status)
{
	SandboxResult recoverBlockRet = SandboxEventDispatcherManager::GetGlobalInstance().
		Emit("Homeland_recoverBlock", SandboxContext(nullptr).
			SetData_UserObject("blockPos", targetblock).
			SetData_Number("targetface", targetface).
			SetData_Number("status", status).
			SetData_Userdata("ClientPlayer", "clientplayer", this));
	if (recoverBlockRet.IsSuccessed())
	{
		return recoverBlockRet.GetData_Bool("recover");
	}
	return false;
}

//bool ClientPlayer::exploitBlock(const WCoord & targetblock, DirectionType targetface, int status, int pickType)
//{
//
//	setIsExploiting(false);
//	setCurItemSkillID(0);
//
//	int curToolId = getCurToolID();
//	if (status == PLAYEROP_STATUS_BEGIN)
//	{
//		bool showProgress = true; //true????????? ???????; false ????????
//		int blockid = m_pWorld->getBlockID(targetblock);
//		if (IsHoelID(curToolId) || IsShovelID(curToolId))
//		{
//			if (pickType == 1 && checkCanExploit(curToolId, blockid))
//			{
//				if ((IsHoelID(curToolId) || IsShovelID(curToolId)) && (targetface == DIR_NEG_Y || !m_pWorld->isAirBlock(targetblock.x, targetblock.y + 1, targetblock.z)))
//				{
//					//?????checkCanExploit ?????blockid??????????ж???
//					showProgress = false;
//				}
//				else
//				{
//					showProgress = true;
//				}
//			}
//			else
//			{
//				showProgress = false;
//			}
//		}
//		else
//		{
//			if (blockid == 0)
//				return false;
//
//			showProgress = true;
//		}
//
//		if (showProgress)
//		{
//			setIsExploiting(true);
//			int mineticks;
//			if (GetClientInfoProxy()->IsCurrentUserOuterChecker())
//			{
//				mineticks = 0;
//			}
//			else
//			{
//				mineticks = 20;
//				const ToolDef *tooldef = GetDefManagerProxy()->getToolDef(getCurToolID());
//				if (tooldef)
//				{
//					mineticks = tooldef->Farmingspeed;
//				}
//			}
//
//			if (m_pWorld && !m_pWorld->isRemoteMode())
//			{
//				// ???????????
//				ObserverEvent_PlayerBlock obevent(getUin(), m_pWorld->getBlockID(targetblock), targetblock.x, targetblock.y, targetblock.z);
//				ObserverEventManager::getSingleton().OnTriggerEvent("Block.Dig.Begin", &obevent);
//			}
//
//			setOperate(PLAYEROP_DIG, mineticks, 0);
//			m_CurDigBlockID = blockid;
//			m_CurDigBlockPos = targetblock;
//
//			playDigAnim(DIG_METHOD_NORMAL);
//		}
//		else
//		{
//			playDigAnim(DIG_METHOD_NORMAL);
//		}
//	}
//	else
//	{
//		assert(status == PLAYEROP_STATUS_END || status == PLAYEROP_STATUS_CANCEL);
//		const ToolDef *tooldef = GetDefManagerProxy()->getToolDef(getCurToolID());
//		if (tooldef && tooldef->Type == 2 && tooldef->Level == 5)
//		{
//			auto soundComp = getSoundComponent();
//			if (soundComp)
//			{
//				soundComp->playSound("item.11015.charge2", 1.0f, 1.0f);
//			}
//		}
//
//		int targetblockid = m_pWorld->getBlockID(targetblock);
//		if (m_pWorld && !m_pWorld->isRemoteMode())
//		{
//			// ???????????
//			ObserverEvent_ActorBlock obevent(getObjId(), targetblockid, targetblock.x, targetblock.y, targetblock.z);
//			ObserverEventManager::getSingleton().OnTriggerEvent(status == PLAYEROP_STATUS_END ? "Block.Dig.End" : "Block.Dig.Cancel", &obevent);
//		}
//
//		if (status == PLAYEROP_STATUS_END)
//		{
//			getLivingAttrib()->removeBuff(INVULNERABLE_BUFF);
//
//			std::vector<CSPermitBitType> csPermitType;
//			csPermitType.push_back(CS_PERMIT_DESTROY_BLOCK);
//
//			SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_canInteractorBlock",
//				SandboxContext(nullptr)
//				.SetData_Number("uin", getUin())
//				.SetData_Number("tool", getCurToolID())
//				.SetData_Number("blockid", targetblockid)
//				.SetData_Usertype<std::vector<CSPermitBitType>>("csPermitType", &csPermitType));
//			bool canInteractorBlockFlag = false;
//			if (result.IsExecSuccessed())
//			{
//				canInteractorBlockFlag = result.GetData_Bool();
//			}
//			if (!canInteractorBlockFlag) {
//				if (!m_pWorld->isRemoteMode()) {
//
//					SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_canInteractorActor",
//						SandboxContext(nullptr).SetData_Number("uin", getUin()));
//					bool canInteractorActorFlag = false;
//					if (result.IsExecSuccessed())
//					{
//						canInteractorActorFlag = result.GetData_Bool();
//					}
//					if (!canInteractorActorFlag) {
//						notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 411);
//					}
//					else { //?????????
//						notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 451);
//					}
//				}
//			}
//			else
//			{
//				BackPackGrid *grid = getPlayerAttrib()->getEquipGrid(EQUIP_WEAPON);
//				if (!g_WorldMgr->isGodMode() && grid->getDuration() > 0)
//					grid->addDuration(1000000); //???????????????
//
//				const int toolId = getCurToolID();
//				bool scripthandled = false;
//				int setBlockAllRet = 0;
//				const ItemDef *def = GetDefManagerProxy()->getItemDef(toolId);
//				if (def && def->UseScript2[0] && def->UseScript2[0] != 0)
//				{
//					bool callScript = true;
//					if (toolId == ITEM_FLINTSTEEL)
//					{
//						if (targetblockid == BLOCK_FIREWORKFLOWER ||
//							targetblockid == BLOCK_CANDLEHOLDER ||
//							targetblockid == BLOCK_REDCANDLEHOLDER ||
//							targetblockid == BLOCK_KONGMINGHOLDER ||
//							targetblockid == BLOCK_TNT ||
//							targetblockid == BLOCK_BONFIRE ||
//							//20210825????仨??  codeby?? yaoxinqun
//							targetblockid == BLOCK_FESTIVE_LANTERN)
//						{
//							callScript = false;
//							//??仨????????????????????????2??????????????????????
//							BlockMaterial * block = this->getWorld()->getBlockMaterial(targetblock);
//							if (block)
//							{
//								block->EXEC_USEMODULE(OnTrigger, this->getWorld(), targetblock, DirectionType::DIR_POS_Y, this, Rainbow::Vector3f(0, 0, 0));
//							}
//
//							callScript = false;
//						}
//					}
//
//					if (callScript && m_pWorld && !m_pWorld->isRemoteMode())
//					{
//						MINIW::ScriptVM::game()->callFunction(def->UseScript2, "u[ClientPlayer]u[World]iiii>bi", this, getWorld(), targetblock.x, targetblock.y, targetblock.z, targetface, &scripthandled, &setBlockAllRet);
//					}
//				}
//
//				if (!g_WorldMgr->isGodMode() && grid->getDuration() > 0)
//					getPlayerAttrib()->onCurToolUsed(-1000000);
//			}
//		}
//
//		if (m_DigEffect)
//		{
//			m_DigEffect->setNeedClear();
//			m_DigEffect = NULL;
//		}
//
//		onOperateEnded();
//	}
//
//	notifyExploitBlock2Tracking(targetblock, targetface, status, pickType);
//	return true;
//}

//block ??? end

// model,skin Avarta ??? begin
bool ClientPlayer::usePackingFCMItem(int itemid, WCoord usepos)
{
	if (!m_pWorld)
		return false;

	if (!m_pWorld->isRemoteMode())
	{
		playAnim(SEQ_ATTACK);

		auto* itemDef = GetDefManagerProxy()->getItemDef(itemid);
		if (itemDef && PackingFullyCustomModelMgr::GetInstancePtr())
		{
			int result = PackingFullyCustomModelMgr::GetInstancePtr()->packingFcmItem_OnUse(this, getWorld(), itemDef->Model.c_str());

			if (hasUIControl())
			{
				PackingFullyCustomModelMgr::GetInstancePtr()->handlePackingFcmItemUseResult(result);
			}
			else if (GetGameNetManagerPtr())
			{
				PB_UsePackingFcmItemHC usePackingFcmItemHC;
				usePackingFcmItemHC.set_result(result);

				GetGameNetManagerPtr()->sendToClient(getUin(), PB_USE_PACKINGFCMITEM_HC, usePackingFcmItemHC);
			}

		}
	}

	return true;
}

static bool IsSameCustomSkin(const char* p1, const char* p2)
{
	if (p1 == NULL || p2 == NULL) return p1 == p2;
	else return strcmp(p1, p2) == 0;
}

void ClientPlayer::ChangeNameObjHeight() {
	const RoleSkinDef* skinDef = GetDefManagerProxy()->getRoleSkinDef(getBody()->getSkinID());
	//??????????????, todo ???????  ???????????
	if (skinDef && getBody()->getMutateMob() == 0 && IsSameCustomSkin(getBody()->getCustomSkins(), "")
		&& (skinDef->ID == 52 || skinDef->ID == 53 || skinDef->ID == 58 || skinDef->ID == 64 || skinDef->ID == 74 || skinDef->ID == 90 || skinDef->ID == 96))
	{
		getBody()->m_NameObjHeight = 60;
	}
	else
	{
		getBody()->m_NameObjHeight = 16;
	}
}

void ClientPlayer::GMChangeSkin(int nType, int nSkinId, const char* file)
{
	//nType:1??? 2??? 3avator
	if (!getBody())
		return;

	getBody()->onLeaveWorld();
	OGRE_DELETE(m_Body);

	m_Body = ENG_NEW(ActorBody)(this);
	getBody()->GMChangeSkin(nType, nSkinId, file);

	if (m_pWorld)
		getBody()->onEnterWorld(m_pWorld);

}

void ClientPlayer::changePlayerModel(int playerindex, int mutatemob, const char* customskin, const char* custommodel/* = NULL*/, int itemid, int blockid, bool force)
{
	if (getBody() == nullptr) return;

	if (getBody()->getPlayerIndex() == playerindex && getBody()->getMutateMob() == mutatemob && IsSameCustomSkin(getBody()->getCustomSkins(), customskin) && NULL == custommodel && !force)
	{
		if (GAMERECORD_INTERFACE_EXEC(isRecordStarted(), false))
		{
			MINIW::ScriptVM::game()->callFunction("ClientGetRoleAvatarInfo", "is", getUin(), m_strCustomjson.c_str());
		}
		return;
	}
	getBody()->stopAct();
	int oldNowPlaySeqID = -1;
	if (getBody()->getActID() < 0)
	{
		oldNowPlaySeqID = getBody()->getNowPlaySeqID();
	}
	int oldCurAnimLayer0 = 0;
	int oldCurAnimLayer1 = 0;
	int oldCurAnimWeapon = 0;
	if (m_Body->getCurAnim(0) > 0)
		oldCurAnimLayer0 = m_Body->getCurAnim(0);
	if (m_Body->getCurAnim(1) > 0)
		oldCurAnimLayer1 = m_Body->getCurAnim(1);
	if (m_Body->getCurAnimWeapon() > 0)
		oldCurAnimWeapon = m_Body->getCurAnimWeapon();

	int oldSkinId = m_Body->getSkinID();

	//特效
	//std::string oldMotionName = getBody()->getCurMotionName();
	if (getGunLogical())
	{
		getGunLogical()->unregisterThirdPersonEvent();
	}
	getBody()->onLeaveWorld();

	if (playerindex <= 0)
		playerindex = 1;

	OGRE_DELETE(m_Body);

	Rainbow::UILib::ModelView* bakview = NULL;
	int attachUIFrom = FUIMODELSPRITE_ATTACH_FROM_NULL;
	int bakindex = 0;
	if (GetActorBodySafeHandle()->IsVaild(m_UIViewBody))
	{
#if MODELVIEW_DECOUPLE_FROM_ACTORBODY
		bakview = getAttachedModelView();
		bakindex = getAttachedModelViewIndex();
		attachUIFrom = GetAttchUIFrom();
		detachUIModelView(bakindex);

#else
		bakview = m_UIViewBody->getAttachedModelView();
		bakindex = m_UIViewBody->getAttachedModelViewIndex();
#endif
		//delete m_UIViewBody;
		OGRE_DELETE(m_UIViewBody);
	}
	std::stringstream str;
	m_Body = ENG_NEW(ActorBody)(this);
	const MonsterDef* def = GetDefManagerProxy()->getMonsterDef(mutatemob);
	if (def && def->ModelType == MONSTER_CUSTOM_MODEL)
	{
		getBody()->initCustomActor(HUMAN_MODEL, def->Model.c_str());
		getBody()->setPlayerIndex(playerindex);
		getBody()->setMutateMob(mutatemob);
		str << "custom_" << def->Model.c_str();
	}
	else if (def && def->ModelType == MONSTER_FULLY_CUSTOM_MODEL)
	{
		getBody()->initFullyCustomActor(MAP_MODEL_CLASS, NULL, def->Model.c_str());
		getBody()->setPlayerIndex(playerindex);
		getBody()->setMutateMob(mutatemob);
		str << "fullycustom_" << def->Model.c_str();
	}
	else if (def && def->ModelType == MONSTER_IMPORT_MODEL)
	{
		getBody()->initOffcialModel(def->Model.c_str());
		getBody()->setPlayerIndex(playerindex);
		getBody()->setMutateMob(mutatemob);
		str << "importmodel_" << def->Model.c_str();
	}
	else if (custommodel && strlen(custommodel) > 0)
	{
		setCustomModelForActorBody(playerindex, mutatemob, customskin, custommodel);
		getBody()->setPlayerIndex(playerindex);
		getBody()->setMutateMob(mutatemob);
		str << custommodel;
	}
	else if (def && mutatemob > 0)//人物变成生物
	{
		getBody()->initPlayer(playerindex, mutatemob, customskin);
		if (def->TextureID > 0)
		{
			char texname[256];
			sprintf(texname, "entity/%s/male%d.png", def->Model.c_str(), def->TextureID);
			if (GetFileManager().IsFileExistWritePath(texname))
			{
				getBody()->setCustomDiffuseTexture(texname);
			}
		}

		// 部分特殊生物模型显示不同的模型部件
		if (def->ID >= 3873 && def->ID <= 3877)
		{
			//祭司不同法杖
			char saddleName[128];
			for (int i = 0; i < 5; ++i)
			{
				sprintf(saddleName, "ball0%d", i + 1);
				getBody()->showSkin(saddleName, (def->ID - 3873) == i);
			}
			getBody()->setNeedUpdateSkin(true);
		}
		if (def->ID == 3411 || def->ID == 3412 || def->ID == 3413 || def->ID == 3421 || def->ID == 3414 || def->ID == 3881 || def->ID == 3401) // δ???????
		{
			showNecklace(0);
			showSaddle(0);
			getBody()->setNeedUpdateSkin(true);
		}
		else if (def->ID == 3410 || def->ID == 3405 || def->ID == 3406 || def->ID == 3422 || def->ID == 3415 || def->ID == 3882 || def->ID == 3891)  // ѱ������
		{
			showNecklace(0);
			showSaddle(1);
			getBody()->setNeedUpdateSkin(true);
		}
		initCusMotion();	//自定义动作处理
		str << "mob_" << mutatemob;
	}
	else if (itemid > 0)//人物变成道具模型
	{
		int realid = ClientItem::getRealModelItemId(itemid);
		ItemDef* def2 = GetDefManagerProxy()->getItemDef(realid);
		if (def2)
		{
			if (GetWorldManagerPtr())
			{
				SandboxEventDispatcherManager::GetGlobalInstance().Emit("ChangeModelMgr_changeItemModelActor", SandboxContext(nullptr)
					.SetData_Number("itemid", itemid)
					.SetData_Usertype("body", getBody()));
			}
			getBody()->setPlayerIndex(playerindex);

			if (def2->MeshType == 1)
			{
				char texname[256];
				if (def2->TextureID == 0)
					sprintf(texname, "itemmods/%s/texture.png", def2->Model.c_str());
				else
					sprintf(texname, "itemmods/%s/texture%d.png", def2->Model.c_str(), def2->TextureID);

				getBody()->setCustomDiffuseTexture(texname);
			}
			str << "item_" << itemid;
		}
		else
		{
			getBody()->initPlayer(playerindex, mutatemob, customskin);
			initCusMotion();	//自定义动作处理

			if (getBody()->getSkinID() > 0)
			{
				str << "skin_" << getBody()->getSkinID();
			}
			else if (getBody()->getModelID() > 0)
			{
				str << "role_" << getBody()->getModelID();
			}
		}
	}
	else if (blockid > 0)
	{
		BlockDef* def2 = GetDefManagerProxy()->getBlockDef(blockid);
		if (def2)
		{
			if (GetWorldManagerPtr())
			{
				SandboxEventDispatcherManager::GetGlobalInstance().Emit("ChangeModelMgr_changeBlockModelActor", SandboxContext(nullptr)
					.SetData_Number("blockid", blockid)
					.SetData_Usertype("body", getBody()));
			}
			getBody()->setPlayerIndex(playerindex);
			str << "block_" << getBody()->getSkinID();
		}
		else
		{
			getBody()->initPlayer(playerindex, mutatemob, customskin);
			initCusMotion();	//自定义动作处理

			if (getBody()->getSkinID() > 0)
			{
				str << "skin_" << getBody()->getSkinID();
			}
			else if (getBody()->getModelID() > 0)
			{
				str << "role_" << getBody()->getModelID();
			}
		}
	}
	else
	{
		getBody()->initPlayer(playerindex, mutatemob, customskin, 0, false, true);
		initCusMotion();	//自定义动作处理

		if (customskin && strlen(customskin) > 0)
		{
			str << "playerskin_" << getUin();
		}
		else if (getBody()->getSkinID() > 0)
		{
			str << "skin_" << getBody()->getSkinID();
		}
		else if (getBody()->getModelID() > 0)
		{
			str << "role_" << getBody()->getModelID();
		}
		applyDisplayName();
	}

	auto functionWrapper = getFuncWrapper();
	if (functionWrapper)
	{
		functionWrapper->setActorFacade(str.str());
	}

	ChangeNameObjHeight();


	if (getOPWay() == PLAYEROP_WAY_FOOTBALLER || getOPWay() == PLAYEROP_WAY_BASKETBALLER)
	{
		//getBody()->setNeedUpdateBallEffect(true);
		updateBallEffect();
	}
	getBody()->setNeedUpdateAnim(hasUIControl());
	if (oldNowPlaySeqID >= 0)
		getBody()->playAnimCheck(oldNowPlaySeqID);

	if (oldCurAnimLayer0 > 0) m_Body->setCurAnim(oldCurAnimLayer0, 0);
	if (oldCurAnimLayer1 > 0) m_Body->setCurAnim(oldCurAnimLayer1, 1);
	if (oldCurAnimWeapon > 0) m_Body->setCurAnimWeapon(oldCurAnimWeapon, 0);

	//if (oldMotionName != "")
	//	getBody()->playMotion(oldMotionName.c_str());

	if (m_pWorld) getBody()->onEnterWorld(m_pWorld);
	if (getGunLogical())
	{
		getGunLogical()->registerThirdPersonEvent();
	}
	auto thornComponent = getThornBallComponent();
	if (thornComponent != nullptr)
	{
		//换装在生成
		thornComponent->createThornBall();
	}
	if (customskin)
		m_strCustomjson = customskin;

	if (m_UIViewBody)
	{
		WarningStringMsg("m_UIViewBody no free");
	}
	m_UIViewBody = ENG_NEW(ActorBody)(this);
	//LOG_WARNING("ClientPlayer #### : %p , this: %p", m_UIViewBody, this);	

	if (def && def->ModelType == MONSTER_CUSTOM_MODEL)
	{
		m_UIViewBody->initCustomActor(HUMAN_MODEL, def->Model.c_str());
		m_UIViewBody->setPlayerIndex(playerindex);
		m_UIViewBody->setMutateMob(mutatemob);
	}
	else if (def && def->ModelType == MONSTER_FULLY_CUSTOM_MODEL)
	{
		m_UIViewBody->initFullyCustomActor(MAP_MODEL_CLASS, NULL, def->Model.c_str());
		m_UIViewBody->setPlayerIndex(playerindex);
		m_UIViewBody->setMutateMob(mutatemob);
	}
	else if (itemid > 0)//人物变成道具模型
	{
		ItemDef* def = GetDefManagerProxy()->getItemDef(itemid);
		if (def)
		{
			if (GetWorldManagerPtr())
			{
				SandboxEventDispatcherManager::GetGlobalInstance().Emit("ChangeModelMgr_changeItemModelActor", SandboxContext(nullptr)
					.SetData_Number("itemid", itemid)
					.SetData_Usertype("body", m_UIViewBody));
			}
			if (def->MeshType == 1)
			{
				char texname[256];
				if (def->TextureID == 0)
					sprintf(texname, "itemmods/%s/texture.png", def->Model.c_str());
				else
					sprintf(texname, "itemmods/%s/texture%d.png", def->Model.c_str(), def->TextureID);

				m_UIViewBody->setCustomDiffuseTexture(texname);
			}
		}
		else
		{
			m_UIViewBody->initPlayer(playerindex, mutatemob, customskin);
		}
	}
	else if (blockid > 0)
	{
		BlockDef* def2 = GetDefManagerProxy()->getBlockDef(blockid);
		if (def2)
		{
			if (GetWorldManagerPtr())
			{
				SandboxEventDispatcherManager::GetGlobalInstance().Emit("ChangeModelMgr_changeBlockModelActor", SandboxContext(nullptr)
					.SetData_Number("blockid", blockid)
					.SetData_Usertype("body", m_UIViewBody));
			}
			getBody()->setPlayerIndex(playerindex);
		}
		else
		{
			getBody()->initPlayer(playerindex, mutatemob, customskin);
		}
	}
	else if (custommodel && strlen(custommodel) > 0)
	{
		setCustomModelForActorBody(playerindex, mutatemob, customskin, custommodel, m_UIViewBody);
		m_UIViewBody->setPlayerIndex(playerindex);
		m_UIViewBody->setMutateMob(mutatemob);
	}
	else
		m_UIViewBody->initPlayer(playerindex, mutatemob, customskin);

	GET_SUB_SYSTEM(UIActorBodyMgr)->AddActorBodySpecialComp(m_UIViewBody);

	if (bakview || (attachUIFrom != FUIMODELSPRITE_ATTACH_FROM_NULL))
		attachUIModelView(bakview, bakindex, attachUIFrom);

	if (mutatemob == 0 && customskin && customskin[0] && !GAMERECORD_INTERFACE_EXEC(isRecordPlaying(), false))
	{
		int skin_id = 0;
		SandboxCoreDriver::GetInstance().GetLua().CallFunctionM("RoleSkin_Helper", "GetUseAvtarBodyModel", "is>i", getUin(), customskin, &skin_id);
		if (skin_id == 0)
		{
			m_UIViewBody->setBodyType(3);
			m_UIViewBody->addDefaultAvatar();

			getBody()->setBodyType(3);
			getBody()->addDefaultAvatar();
		}

		MINIW::ScriptVM::game()->callFunction("ClientGetRoleAvatarInfo", "is", getUin(), customskin);
	}

	if (def)  ///玩家使用生物模型 背包预览的模型缩放 贴图处理
	{
		const char* pModeName = def->Model.c_str();
		if (def->gamemod && pModeName && pModeName[0] == 'a')
		{
			jsonxx::Object* pobj = nullptr;
			def->gamemod->Event2().Emit<jsonxx::Object*&, int>("Mod_GetAvatarJsonObjById", pobj, mutatemob);
			//jsonxx::Object* pobj = def->gamemod->GetAvatarJsonObjById(mutatemob);
			if (pobj)
			{
				//解析avatar数据
				getBody()->ParseAvatarInfo(*pobj);
				//背包展示模型
				m_UIViewBody->ParseAvatarInfo(*pobj);
			}
		}
		else
		{
			if (def->TextureID > 0)
			{
				char texname[256];
				sprintf(texname, "entity/%s/male%d.png", def->Model.c_str(), def->TextureID);
				if (GetFileManager().IsFileExistWritePath(texname))
				{
					m_UIViewBody->setCustomDiffuseTexture(texname);
				}
			}
			if (def->PreScale > 0.01f)
			{
				m_UIViewBody->setScale(def->PreScale);
				m_UIViewBody->setRealScale(def->PreScale);
			}
		}
	}
	if (g_pPlayerCtrl && (g_pPlayerCtrl != this))
	{
		setNickName((char*)m_Nickname.c_str());
		m_Body->setVisibleDispayName(true);
		setHPProgressDirty();
	}


	for (int i = 0; i < MAX_EQUIP_SLOTS; ++i)
	{
		applyEquips((EQUIP_SLOT_TYPE)i);
	}

	//装备效果重新生效
	getLivingAttrib()->execSpecialEffect(BUFFATTRT_BODY_ALL);
	getLivingAttrib()->execSpecialEffect(BUFFATTRT_BODY_HEAD);
	getLivingAttrib()->execSpecialEffect(BUFFATTRT_BUBBLE);

	PlayerControl* player = dynamic_cast<PlayerControl*>(this);
	if (player) {
		player->setPlayerCamera(playerindex, mutatemob, customskin);
	}

	MINIW::ScriptVM::game()->callFunction("ReqCurUseAchieveByUin", "i", getUin());
	//2021-12-20 codeby: wangyang ��Աͼ��
	MINIW::ScriptVM::game()->callFunction("OnPlayerEnter", "i", getUin());

	//2021-12-20 codeby: wangyang 会员图标
	char buffer[64];
	sprintf(buffer, "%d", getUin());
	MINIW::ScriptVM::game()->callFunction("OnPlayerEnter", "s", buffer);

	if (m_pWorld && !m_pWorld->isRemoteMode())
	{
		//auto riding = getRidingActor();
		if (isShapeShift() && getBody() && getBody()->getSkinID() != oldSkinId)
		{
			mountActor(NULL);
		}
		
		const RoleSkinDef* skinDef = GetDefManagerProxy()->getRoleSkinDef(getBody()->getSkinID());
		if (skinDef)
		{
			char name[128];
			snprintf(name, sizeof(name), "&N&A&M&E$1%s", skinDef->Name);
			tickNewChat(name);
		}
	}

	if (hasUIControl())
	{
		MINIW::ScriptVM::game()->callFunction("ChangePlayerCallBack", "");
	}
}

//被调函数:ClientPlayer::changePlayerModel().
void ClientPlayer::setCustomModelForActorBody(int playerindex, int mutatemob, const char* customskin, const char* custommodel/*=""*/, ActorBody* body/*=nullptr*/)
{
	if (custommodel)
	{
		ActorBody* actorbody = body == nullptr ? getBody() : body;
		if (nullptr == actorbody)
		{
			return;
		}
		std::string strCustomModel = custommodel;
		int pos = strCustomModel.find('_');
		std::string strModelID;

		if (pos > 0 && pos < (int)strCustomModel.size())
		{
			strModelID = strCustomModel.substr(pos + 1);

			if (strModelID.size() > 0)
			{
				if (custommodel[0] == 'f')
				{
					//fullycustommodel
					actorbody->initFullyCustomActor(MAP_MODEL_CLASS, NULL, strModelID);
				}
				else if (custommodel[0] == 'c')
				{
					CustomActorModelData* data = CustomModelMgr::GetInstancePtr()->findCustomActorModelData(MAP_MODEL_CLASS, custommodel);
					if (!data)
						data = CustomModelMgr::GetInstancePtr()->findCustomActorModelData(RES_MODEL_CLASS, custommodel);
					if (!data)
					{
						actorbody->initCustomModel(strModelID);
					}
					else
					{
						actorbody->initCustomActor(HUMAN_MODEL, strModelID);
					}
				}
				else if (custommodel[0] == 'i')
				{
					actorbody->initOffcialModel(strModelID);
				}
				else if (custommodel[0] == 'a')
				{
					//Avatarģ��
					//'a':���⴦��
					actorbody->initPlayer(playerindex, 0, "a");

					jsonxx::Object tmpObject;
					std::string  str = std::string(custommodel);
					std::string  sub = str.substr(str.find('_') + 1);
					std::string data = "";
					if (g_WorldMgr)
					{
						SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("ChangeModelMgr_getAvatorByid",
							SandboxContext(nullptr).SetData_String("sub", sub));
						if (result.IsSuccessed())
						{
							data = result.GetData_String();
						}
					}

					if (!data.empty())
					{
						tmpObject.parse(data);
					}
					else
					{
						if (g_WorldMgr && g_WorldMgr->isRemote()) //�ͻ����ӱ���ȡ����
						{
							int teamId = getTeam();
							IGameMode* setterMgr = g_WorldMgr->getBaseSettingManager();
							if (setterMgr != NULL)
							{
								const char* modeldata = setterMgr->getPlayerBaseModelData(teamId);
								if (modeldata != NULL) tmpObject.parse(modeldata);
							}
						}
						else
						{
							const int cfgLen = 10240;
							char skinCfg[cfgLen] = ""; //������ȳ���Len��Χ���ؿ����ݷ�ֹ����
							MINIW::ScriptVM::game()->callFunction("PlayerModelGetAvatorSkinCfg", "si>s", strModelID.c_str(), cfgLen, skinCfg);
							assert(skinCfg[0] != '\0');
							tmpObject.parse(skinCfg);
						}
					}
					actorbody->ParseAvatarInfo(tmpObject);
				}
			}
		}
	}
}
std::string ClientPlayer::getFullyCustomModelKey()
{
	if (getBody())
	{
		const MonsterDef* def = GetDefManagerProxy()->getMonsterDef(getBody()->getMutateMob());
		if (def && def->ModelType == MONSTER_FULLY_CUSTOM_MODEL)
		{
			return def->Model.c_str();
		}
	}

	return "";
}

void ClientPlayer::updateBodyByFullyCustomModel()
{
	if (getBody())
	{
		std::string strCustomModel = getCustomModel();
		const MonsterDef* def = GetDefManagerProxy()->getMonsterDef(getBody()->getMutateMob());
		int playerindex = getBody()->getPlayerIndex();

		if (def && def->ModelType == MONSTER_FULLY_CUSTOM_MODEL)
		{
			getBody()->initFullyCustomActor(MAP_MODEL_CLASS, NULL, def->Model.c_str());
			getBody()->setPlayerIndex(playerindex);
		}
		else if (strCustomModel.size() > 0)
		{
			changeBaseModel(strCustomModel, getCustomModelScale());
		}
	}
}

void ClientPlayer::updateBodyByImportModel()
{
	if (!getBody())
		return;

	std::string strCustomModel = getCustomModel();
	const MonsterDef* def = GetDefManagerProxy()->getMonsterDef(getBody()->getMutateMob());
	int playerindex = getBody()->getPlayerIndex();

	if (def && def->ModelType == MONSTER_IMPORT_MODEL)
	{
		getBody()->initOffcialModel(def->Model.c_str());
		getBody()->setPlayerIndex(playerindex);
	}
	else if (strCustomModel.size() > 0)
	{
		changeBaseModel(strCustomModel, getCustomModelScale());
	}
}

void ClientPlayer::refreshAvarta()
{
	if (m_strCustomjson.size() && GAMERECORD_INTERFACE_EXEC(isRecordStarted(), false))
	{
		//MINIW::ScriptVM::game()->callFunction("ClientGetRoleAvatarInfo", "is", getUin(), m_strCustomjson.c_str());
		if (getBody())
			changePlayerModel(getBody()->getPlayerIndex(), 0, m_strCustomjson.c_str());
	}
}

void ClientPlayer::setCustomModel(std::string custommodel)
{
	m_strCustomModel = custommodel;
}

std::string ClientPlayer::getCustomModel()
{
	return m_strCustomModel;
}
void ClientPlayer::setCustomJson(string& s) {
	m_strCustomjson = s;
	if (g_WorldMgr)
		g_WorldMgr->signChangedToSync(getUin(), BIS_CUSTOM_JSON);
}
void ClientPlayer::setNickName(char* nickname)
{
	GetDefManagerProxy()->filterStringDirect(nickname);
	ActorLiving::setNickName(nickname);
	m_Nickname = std::string(nickname);
}

void ClientPlayer::setBaseModel(int nTeamId)
{
	bool bEnableBaseModel = false;

	if (GetWorldManagerPtr() && GetWorldManagerPtr()->isGameMakerRunMode() && GetWorldManagerPtr()->getBaseSettingManager())
	{
		bool bState = GetWorldManagerPtr()->getBaseSettingManager()->getCustomFuncState(CUSTOM_MODEL_ENABLE, nTeamId);

		//δ???????, ?????????
		if (nTeamId >= 0 && !bState)
		{
			nTeamId = -1;
			bState = GetWorldManagerPtr()->getBaseSettingManager()->getCustomFuncState(CUSTOM_MODEL_ENABLE, nTeamId);
		}

		if (bState)
		{
			bEnableBaseModel = true;
			std::string strModelId = GetWorldManagerPtr()->getBaseSettingManager()->getPlayerBaseModelID(nTeamId);
			float fScale = GetWorldManagerPtr()->getBaseSettingManager()->getPlayerBaseModelScale(nTeamId);

			//?л????
			if (strModelId.size() > 0)
			{
				setCustomModel(strModelId);
				setCustomModelScale(fScale);

				bool changemodel = MNSandbox::IMiniDeveloperProxy::getMiniDeveloperProxy()->HasChangeModel(getUin());
				if (!changemodel)
				{
					MNSandbox::IMiniDeveloperProxy::getMiniDeveloperProxy()->ChangeModel(this, strModelId);
				}
			}
		}
	}
}

/**
 * @brief 获取当前使用的modelid(使用角色), 角色天赋等级, 皮肤ID
 *
 * @param model 返回值, 当前使用角色
 * @param genius_lv 返回值, 当前角色的天赋等级
 * @param skin_id 返回值, 当前使用的皮肤ID
 * @return true 获取成功
 * @return false 获取失败
 */
bool ClientPlayer::getBodyInfoInUse(int* model, int* genius_lv, int* skin_id) {
	if (m_Body) {
		if (model)
			*model = m_Body->getModelID();
		if (genius_lv)
			*genius_lv = m_Body->getGeniusLv();
		if (skin_id)
			*skin_id = m_Body->getSkinID();
		return true;
	}

	if (hasUIControl()) {
		auto& roleInfo = GetClientInfoProxy()->getAccountInfo()->RoleInfo;
		int model_ = roleInfo.Model;
		if (model)
			*model = model_;
		if (genius_lv)
			*genius_lv = GetClientInfoProxy()->getGenuisLv(model_);
		if (skin_id)
			*skin_id = m_AccoutSkinID;
		return true;
	}
	return false;
}

void ClientPlayer::changeBaseModel(std::string& strModelId, float fScale)
{
	//if (ClientAccountMgr::GetInstancePtr() == NULL) return;
	const ROLEINFO& roleinfo = GetClientInfoProxy()->getAccountInfo()->RoleInfo;
	int rolemodel = roleinfo.Model;
	int geniuslv = GetClientInfoProxy()->getGenuisLv(rolemodel);
	int playerindex = ComposePlayerIndex(rolemodel, geniuslv, roleinfo.SkinID);

	if (rolemodel == 0)
		rolemodel = 1;

	if (geniuslv < 0)
		geniuslv = 0;

	std::string sType, sID;
	parsePlayerBaseModelID(strModelId, sType, sID);

	if (sType == "mob")
	{
		int nMobID = atoi(sID.c_str());
		changePlayerModel(playerindex, nMobID);
		getBody()->setMutateMob(0);//???????????, ?????????????????(???????????)
	}
	else if (sType == "role")
	{
		int nRoleID = atoi(sID.c_str());
		int playerindex = ComposePlayerIndex(nRoleID, geniuslv, 0);
		//changePlayerModel(nRoleID);
		changePlayerModel(playerindex);
	}
	else if (sType == "skin")
	{
		int nSkinID = atoi(sID.c_str());
		int playerindex = ComposePlayerIndex(rolemodel, geniuslv, nSkinID);
		//int playerindex = ComposePlayerIndex(0, 0, nSkinID);
		changePlayerModel(playerindex);
	}
	else if (sType == "avator")
	{
		int nSkinID = 0;
		int playerindex = ComposePlayerIndex(rolemodel, geniuslv, nSkinID);
		changePlayerModel(playerindex, 0, "", strModelId.c_str());
	}
	else if (sType == "custom")
	{
		int skinID = 0;
		int playerindex = ComposePlayerIndex(rolemodel, geniuslv, skinID);
		changePlayerModel(playerindex, 0, "", strModelId.c_str());
	}
	else if (sType == "fullycustom")
	{
		int skinID = 0;
		int playerindex = ComposePlayerIndex(rolemodel, geniuslv, skinID);
		changePlayerModel(playerindex, 0, "", strModelId.c_str());
	}
	else if (sType == "importmodel")
	{
		int skinID = 0;
		int playerindex = ComposePlayerIndex(rolemodel, geniuslv, skinID);
		changePlayerModel(playerindex, 0, "", strModelId.c_str());
	}
	setScale(fScale);
}

void ClientPlayer::parsePlayerBaseModelID(std::string& strWholeID, std::string& sType, std::string& sID)
{
	//modelId: "nType_ID", ??"mob_1255"??"custom_1554778514515454545"
	//nType: mob:??? role:??? skin:??? avator:??????? custom:??????? fullycustom:????????
	int pos = strWholeID.find('_');
	if (pos > 0 && pos < (int)strWholeID.size())
	{
		sType = strWholeID.substr(0, pos);
		sID = strWholeID.substr(pos + 1);
	}
}

int ClientPlayer::getSkinID()
{
	if (getBody())
		return getBody()->getSkinID();

	return -1;
}

// model,skin Avarta ??? end


void ClientPlayer::endCurOperate(long long vehID)
{
	if (m_CurOperate == PLAYEROP_ATTACK_BOW)
	{
		useItem(getCurToolID(), PLAYEROP_STATUS_END);
	}
	else if (m_CurOperate == PLAYEROP_DIG)
	{
		auto pState = getCurrentActionStatePtr();
		if (nullptr != pState)
		{
			if (vehID > 0)
			{
				auto actor = m_pWorld->getActorMgr()->ToCastMgr()->findActorByWID(vehID);
				if (actor)
				{
					auto pveh = dynamic_cast<ActorVehicleAssemble*>(actor);
					if (pveh && pveh->canDig())
					{
						auto pDigState = dynamic_cast<DigState*>(pState);
						if (nullptr != pDigState)
						{
							pDigState->digBlockWithVehicle(m_CurDigVehBlockPos, DIR_NEG_X, PLAYEROP_STATUS_CANCEL, pveh);
						}
						else
						{
							auto pRangDigState = dynamic_cast<RangeDigState*>(pState);
							if (nullptr != pRangDigState)
							{
								pRangDigState->digBlockWithVehicle(m_CurDigVehBlockPos, DIR_NEG_X, PLAYEROP_STATUS_CANCEL, pveh);
							}
						}

					}
				}
			}
			else {
				auto pDigState = dynamic_cast<DigState*>(pState);
				if (nullptr != pDigState)
				{
					pDigState->digBlock(m_CurDigBlockPos, DIR_NEG_X, PLAYEROP_STATUS_CANCEL);
				}
				else
				{
					auto pRangDigState = dynamic_cast<RangeDigState*>(pState);
					if (nullptr != pRangDigState)
					{
						pRangDigState->digBlock(m_CurDigBlockPos, DIR_NEG_X, PLAYEROP_STATUS_CANCEL);
					}
				}
			}
		}

	}
	else if (m_CurOperate == PLAYEROP_EATFOOD || m_CurOperate == PLAYEROP_DRINKWATER)
	{
		useItem(getCurToolID(), PLAYEROP_STATUS_CANCEL);
	}
	else
	{
		assert(m_CurOperate == PLAYEROP_NULL);
	}
}

void ClientPlayer::onOperateEnded()
{
	setOperate(PLAYEROP_NULL);
}

void ClientPlayer::playCurToolSound()
{
	int id = getCurToolID();
	const ToolDef* tooldef = GetDefManagerProxy()->getToolDef(id);
	if (tooldef != NULL)
	{
		auto soundComp = getSoundComponent();
		if (soundComp)
		{
			soundComp->playSound(tooldef->UseSound, 1.0f, 1.0f);
		}
	}
}

void ClientPlayer::tryMoveTo(int x, int y, int z, float speed, bool canControl, bool needSync /*= false*/, bool showtip /*= false*/)
{
	if (!getNavigator())
	{
		CreateNavPath();
	}

	bool  bshow = showtip;
	if (this->getObjType() != OBJ_TYPE_ROLE) { bshow = false; }  //???????????????
	if (showtip)
	{
		bshow = hasUIControl();  //??б??????????
	}

	if (getNavigator()->tryMoveTo(x, y, z, speed, -1, -1, bshow))
	{
		setCanControl(canControl);
	}

	if (needSync)
	{
		if (g_WorldMgr && !g_WorldMgr->isRemote())
		{
			// ??????
			//	1????PlayerControl?????????; 2????PlayerControl???player??????????????·
			if (!isPlayerControl())
			{
				PB_PlayerNavigateHC navigateHC;
				navigateHC.set_speed(speed);
				navigateHC.set_cancontrol(canControl);
				navigateHC.set_showtip(showtip);
				PB_Vector3* targetPos = navigateHC.mutable_targetpos();
				targetPos->set_x(x);
				targetPos->set_y(y);
				targetPos->set_z(z);
				GameNetManager::getInstance()->sendToClient(getUin(), PB_PLAYER_NAVIGATE_HC, navigateHC);
			}
		}
	}
}

void ClientPlayer::getFacedActors(std::vector<ClientActor*>& actors, const Rainbow::Vector3f& dir, int range, int width)
{
	CollideAABB box;
	getCollideBox(box);
	box.expand(range, 0, range);

	std::vector<IClientActor*>tmpactors;
	m_pWorld->getActorsInBox(tmpactors, box);

	WCoord origin = getPosition();

	for (size_t i = 0; i < tmpactors.size(); i++)
	{
		ClientActor* actor = static_cast<ClientActor*>(tmpactors[i]);
		ActorVehicleAssemble* vehicle = dynamic_cast<ActorVehicleAssemble*>(actor);
		if (vehicle)
		{
			actors.push_back(actor);
			continue;
		}
		Rainbow::Vector3f dp = (actor->getPosition() - origin).toVector3();

		float t = DotProduct(dp, dir);
		if (t > 0 && t < range)
		{
			Rainbow::Vector3f tmp = CrossProduct(dp, dir);
			if (tmp.LengthSqr() < width * width) actors.push_back(actor);
		}
	}
}

void ClientPlayer::playToolEffect(int stage, bool stoplast)
{
	if (stoplast)
		stopMotion(20);

	if (stage >= 0)
	{
		int curToolID = getCurToolID();
		auto* toolDef = GetDefManagerProxy()->getToolDef(getCurToolID());
		if (toolDef && toolDef->Type == 6 && toolDef->Level == 5)
			curToolID = 12005;


		char name[32];

		if (toolDef && (toolDef->ID == 12009 || toolDef->ID == 12010))
		{
			float Yew = 0;
			float Pitch = 0;
			if (this->m_CameraModel && this->m_CameraModel->m_GameCamera) {
				Yew = this->m_CameraModel->m_GameCamera->m_RotateYaw + 180.0f;
				Pitch = this->m_CameraModel->m_GameCamera->m_RotatePitch;
			}
			sprintf(name, "particles/item_%d_%d.ent", curToolID, stage);
			m_pWorld->getEffectMgr()->playParticleEffectAsync(name, getPosition(), 30, Yew, -Pitch, false);
		}
		else
		{
			sprintf(name, "item_%d_%d", curToolID, stage);
			playMotion(name, 20);
		}

		m_CurPlayEffectToolID = getCurToolID();
	}
	else
	{
		m_CurPlayEffectToolID = -1;
	}
}

void ClientPlayer::playToolSound(int stage, bool loop)
{
	if (loop)
	{
		OGRE_RELEASE(m_CurPlaySnd);
		m_CurPlaySndToolID = -1;
	}

	if (stage < 0) return;

	int curToolID = getCurToolID();
	auto* toolDef = GetDefManagerProxy()->getToolDef(getCurToolID());
	if (toolDef && toolDef->Type == 6 && toolDef->Level == 5)
		curToolID = 12005;

	char name[32];
	sprintf(name, "item.%d.charge%d", curToolID, stage);

	if (loop)
	{
		m_CurPlaySnd = m_pWorld->getEffectMgr()->playLoopSound(getPosition(), name, 1.0f, 1.0f);
		m_CurPlaySndToolID = getCurToolID();
	}
	else
	{
		auto soundComp = getSoundComponent();
		if (soundComp)
		{
			soundComp->playSound(name, 1.0f, 1.0f, 0);
		}
	}
}

void ClientPlayer::playBlockPlaceSound(int blockid, int x, int y, int z)
{
	if (m_pWorld)
	{
		m_pWorld->playBlockPlaceSound(blockid, x, y, z);
	}
	//const BlockDef* def = GetDefManagerProxy()->getBlockDef(blockid);
	//WCoord centerpos = BlockCenterCoord(WCoord(x, y, z));

	//const char *placesound = def->PlaceSound.c_str();
	//if (placesound[0] == 0) placesound = def->DigSound.c_str();
	//if (placesound[0] == 0) placesound = "blockd.grass";

	//m_pWorld->getEffectMgr()->playSound(centerpos, placesound, GSOUND_PLACE);
}

void ClientPlayer::playBlockDigSound(int blockid, int x, int y, int z)
{
	if (m_pWorld)
	{
		m_pWorld->playBlockDigSound(blockid, x, y, z);
	}
	//const BlockDef* def = GetDefManagerProxy()->getBlockDef(blockid);
	//WCoord centerpos = BlockCenterCoord(WCoord(x, y, z));

	//const char *digsound = def->DigSound.c_str();
	//if (digsound[0] == 0) digsound = "blockd.grass";

	//m_pWorld->getEffectMgr()->playSound(centerpos, digsound, GSOUND_DESTROY);
}

//死亡复活时, 如果不掉落装备, 则恢复装备的效果
void ClientPlayer::reviveEquipEffect(int reviveType/*= 0*/)
{
	if (reviveType == 1)
	{
		for (int i = 0; i < EQUIP_WEAPON; ++i)
		{
			int itemid = getPlayerAttrib()->getEquipItem((EQUIP_SLOT_TYPE)i);
			if (itemid > 0)
			{
				getLivingAttrib()->addEquipBuff(itemid);
				//类似陨魔假面这类道具把Effect放到ToolStatus
				getLivingAttrib()->addToolBuff(itemid);
			}
		}
	}
}

bool ClientPlayer::playAct(int act, bool isSwitchViewMode)
{
	if (!getBody())
		return false;

	auto RidComp = getRiddenComponent();
	if (RidComp && RidComp->getRidingActor())
	{
		return false;
	}

	onPlayActEvent(act);
	getBody()->stopAct();
	if (getBody()->playAct(act))
	{
		// ???????
		if (getWorld() && !getWorld()->isRemoteMode())
		{
			playActionOnTrigger(act);
		}
	}
	return true;
}

void ClientPlayer::stopAct()
{
	if (!getBody())
		return;

	auto RidComp = getRiddenComponent();
	if (RidComp && RidComp->getRidingActor())
	{
		return;
	}

	getBody()->stopAct();
}

void ClientPlayer::onPlayActEvent(int act)
{
	if (!m_pWorld || m_pWorld->isRemoteMode())
		return;

	if (GetWorldManagerPtr() && m_pWorld)
	{
		std::vector<IClientActor*> nearMobs;
		WCoord pos = getPosition();

		//???????????鷴??
		m_pWorld->findNearActors(nearMobs, pos, 1, OBJ_TYPE_FLYMONSTER);
		for (size_t i = 0; i < nearMobs.size(); i++)
		{
			ClientMob* mob = dynamic_cast<ClientMob*>(nearMobs[i]);
			if (mob && mob->getDefID() == MONSTER_WARBLER_TAMED && mob->getTamedOwnerID() == getUin())
			{
				MINIW::ScriptVM::game()->callFunction("F3884_ExpressionFeedback", "u[ClientMob]", mob);
				//break;
			}
		}
		nearMobs.clear();
		if (act == 1) {
			std::vector<ClientActor*> nearMobs2;
			m_clicktimes++;
			if (m_helloTick >= 0 && m_clicktimes == 2) {
				m_canFllowByVillager = true;
			}
			else {
				m_helloTick = 100;
				m_clicktimes = 1;
				m_canFllowByVillager = false;
			}

			/*m_pWorld->findNearActors(nearMobs, pos, 1, OBJ_TYPE_DESERTVILLAGER);
			for (size_t i = 0; i < nearMobs.size(); i++) {
				int disY = Abs((pos.y - nearMobs[i]->getPosition().y) / BLOCK_SIZE);
				if (disY < 6) {
					int disX = (pos.x - nearMobs[i]->getPosition().x) / BLOCK_SIZE;
					int disZ = (pos.z - nearMobs[i]->getPosition().z) / BLOCK_SIZE;
					int dis = sqrt(disX * disX + disZ * disZ);
					if (dis < 6) {
						ActorDesertVillager* mob = dynamic_cast<ActorDesertVillager*>(nearMobs[i]);
						ObserverEvent obevent;
						obevent.SetData_EventObj(mob->getObjId());
						obevent.SetData_ToObj(getObjId());
						obevent.SetData_Act(act);
						ObserverEventManager::getSingleton().OnTriggerEvent("Actor.BeGreetedBy", &obevent);
						if (!mob->getGossipState() && m_canFllowByVillager) {
							nearMobs2.push_back(mob);
						}
					}
				}
			}*/
			auto mgt = getWorld()->getActorMgr()->GetActorMGT();
			if (mgt)
			{
				Rainbow::AABB range(pos.toWorldPos().toVector3(), 6 * BLOCK_SIZE);
				mgt->QueryNodes(range, [&nearMobs2, act, this](IClientActor* actor) {
					if (actor->needClear()) return;
				if (OBJ_TYPE_DESERTVILLAGER == actor->getObjType())
				{
					ActorDesertVillager* mob = static_cast<ActorDesertVillager*>(actor);
					ObserverEvent obevent;
					obevent.SetData_EventObj(mob->getObjId());
					obevent.SetData_ToObj(this->getObjId());
					obevent.SetData_Act(act);
					ObserverEventManager::getSingleton().OnTriggerEvent("Actor.BeGreetedBy", &obevent);
					if (!mob->getGossipState() && m_canFllowByVillager) {
						nearMobs2.push_back(mob);
					}
				}
				});
			}

			if (nearMobs2.size() > 0) {
				int j = rand() % nearMobs2.size();
				ActorDesertVillager* mob2 = dynamic_cast<ActorDesertVillager*>(nearMobs2[j]);
				mob2->setHelloFollowState(true);
			}
			nearMobs2.clear();
			nearMobs.clear();
		}
		m_pWorld->findNearActors(nearMobs, pos, 1, OBJ_TYPE_VILLAGER);
		std::map<int, VillageInfo> out;
		GetWorldManagerPtr()->getWorldInfoManager()->getAllVillageInfo(out);
		for (size_t i = 0; i < nearMobs.size(); i++)
		{
			auto mob = nearMobs[i];
			if (!mob)
				continue;
			std::map<int, VillageInfo>::iterator iter = out.begin();
			for (; iter != out.end(); ++iter)
			{
				auto widSet = iter->second.bindVillagers;
				if (widSet.find((WORLD_ID)mob->getObjId()) != widSet.end())
				{
					// ??????????? - A??B???????
					ObserverEvent obevent;
					obevent.SetData_EventObj(mob->getObjId());
					obevent.SetData_ToObj(getObjId());
					obevent.SetData_Act(act);
					GetObserverEventManager().OnTriggerEvent("Actor.BeGreetedBy", &obevent);
				}
			}
		}
	}
}


//----- ?????,backpack,  begin
/*
	20210824:????????????????? codeby??wangyu
*/
int ClientPlayer::exchangeItems(int costitemid, int costnum, int gainitemid, int gainnum)
{
	//?????????
	if (!getBackPack() || getBackPack()->getItemCountInNormalPack(costitemid) < costnum)
	{
		return 0;
	}
	getBackPack()->removeItemInNormalPack(costitemid, costnum);
	this->gainItems(gainitemid, gainnum, 1);
	//???????
	return 1;
}

void ClientPlayer::addCurToolDuration(int num)
{
	BackPackGrid* pgrid = getBackPack()->index2Grid(getCurShortcut() + getShortcutStartIndex());
	if (pgrid->def)
	{
		const ToolDef* tooldef = GetDefManagerProxy()->getToolDef(pgrid->def->ID);
		if (tooldef && tooldef->Duration > 0)
		{
			getPlayerAttrib()->onCurToolUsed(num);
		}
	}
}

void ClientPlayer::DoCurToolAtkDuration()
{
	BackPackGrid* pgrid = getBackPack()->index2Grid(getCurShortcut() + getShortcutStartIndex());
	if (pgrid->def)
	{
		const ToolDef* tooldef = GetDefManagerProxy()->getToolDef(pgrid->def->ID);
		if (tooldef && tooldef->Duration > 0 && tooldef->AtkDuration > 0)
		{
			getPlayerAttrib()->onCurToolUsed(-tooldef->AtkDuration);
		}
	}
}

void ClientPlayer::addCurDorsumDuration(int num)
{
	getLivingAttrib()->damageEquipItemWithType(EQUIP_PIFENG, num);
}

int  ClientPlayer::getEquipItemDuration(EQUIP_SLOT_TYPE slot)
{
	PlayerAttrib* playerAttrib = dynamic_cast<PlayerAttrib*>(getLivingAttrib());
	return playerAttrib->getEquipItemDuration(slot);
}

void ClientPlayer::onSetCurShortcutWithFB(const FBSave::ActorPlayer* wrole)
{
	int nCurShotcut = wrole->handindex();
	int nCurShotcutEdit = wrole->handindexEdit();
	if (nCurShotcut < 0)
		nCurShotcut = 0;
	if (nCurShotcutEdit < 0)
		nCurShotcutEdit = 0;

	getPlayerAttrib()->m_CurShotcut = nCurShotcut;
	getPlayerAttrib()->m_CurShotcutEdit = nCurShotcutEdit;
	onSetCurShortcut(getCurShortcut());
}

void ClientPlayer::CopyShortcutForUGC(BackPack* backpack)
{
	PackContainer* packFrom = dynamic_cast<PackContainer*>(backpack->getContainer(SHORTCUT_START_INDEX));
	PackContainer* packTo = dynamic_cast<PackContainer*>(backpack->getContainer(SHORTCUT_START_INDEX_EDIT));
	if (!packFrom || !packTo)
	{
		return;
	}

	int fromCount = packFrom->getGridCount();
	int toCount = packTo->getGridCount();
	int minCount = fromCount < toCount ? fromCount : toCount;

	for (int i = 0; i < minCount; i++)
	{
		BackPackGrid* gridFrom = packFrom->index2Grid(SHORTCUT_START_INDEX + i);
		BackPackGrid* gridTo = packTo->index2Grid(SHORTCUT_START_INDEX_EDIT + i);
		if (gridFrom && gridTo)
		{
			gridTo->setItem(*gridFrom, gridFrom->getNum());
		}
	}
}

void ClientPlayer::onSetCurShortcut(int i)
{
	if (m_pItemComponent) m_pItemComponent->DoCurShotGunModEntry(false);
	getPlayerAttrib()->setCurShotcut(i);
	if (m_pItemComponent) m_pItemComponent->onSetCurShortcut(i);
	applyEquipsOnShortcut(EQUIP_WEAPON, false);
	getPlayerAttrib()->ApplyWeaponEnchantEffect(getBody());

	if (m_UIViewBody && GetActorBodySafeHandle()->IsVaild(m_UIViewBody))
		getPlayerAttrib()->ApplyWeaponEnchantEffect(m_UIViewBody);

	MINIW::ScriptVM::game()->callFunction("CurItemStatistics", "ii", getCurToolID(), getUin());
}

void ClientPlayer::onPickupItem(ClientActor* item)
{
	m_pWorld->getEffectMgr()->GetGameEffectMgr()->playPickItemEffect(this, item, 0);


	ClientItem* pItem = dynamic_cast<ClientItem*>(item);
	if (pItem) {
		WCoord itemPos = CoordDivBlock(pItem->getPosition());
		std::ostringstream oss;
		oss << "[" << itemPos.x << "," << itemPos.y << "," << itemPos.z << "]";
		std::string location = oss.str();	

		GameAnalytics::TrackEvent("item_pickup", {
			{"item_id",pItem->getDefID()},
			{"item_count",pItem->getItemNum()},
			{"uin",getUin()},
			{"location",location}
		});
	}

	auto soundComp = getSoundComponent();
	if (soundComp)
	{
		float pitch = ((GenRandomFloat() - GenRandomFloat()) * 0.7f + 1.0f) * 2.0f;
		soundComp->playSound("misc.pop", 0.2f, pitch);
	}
}

int ClientPlayer::getCurShortcut()
{
	if (getPlayerAttrib() != NULL) {
		return getPlayerAttrib()->getCurShotcut();
	}
	return -1;
}

int ClientPlayer::getCurShortcutItemNum()
{
	int shortcutIndex = getCurShortcut() + getShortcutStartIndex();
	int shortcutNum = getBackPack()->getGridNum(shortcutIndex);
	return shortcutNum;
}

//????????????
int ClientPlayer::getShortcutStartIndex()
{
	if (getBackPack())
	{
		return getBackPack()->getShortcutStartIndex();
	}

	return SHORTCUT_START_INDEX;
}

void ClientPlayer::shortcutItemAllUsed(int maxUsedNum)
{
	int shortcutNum = getCurShortcutItemNum();
	if (shortcutNum > maxUsedNum)
	{
		shortcutNum = maxUsedNum;
	}
	for (int i = 0; i < shortcutNum; i++)
	{
		shortcutItemUsed(true);
	}
}

void ClientPlayer::shortcutItemUsed(bool ignoreDurable)
{
	if (GetWorldManagerPtr() && GetWorldManagerPtr()->getSpecialType() == HOME_GARDEN_WORLD)
	{
		if (getAttrib() == NULL) return;
		int itemid = getPlayerAttrib()->getEquipItem(EQUIP_WEAPON);
		if (itemid > 0)
		{
			bool bFindHomeItemDef = false;
#ifdef MODULE_FUNCTION_ENABLE_HOMELAND
			bFindHomeItemDef = GetDefManagerProxy()->findHomeItemDefFunctionId(itemid, HOMELAND_FUNC_UNLIMIT_USE);
#endif
			if (!bFindHomeItemDef && itemid != ITEM_BLUEPRINT) //??????????? ???????? 
			{
				addAchievement(3, ACHIEVEMENT_USEITEM, itemid, 1);
				updateTaskSysProcess(TASKSYS_USE_ITEM, itemid);
				auto itemDef = GetDefManagerProxy()->getItemDef(itemid);
				if (itemDef)
					addOWScore(itemDef->Score);

				LOG_INFO("shortcutItemUsed uin:%d id:%d, ", getUin(), itemid);

				if (ignoreDurable)
					getPlayerAttrib()->onCurToolUsed(0, true);
				else
					getPlayerAttrib()->onCurToolUsed();
			}
			//?????? ???????????
			MINIW::ScriptVM::game()->callFunction("HomeLandToolUsed", "ii", itemid, 1);
		}
	}
	else
	{
		if (m_pWorld == NULL || getAttrib() == NULL)  return;
		if (!(GetWorldManagerPtr() && GetWorldManagerPtr()->isGodMode()))
		{
			int itemid = getPlayerAttrib()->getEquipItem(EQUIP_WEAPON);
			if (itemid > 0)
			{
				if (itemid == ItemIDs::BLUEPRINT)
					return;//建筑图纸不消耗
				addAchievement(3, ACHIEVEMENT_USEITEM, itemid, 1);
				updateTaskSysProcess(TASKSYS_USE_ITEM, itemid);
				auto itemDef = GetDefManagerProxy()->getItemDef(itemid);
				if (itemDef)
					addOWScore(itemDef->Score);
				if (ignoreDurable)
					getPlayerAttrib()->onCurToolUsed(0, true);
				else
					getPlayerAttrib()->onCurToolUsed();
			}
		}
	}
}

int ClientPlayer::removeItemInNormalPack(int itemid, int num)
{
	return getBackPack()->removeItemInNormalPack(itemid, num);
}

IBackPack* ClientPlayer::getIBackPack()
{
	return getBackPack();
}

BackPack* ClientPlayer::getBackPack()
{
	if (getPlayerAttrib() != NULL) {
		return getPlayerAttrib()->m_Backpack;
	}
	return NULL;
}

CraftingQueue* ClientPlayer::getCraftingQueue()
{
	if (getPlayerAttrib() != NULL) {
		return getPlayerAttrib()->m_CraftingQueue;
	}
	return NULL;
}

void ClientPlayer::setAccumulatorState(float progress)
{
	//主机同步到相应客机上面去
	if ((GetClientInfoProxy()->getMultiPlayer() != 0) && m_pWorld && !m_pWorld->isRemoteMode())
	{
		jsonxx::Object context;
		context << "uin" << getUin();
		context << "progress" << progress;
		SandBoxManager::getSingleton().sendToClient(getUin(), "PB_Accumulator_HC", context.bin(), context.binLen());
	}
}

void ClientPlayer::sortPack(int base_index)
{
	if (getBackPack()) getBackPack()->sortPack(base_index);
}

void ClientPlayer::sortStorageBox()
{
	if (getBackPack()) getBackPack()->sortStorageBox();
}

void ClientPlayer::randomSelect(std::vector<const EnchantDef*>& enchantSet, int num)
{
	std::vector<const EnchantDef*> candiSet;
	candiSet.swap(enchantSet);

	if (candiSet.size() <= 0) return;

	int* probsLower = new int[candiSet.size()];
	int* probsHigher = new int[candiSet.size()];

	int candiCnt = 0;

	while (candiSet.size() > 0 && num > 0)
	{
		memset(probsLower, 0, sizeof(int) * candiSet.size());
		memset(probsHigher, 0, sizeof(int) * candiSet.size());
		candiCnt = 0;
		int total = 0;
		// ???????????
		for (int i = 0, _n = candiSet.size(); i < _n; ++i)
		{
			probsLower[candiCnt] = total + 1;
			total += candiSet[i]->Weight;
			probsHigher[candiCnt] = total;
			candiCnt++;
		}
		int luckyNum = m_pWorld->genRandomInt(1, total);
		int selected = 0;
		for (int i = 0; i < candiCnt; ++i)
		{
			if (luckyNum >= probsLower[i] && luckyNum <= probsHigher[i])
			{
				selected = i;
				break;
			}
		}
		enchantSet.push_back(candiSet[selected]);
		--num;

		int conflitId = candiSet[selected]->ConflictID;
		candiSet.erase(std::remove_if(candiSet.begin(), candiSet.end(),
			[conflitId](const EnchantDef* def)->bool
		{
			return (def->ConflictID > 0 && def->ConflictID == conflitId);
		}), candiSet.end());
	}

	OGRE_DELETE_ARRAY(probsLower);
	OGRE_DELETE_ARRAY(probsHigher);
}

std::map<int, int> ClientPlayer::getRepairCost(BackPackGrid* grid)
{
	std::map<int, int> costitems;
	if (grid == nullptr) return costitems;
	int itemid = grid->getItemID();
	if (itemid <= 0) return costitems;

	char repairstr[1024] = "";
	ScriptVM::game()->callFunction("GetToolRepairCostStr", "iii>s", itemid, grid->getDuration(), grid->getMaxDuration(), repairstr);

	if (repairstr[0] != '\0')
	{
		std::vector<std::string> group;
		StringUtil::split(group, repairstr, ",");
		for (std::string& itemcost : group)
		{
			std::vector<std::string> kv;
			StringUtil::split(kv, itemcost, ":");
			if (kv.size() == 2)
			{
				costitems[atoi(kv[0].c_str())] = atoi(kv[1].c_str());
			}
		}
	}
	return costitems;
}

int ClientPlayer::repair(int tgtGridIdx)
{
	auto backpack = getBackPack();
	if (backpack == nullptr) return -1;
	PackContainer* backpackContainer = (PackContainer*)backpack->getContainer(BACKPACK_START_INDEX);
	PackContainer* shortcutContainer = (PackContainer*)backpack->getContainer(getShortcutStartIndex());

	if (backpackContainer == nullptr || shortcutContainer == nullptr) return -1;

	auto grid = backpack->index2Grid(tgtGridIdx);
	if (nullptr == grid) return -1;


	if (grid->def == nullptr || grid->def->ID == 0) return -1;

	if (grid->getDuration() >= grid->getMaxDuration()) return -1;

	auto costitems = getRepairCost(grid);

	if (!canRepair(tgtGridIdx, costitems)) return -1;

	int toolId = backpack->getGridToolType(tgtGridIdx);
	if (toolId < 0) return -1; //not tool

	auto t_id = backpack->getGridItem(tgtGridIdx);

	auto def = GetDefManagerProxy()->getToolDef(t_id);
	if (def == nullptr) return -1;

	for (auto& pair : costitems)
	{
		backpack->removeItemInNormalPack(pair.first, pair.second);
	}

	backpack->mendItem(tgtGridIdx, 0);
	updateTaskSysProcess(TASKSYS_REPAIR_ITEM, t_id);
	return tgtGridIdx;
}

bool ClientPlayer::canRepair(int tgtGridIdx, const std::map<int, int>& costitems)
{
	auto backpack = getBackPack();
	if (backpack == nullptr) return false;

	int toolId = backpack->getGridToolType(tgtGridIdx);
	if (toolId < 0) return false; 

	for (auto& pair : costitems)
	{
		int havecount = backpack->getItemCountInNormalPack(pair.first);
		if (havecount < pair.second)
			return false;
	}
	return true;
}

int ClientPlayer::calcRepairCost(int tgtGridIdx, int materialId, int useNum)
{
	auto backpack = getBackPack();
	if (backpack == nullptr) return -1;

	int toolId = backpack->getGridToolType(tgtGridIdx);
	if (toolId < 0) return -1; //not tool

	auto t_id = backpack->getGridItem(tgtGridIdx);

	auto def = GetDefManagerProxy()->getToolDef(t_id);
	if (def == nullptr) return -1;

	int materialRite = -1;
	for (int i = 0; i < 6; ++i)
	{
		if (def->RepairId[i] == materialId)
		{
			materialRite = i;
			break;
		}
	}
	if (materialRite < 0 || def->RepairAmount[materialRite] <= 0) return -1;

	int canAmount = def->RepairAmount[materialRite] * useNum;
	int needAmount = backpack->getGridMaxDuration(tgtGridIdx) - backpack->getGridDuration(tgtGridIdx);
	int mendDur = canAmount < needAmount ? canAmount : needAmount;

	return (mendDur + 99) / 100;
}

int ClientPlayer::NewRepair(int tgtGridIdx, int repairDur, int mat1Id, int mat2Id, int repairCount, int starCount)
{
	if (m_pWorld->isRemoteMode())
	{
		PB_NewRepairItemCH newRepairItemCH;
		newRepairItemCH.set_tgtgrididx(tgtGridIdx);
		newRepairItemCH.set_repairdur(repairDur);
		newRepairItemCH.set_mat1id(mat1Id);
		newRepairItemCH.set_mat2id(mat2Id);
		newRepairItemCH.set_repaircount(repairCount);
		newRepairItemCH.set_starcount(starCount);

		GetGameNetManagerPtr()->sendToHost(PB_NEW_REPAIR_ITEM_CH, newRepairItemCH);
		return 0;
	}
	auto backpack = getBackPack();
	if (backpack == nullptr) return false;

	PackContainer* backpackContainer = (PackContainer*)backpack->getContainer(BACKPACK_START_INDEX);
	PackContainer* shortcutContainer = (PackContainer*)backpack->getContainer(getShortcutStartIndex());
	if (backpackContainer == nullptr || shortcutContainer == nullptr) return false;

	auto grid = backpack->index2Grid(tgtGridIdx);
	if (nullptr == grid) return -1;

	if (grid->def == nullptr || grid->def->ID == 0) return -1;
	if (grid->getDuration() >= grid->getMaxDuration()) return -1;

	int toolId = backpack->getGridToolType(tgtGridIdx);
	if (toolId < 0) return -1; //not tool
	auto t_id = backpack->getGridItem(tgtGridIdx);
	auto def = GetDefManagerProxy()->getToolDef(t_id);

	auto playerAttr = getAttrib() == nullptr ? nullptr : dynamic_cast<PlayerAttrib*>(getAttrib());
	if (playerAttr == nullptr) return -1;
	if (starCount > 0) //Use star to repair tool
	{
		int curExp = playerAttr->getExp();
		if (curExp / EXP_STAR_RATIO < starCount) return -1;

		playerAttr->addExp(-starCount * EXP_STAR_RATIO);
		backpack->mendItem(tgtGridIdx, repairDur);
		updateTaskSysProcess(TASKSYS_REPAIR_ITEM, t_id);
		return tgtGridIdx;
	}
	auto RemoveRepairItem = [&backpackContainer, &tgtGridIdx, &backpack, &shortcutContainer](int needCount, int matId) {
		for (size_t i = 0, _n = backpackContainer->m_Grids.size(); i < _n; ++i)
		{
			if (needCount <= 0) break;
			BackPackGrid& bpg = backpackContainer->m_Grids[i];
			if (bpg.getIndex() == tgtGridIdx) continue;
			if (bpg.def && bpg.def->ID == matId)
			{
				if (bpg.getNum() >= needCount)
				{
					backpack->removeItem(bpg.getIndex(), needCount);
					needCount = 0;
				}
				else
				{
					needCount -= bpg.getNum();
					backpack->removeItem(bpg.getIndex(), bpg.getNum());
				}
			}
		}
		for (size_t i = 0, _n = shortcutContainer->m_Grids.size(); i < _n; ++i)
		{
			if (needCount <= 0) break;
			BackPackGrid& bpg = shortcutContainer->m_Grids[i];
			if (bpg.getIndex() == tgtGridIdx) continue;
			if (bpg.def && bpg.def->ID == matId)
			{
				if (bpg.getNum() >= needCount)
				{
					backpack->removeItem(bpg.getIndex(), needCount);
					needCount = 0;
				}
				else
				{
					needCount -= bpg.getNum();
					backpack->removeItem(bpg.getIndex(), bpg.getNum());
				}
			}
		}
	};
	RemoveRepairItem(repairCount, mat1Id);
	RemoveRepairItem(repairCount, mat2Id);
	backpack->mendItem(tgtGridIdx, repairDur);
	updateTaskSysProcess(TASKSYS_REPAIR_ITEM, t_id);
	return tgtGridIdx;
}

void ClientPlayer::research()
{
	if (isRemote())
	{
		PB_ResearchCH ch;
		GetGameNetManagerPtr()->sendToHost(PB_Research_CH, ch);
		return;
	}

	int research_index = STORAGE_START_INDEX;
	int demand_index = STORAGE_START_INDEX + 1;

	auto backpack = getBackPack();
	if (backpack == nullptr) return;
	PackContainer* backpackContainer = (PackContainer*)backpack->getContainer(BACKPACK_START_INDEX);
	PackContainer* shortcutContainer = (PackContainer*)backpack->getContainer(getShortcutStartIndex());

	if (backpackContainer == nullptr || shortcutContainer == nullptr) return;

	BackPackGrid* research_grid = backpack->index2Grid(research_index);
	BackPackGrid* demand_grid = backpack->index2Grid(demand_index);
	if (nullptr == research_grid || demand_grid == nullptr)
	{
		LOG_WARNING("not grid");
		return;
	}

	int research_itemid = research_grid->getItemID();
	WorkbenchTechCsvDef* TechCsvDef = GetDefManagerProxy()->findItembenchTech(research_itemid);
	if (!TechCsvDef)
	{
		LOG_WARNING("not TechCsvDef");
		return;
	}

	int demand_itemid = demand_grid->getItemID();
	if (demand_itemid != TechCsvDef->_Cost.CostItem)
	{
		LOG_WARNING("Cost.CostItem");
		return;
	}

	if (demand_grid->getNum() < TechCsvDef->_Cost.CostValue)
	{
		LOG_WARNING("demand_grid not num");
		return;
	}

	//消耗
	backpack->removeItem(demand_index, TechCsvDef->_Cost.CostValue); //消耗晶石
	backpack->removeItem(research_index, research_grid->getNum());   //消耗研究物(一般只有1个)
	//生成
	std::string itemid_str = std::to_string(TechCsvDef->ItemId);
	backpack->setItemWithoutLimit(2020119, research_index,1, itemid_str.c_str());
}

//丢弃道具
void ClientPlayer::discardItem(int index, int num)
{
	if (GetWorldManagerPtr() && GetWorldManagerPtr()->getSpecialType() == HOME_GARDEN_WORLD)
	{
		//家园地图不让丢东西
		getBackPack()->removeItem(index, num);
		return;
	}

	if (GetWorldManagerPtr() && GetWorldManagerPtr()->m_RuleMgr)
	{
		//'可丢弃道具'开关
		if (!checkActionAttrState(ENABLE_DISCARDITEM))
			return;
	}

	int toolType = getBackPack()->getGridToolType(index);
	if (toolType == 31) //新鱼竿不可丢
	{
		notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 81150);
		getBackPack()->afterChangeGrid(index);
		return;
	}
	// 信纸清除审核文本
	removeLetter(index);
	getBackPack()->discardItem(index, num);


}

void ClientPlayer::createPolaroidPhotoItem(string userdata)
{
	BackPack* pBackpack = getBackPack();
	if (pBackpack == nullptr)
		return;
	const ItemDef* pDef = GetDefManagerProxy()->getItemDef(ITEM_POLAROID_PHOTO);
	if (pDef == nullptr)
		return;
	int emptyShortcutIndex = pBackpack->getEmptyShortcutIndex();
	//先判断快捷栏有没有位置，然后判断背包有没有位置，都没有就产生掉落物
	if (emptyShortcutIndex == -1)
		emptyShortcutIndex = pBackpack->getEmptyBagIndex();
	if (emptyShortcutIndex == -1)
	{
		downloadPolaroidPhoto(userdata);
	}
	else if(!m_pWorld->isRemoteMode())
	{
		BackPackGrid* photoBackGrid = pBackpack->index2Grid(emptyShortcutIndex);
		SetBackPackGrid(*photoBackGrid, ITEM_POLAROID_PHOTO, 1);
		photoBackGrid->userdata_str = userdata;
		pBackpack->afterChangeGrid(emptyShortcutIndex);
	}
}

void ClientPlayer::createPolaroidPhotoDropItem(std::string userdata)
{
	if (!m_pWorld)
		return;

	if (userdata.size() == 0)
		return;
	jsonxx::Object infoObj;
	if (!infoObj.parse(userdata))
		return;
	//有这个objid一定是客机
	if (infoObj.has<jsonxx::Number>("objid"))
	{
		long long objId = infoObj.get<jsonxx::Number>("objid");
		ClientActor *pActor = m_pWorld->getActorMgr()->ToCastMgr()->findActorByWID(objId);
		if (!pActor)
			return;
		ClientItem* pItem = dynamic_cast<ClientItem*>(pActor);
		if (!pItem)
			return;
		pItem->updateRenderObjs();
	}
	else
	{
		BackPackGrid photoBackGrid;
		SetBackPackGrid(photoBackGrid, ITEM_POLAROID_PHOTO, 1);
		photoBackGrid.userdata_str = userdata;
		ClientItem* pClientItem = m_pWorld->getActorMgr()->ToCastMgr()->spawnItem(getPosition(), photoBackGrid, 0);
		pClientItem->SetItemSpawnType(ANYWAY);
		pClientItem->m_LiveTicks = 150;
	}
}

void ClientPlayer::downloadPolaroidPhoto(std::string userdata)
{
	if (userdata.empty())
		return;
	jsonxx::Object infoObj;
	if (!infoObj.parse(userdata))
		return;
	string path("");
	if (infoObj.has<jsonxx::Number>("objid"))
	{
		if (!infoObj.has<jsonxx::String>("userdata"))
			return;
		std::string originalUserdata = infoObj.get<jsonxx::String>("userdata");
		jsonxx::Object userdataObj;
		if (!userdataObj.parse(originalUserdata))
			return;
		if (!userdataObj.has<jsonxx::String>("pngpath"))
			return;
		path = userdataObj.get<jsonxx::String>("pngpath");
	}
	else
	{
		if (!infoObj.has<jsonxx::String>("pngpath"))
			return;
		path = infoObj.get<jsonxx::String>("pngpath");
	}
	MNSandbox::GetCoreLuaDirector().CallFunctionM("UGCMapResourceInterface", "DownLoadPolaroidPhoto", "sis", path.c_str(), 1, userdata.c_str());
}

void ClientPlayer::removeLetter(int index)
{
	if (getBackPack()->getGridItem(index) == 11806 && g_WorldMgr && g_WorldMgr->isGodMode())
	{
		auto backpack = getBackPack();
		auto grid = backpack->index2Grid(index);
		std::string skey = "";
		if (!grid->userdata_str.empty())
		{
			jsonxx::Object oldTxtObj;
			if (oldTxtObj.parse(grid->userdata_str))
			{
				if (oldTxtObj.has<jsonxx::String>("key"))
				{
					skey = oldTxtObj.get<jsonxx::String>("key");
					SandboxEventDispatcherManager::GetGlobalInstance().Emit("WorldStringManager_remove", SandboxContext(nullptr)
						.SetData_String("strKey", skey)
						.SetData_Number("type", 8  /*SAVEFILETYPE::LETTER*/));
				}
			}
		}
	}
}

void ClientPlayer::removeWorldStringByGridIndex(int index, int destId, int type)
{
	// 只在创造模式清空，玩法模式丢弃不会消失可以捡回来
	if (getBackPack()->getGridItem(index) == destId && g_WorldMgr && g_WorldMgr->isGodMode())
	{
		auto backpack = getBackPack();
		auto grid = backpack->index2Grid(index);
		std::string skey = "";
		if (!grid->userdata_str.empty())
		{
			jsonxx::Object oldTxtObj;
			if (oldTxtObj.parse(grid->userdata_str))
			{
				if (oldTxtObj.has<jsonxx::String>("key"))
				{
					skey = oldTxtObj.get<jsonxx::String>("key");
					SandboxEventDispatcherManager::GetGlobalInstance().Emit("WorldStringManager_remove", SandboxContext(nullptr)
						.SetData_String("strKey", skey)
						.SetData_Number("type", type));
				}

				// 书本还要顺便清空包含的信纸
				if (destId == ITEM_BOOK && oldTxtObj.has<jsonxx::Array>("letters"))
				{
					jsonxx::Array& letters = oldTxtObj.get<jsonxx::Array>("letters");
					size_t size = letters.size();
					for (size_t i = 0; i < size; ++i)
					{
						if (letters.has<jsonxx::Object>(i))
						{
							jsonxx::Object& object = letters.get<jsonxx::Object>(i);
							if (object.has<jsonxx::String>("key"))
							{
								skey = object.get<jsonxx::String>("key");
								SandboxEventDispatcherManager::GetGlobalInstance().Emit("WorldStringManager_remove", SandboxContext(nullptr)
									.SetData_String("strKey", skey)
									.SetData_Number("type", SAVEFILETYPE::LETTER));
							}
						}
					}
				}
			}
		}
	}
}

void ClientPlayer::removeWorldStringByKey(const std::string& key, int type)
{
	if (!key.empty() && g_WorldMgr && g_WorldMgr->isGodMode())
	{
		SandboxEventDispatcherManager::GetGlobalInstance().Emit("WorldStringManager_remove", SandboxContext(nullptr)
			.SetData_String("strKey", key)
			.SetData_Number("type", type));
	}
}

void ClientPlayer::SetPickItemLastIndex(int index)
{
	m_PickItemLastIndex = index;
}

int ClientPlayer::GetPickItemLastIndex()
{
	return m_PickItemLastIndex;
}
//----- 快捷栏,backpack, end

//--------- bed ,chair 相关 begin

int ClientPlayer::sleepInVehicleBed(const WCoord& blockpos, World* pworld)
{
	VehicleWorld* vworld = dynamic_cast<VehicleWorld*>(pworld);
	if (vworld)
	{
		ActorVehicleAssemble* pAssemble = vworld->getActorVehicleAssemble();
		return sleepInVehicleBed(blockpos, pAssemble);
	}
	else
		return -1;
}

int ClientPlayer::sleepInVehicleBed(const WCoord& blockpos, ActorVehicleAssemble* assemble)
{
	if (g_pPlayerCtrl == this)
	{
		toActionState("ToSleep");
	}
	auto pSleepState = dynamic_cast<SleepState*>(getLocoCurActionStatePtr("Sleep"));
	if (pSleepState)
	{
		return pSleepState->sleepInVehicleBed(blockpos, assemble);
	}
	return STRDEF_SLEEP_STATUS;
}

int ClientPlayer::sleep(const WCoord& blockpos, bool refreshRevivePoint)
{
	if (g_pPlayerCtrl == this)
	{
		toActionState("ToSleep");
	}
	auto pSleepState = dynamic_cast<SleepState*>(getLocoCurActionStatePtr("Sleep"));
	if (pSleepState)
	{
		return pSleepState->sleep(blockpos, refreshRevivePoint);
	}
	return STRDEF_SLEEP_STATUS;
}

int ClientPlayer::sleepInBed(const WCoord& blockpos, bool refreshRevivePoint)
{
	return sleep(blockpos, refreshRevivePoint);
}

void ClientPlayer::playerTrySleep()
{
	auto pSleepState = dynamic_cast<SleepState*>(getLocoCurActionStatePtr("Sleep"));
	if (pSleepState)
	{
		pSleepState->playerTrySleep();
	}
}

SleepState* ClientPlayer::GetSleepState()
{
	if (!m_SleepState) m_SleepState = dynamic_cast<SleepState*>(getLocoCurActionStatePtr("Sleep"));
	return m_SleepState;
}

bool ClientPlayer::isSleeping()
{
	auto pSleepState = GetSleepState();
	if (pSleepState)
	{
		return pSleepState->isSleeping();
	}
	return false;
}

bool ClientPlayer::isRestInBed()
{
	auto pSleepState = GetSleepState();
	if (pSleepState)
	{
		return pSleepState->isRestInBed();
	}
	return false;
}

void ClientPlayer::wakeUp(bool immediately, bool updateallflag, bool setrevive)
{
	auto pSleepState = GetSleepState();
	if (pSleepState)
	{
		pSleepState->wakeUp(immediately, updateallflag, setrevive);
	}
}


int ClientPlayer::sitInChair(const WCoord& blockpos)
{
	if (g_pPlayerCtrl == this)
	{
		toActionBodyState("ToSit");
	}
	auto pSitState = dynamic_cast<SitState*>(getLocoCurActionBodyStatePtr("Sit"));
	if (pSitState)
	{
		return pSitState->sitInChair(blockpos);
	}
	return 0;
}

int ClientPlayer::GetRangeWorkbenchMaxLevel(int range)
{
	if (!GetWorld()) return 0;

	SocWorkbenchMgr* workbenchmgr = GetWorld()->GetWorkbenchMgr();
	if (!workbenchmgr) return 0;
	return workbenchmgr->GetRangeWorkbenchMaxLevel(getPosition() / 100, range);
}

int ClientPlayer::sitInChairEX(const WCoord& blockpos, const WCoord& targetpos)
{
	if (g_pPlayerCtrl == this)
	{
		toActionBodyState("ToSit");
	}
	auto pSitState = dynamic_cast<SitState*>(getLocoCurActionBodyStatePtr("Sit"));
	if (pSitState)
	{
		return pSitState->sitInChairEX(blockpos, targetpos);
	}
	return 0;
}

int ClientPlayer::sitInEmitter(const WCoord pos, const Rainbow::Vector3f& euler)
{
	if (g_pPlayerCtrl == this)
	{
		toActionBodyState("ToSit");
	}
	/*updateBound(120, 20);
	getLocoMotion()->m_yOffset = 0;
	getLocoMotion()->setPosition(pos.x, pos.y, pos.z);
	static float YawArray[4] = { 90.0f, -90.0f, 0.0f, 180.0f };
	getLocoMotion()->m_RotateYaw = YawArray[dir];
	getLocoMotion()->m_RotationPitch = 0;
	setSitting(true);
	getLocoMotion()->m_Motion = Rainbow::Vector3f(0, 0, 0);
	getBody()->resetPos();

	setJumping(false);
	getLocoMotion()->m_OnGround = true;

	if (hasUIControl()) GetGameEventQue().postRidingChange(1);*/
	auto pSitState = dynamic_cast<SitState*>(getLocoCurActionBodyStatePtr("Sit"));
	if (pSitState)
	{
		return pSitState->sitInEmitter(pos, euler);
	}
	return 0;
}
//
//int ClientPlayer::cookByHearth(const WCoord &blockPos)
//{
//	if (!m_pWorld->isRemoteMode())
//	{
//		if (isSleeping() || isRestInBed() || getSitting() || isDead())
//		{
//			return 253;
//		}
//
//		// ????????
//		/*WCoord curblock = CoordDivBlock(getPosition());
//		if (Rainbow::Abs(curblock.x - blockPos.x) > 3 || Rainbow::Abs(curblock.y - blockPos.y) > 2 || Rainbow::Abs(curblock.z - blockPos.z) > 3)
//		{
//			return 254;
//		}*/
//	}
//
//	// ??????
//	auto RidComp = getRiddenComponent();
//	if (RidComp && RidComp->isRiding())
//	{
//		mountActor(NULL);
//	}
//
//	getLocoMotion()->m_yOffset = 0;
//	WCoord playerPos(0, 0, 0);
//
//	if (m_pWorld->blockExists(blockPos))
//	{
//		int blockdata = m_pWorld->getBlockData(blockPos);
//		int dir = blockdata & 3;
//		
//		// ?????x??????
//		if (dir == 0)
//		{
//			playerPos = blockPos * BLOCK_SIZE + WCoord(-50, 0, 50);
//		}
//		// ?????x??????
//		else if (dir == 1)
//		{
//			playerPos = blockPos * BLOCK_SIZE + WCoord(150, 0, 50);
//		}
//		// ?????z??????
//		else if (dir == 2)
//		{
//			playerPos = blockPos * BLOCK_SIZE + WCoord(50, 0, -50);
//		}
//		// ?????z??????
//		else
//		{
//			playerPos = blockPos * BLOCK_SIZE + WCoord(50, 0, 150);
//		}
//
//		getLocoMotion()->setPosition(playerPos.x, playerPos.y, playerPos.z);
//
//		// ??????????
//		static float YawArray[4] = { -90.0f, 90.0f,180.0f, 0.0f };
//		getLocoMotion()->m_RotateYaw = YawArray[dir];
//		getLocoMotion()->m_RotationPitch = 0;
//	}
//	else
//	{
//		playerPos = blockPos * BLOCK_SIZE + WCoord(50, 0, -50);
//		getLocoMotion()->setPosition(playerPos.x, playerPos.y, playerPos.z);
//		getLocoMotion()->m_RotateYaw = 0.0f;
//		getLocoMotion()->m_PrevRotatePitch = 0;
//	}
//
//	getLocoMotion()->m_Motion = Rainbow::Vector3f(0, 0, 0);
//	setUseHearth(true);
//	setUsingHearthPos(blockPos);
//
//	if (hasUIControl()) GetGameEventQue().postRidingChange(1);
//
//	// ???????????????????????????????????????????????????Ч????????
//	playAnim(SEQ_COOK, true);
//
//	// ???
//	if (m_pWorld->isRemoteMode())
//	{
//		PB_UseHearthCH useHearth;
//		useHearth.set_playeruin(getUin());
//		PB_Vector3 *posh = useHearth.mutable_hearthpos();
//		if (posh)
//		{
//			posh->set_x(blockPos.x);
//			posh->set_y(blockPos.y);
//			posh->set_z(blockPos.z);
//		}
//		PB_Vector3 *posp = useHearth.mutable_playerpos();
//		if (posp)
//		{
//			posp->set_x(playerPos.x);
//			posp->set_y(playerPos.y);
//			posp->set_z(playerPos.z);
//		}
//		useHearth.set_isuse(true);
//
//		if (GameNetManager::getInstance())
//		{
//			GameNetManager::getInstance()->sendToHost(PB_USEHEARTH_CH, useHearth);
//		}
//	}
//
//	return 0;
//}

bool ClientPlayer::isSittingInChair()
{
	if (!getWorld())
		return false;

	return IsChairBlock(getWorld()->getBlockID(CoordDivBlock(getPosition())));
}

bool ClientPlayer::isSittingInStarStationCabin()
{
	if (!getWorld())
		return false;

	int blockId = getWorld()->getBlockID(CoordDivBlock(getPosition()));
	return (blockId == BLOCK_STARSTATION_TRANSFER_CABIN_LEVEL1 || blockId == BLOCK_STARSTATION_TRANSFER_CABIN_LEVEL2) && getSitting();
}

bool ClientPlayer::isStandOnVoidMushroom()
{
	if (!getWorld())
		return false;

	//脚下的方块是虚空菇方块
	int blockId = getWorld()->getBlockID(DownCoord(CoordDivBlock(getPosition())));

	return ((blockId == BLOCK_VOID_MUSHROOM) || (blockId == BLOCK_VOID_MUSHROOM_CAP)) && !getSitting();
}

bool ClientPlayer::isSittingInPianoChair()
{
	auto pSitState = dynamic_cast<SitState*>(getLocoCurActionBodyStatePtr("Sit"));
	if (pSitState)
	{
		return pSitState->isSittingInPianoChair();
	}
	else
		return false;
}

bool ClientPlayer::getUsingPianoPos(int& outx, int& outy, int& outz)
{
	auto pSitState = dynamic_cast<SitState*>(getLocoCurActionBodyStatePtr("Sit"));

	if (pSitState)
	{
		WCoord pos = pSitState->getUsingPianoPos();
		outx = pos.x;
		outy = pos.y;
		outz = pos.z;
		return true;
	}
	else
		return false;
}

void ClientPlayer::standUpFromChair()
{
	if (!getLocoMotion() || !m_pWorld) return;
	auto pSitState = dynamic_cast<SitState*>(getLocoCurActionBodyStatePtr("Sit"));
	if (pSitState)
	{
		pSitState->standUpFromChair();
	}


	//m_LocoMotion->setBound(180, 60);
	updateBound(180, 60);
	getLocoMotion()->m_yOffset = 0;

	WCoord blockpos = CoordDivBlock(getPosition());
	WCoord newpos = blockpos;

	if (IsChairBlock(m_pWorld->getBlockID(blockpos)))
	{
		//BlockBed::setBedOccupied(m_pWorld, blockpos, false);
		//bool succeed = BlockBed::getNearestEmptyChunkCoordinates(newpos, m_pWorld, blockpos, 0);
		BedLogicHandle::setBedOccupied(m_pWorld, blockpos, false);
		bool succeed = BedLogicHandle::getNearestEmptyChunkCoordinates(newpos, m_pWorld, blockpos, 0);

		if (!succeed)
		{
			newpos = TopCoord(blockpos);
		}

		getLocoMotion()->gotoPosition(BlockBottomCenter(newpos), getLocoMotion()->m_RotateYaw, getLocoMotion()->m_RotationPitch);
	}
	else if (isSittingInStarStationCabin() || getBody()->isInStarStationCabin())
	{
		//查找玩家位置的前后四个方块
		bool found = false;
		for (int x = -1; x < 2;)
		{
			for (int z = -1; z < 2;)
			{
				if (getBody()->isInStarStationCabin() && !isSittingInStarStationCabin()) //玩家通过使用工具等渠道改变了角色位置，需要取它之前的位置来判断他是否是从传送舱中出来
				{
					blockpos = CoordDivBlock(getLocoMotion()->m_OldPosition + WCoord(x * 50, 0, z * 50));
				}
				else
				{
					blockpos = CoordDivBlock(getPosition() + WCoord(x * 50, 0, z * 50));
				}

				WorldStarStationTransferCabinContainer* container = dynamic_cast<WorldStarStationTransferCabinContainer*>(m_pWorld->getContainerMgr()->getContainer(blockpos));
				if (container)
				{
					int nStarStationId = container->getConnectedStarStationID();
					int nStatus = container->getStatus();

					if (!m_pWorld->isRemoteMode())
					{
						container->setBindPlayerUin(0);
						if (isSittingInStarStationCabin())
						{
							bool succeed = m_pWorld->getNearestEmptyChunkCoordinates(newpos, blockpos);
							if (!succeed)
							{
								newpos = TopCoord(blockpos);
							}

							getLocoMotion()->gotoPosition(BlockBottomCenter(newpos), getLocoMotion()->m_RotateYaw, getLocoMotion()->m_RotationPitch);
						}

						if (nStatus == STARSTATION_TRANSFERING)
						{
							nStatus = STARSTATION_TRANSFER_SUSPENDED;
							container->setStatus((StarStationTransferStatus)nStatus);
						}

						getBody()->setIsInStarStationCabin(false);

						if (g_pPlayerCtrl == this)
						{
							int blockData = m_pWorld->getBlockData(blockpos);
							m_pWorld->setBlockData(blockpos, blockData & 7);
							ScriptVM::game()->callFunction("OnPlayerLeaveStarStationCabin", "i", nStatus);
						}

						PB_LeaveStarStationCabin leaveCabin;
						leaveCabin.set_uin(getUin());
						leaveCabin.set_starstationid(nStarStationId);
						leaveCabin.set_status(nStatus);
						PB_Vector3* pos = leaveCabin.mutable_cabinpos();
						if (pos)
						{
							pos->set_x(blockpos.x);
							pos->set_y(blockpos.y);
							pos->set_z(blockpos.z);
						}

						GetGameNetManagerPtr()->sendBroadCast(PB_NOTIFY_LEAVE_STARSTATION_CABIN_HC, leaveCabin);
					}
					else
					{
						PB_LeaveStarStationCabin leaveCabin;
						leaveCabin.set_uin(getUin());
						leaveCabin.set_starstationid(nStarStationId);
						leaveCabin.set_status(nStatus);
						PB_Vector3* pos = leaveCabin.mutable_cabinpos();
						if (pos)
						{
							pos->set_x(blockpos.x);
							pos->set_y(blockpos.y);
							pos->set_z(blockpos.z);
						}

						GetGameNetManagerPtr()->sendToHost(PB_LEAVE_STARSTATION_CABIN_CH, leaveCabin);
					}

					found = true;
					break;
				}

				z += 2;
			}

			if (found)
				break;

			x += 2;
		}
	}
	else if (m_sitingPianoPos.x == blockpos.x && m_sitingPianoPos.y == blockpos.y && m_sitingPianoPos.z == blockpos.z)
	{
		//这里不判断方块得ID，因为有可能已经被客机敲掉了
		BlockPiano::setPianoOccupied(m_pWorld, m_usingPianoPos, false);
		this->tryStopPianoSoundAndPaticle();

		getLocoMotion()->gotoPosition(BlockBottomCenter(newpos), getLocoMotion()->m_RotateYaw, getLocoMotion()->m_RotationPitch);


		if (getUin() == GetClientInfoProxy()->getUin())
		{
			MINIW::ScriptVM::game()->callFunction("ClosePianoFrame", NULL);
		}
		setUsingPianoPos(WCoord(0, -1, 0));
		setUsingPianoSitPos(WCoord(0, -1, 0));
	}

	if (isMoveControlActive())
	{
		if (getSitting())
			changeMoveFlag(IFC_Sit, false);
	}
	else
		setSitting(false);
	if (hasUIControl())
	{
		//ge GetGameEventQue().postRidingChange(0);
		MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
			SetData_Number("ridetype", 0);
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GIE_RIDING_CHANGE", sandboxContext);
		}
	}

	if (!m_pWorld->isRemoteMode())
	{
		PB_PlayerSleepHC playerSleepHC;
		playerSleepHC.set_flags(4);

		PB_Vector3* pos = playerSleepHC.mutable_pos();
		pos->set_x(getLocoMotion()->m_Position.x);
		pos->set_y(getLocoMotion()->m_Position.y);
		pos->set_z(getLocoMotion()->m_Position.z);

		GetGameNetManagerPtr()->sendToClient(getUin(), PB_PLAYER_SLEEP_HC, playerSleepHC);
	}
}

//--------- bed, chair 相关 end
bool ClientPlayer::getPunchAtkData(OneAttackData& atkdata, ClientActor* target)
{
	if (target == NULL || target->isDead()) return false;
	if (!target->canAttackWithItem()) return false;

	ATTACK_TARGET_TYPE targettype = target->getAttackTargetType();

	//memset(&atkdata, 0, sizeof(atkdata));
	atkdata.damage_armor = true;
	atkdata.atktype = ATTACK_PUNCH;
	//const ToolDef *tooldef = ToolDefCsv::getInstance()->getToolTable().GetRecord(getCurToolID());
	//if(tooldef) atkdata.atktype = (ATTACK_TYPE)tooldef->AttackType;

	LivingAttrib* attrib = getLivingAttrib();
	if (isAttrShapeShift())
	{
		if (m_PlayerAttrib->m_AttrShapeShiftDef)
			atkdata.atkpoints = m_PlayerAttrib->m_AttrShapeShiftDef->Attacks[ATTACK_PUNCH];
		else
			atkdata.atkpoints = 10.0f;
	}
	else
	{
		atkdata.atkpoints = attrib->getAttackPoint(atkdata.atktype);
		atkdata.enchant_atk = attrib->getEnchantAttackPoint(atkdata.atktype, targettype);
	}
	atkdata.buff_atk = attrib->getModAttrib(MODATTR_ATTACK_PUNCH + atkdata.atktype);
	if (targettype < ATTACK_TARGET_OTHERS) atkdata.buff_atk += attrib->getModAttrib(MODATTR_ATTACK_PLAYER + targettype);

	auto RidComp = getRiddenComponent();
	auto functionWrapper = getFuncWrapper();
	atkdata.critical = functionWrapper && functionWrapper->getFallDistance() > 0 && !getLocoMotion()->m_OnGround && !getLocoMotion()->isOnLadder() && !getLocoMotion()->m_InWater && !(RidComp && RidComp->isRiding());

	atkdata.knockback = attrib->getKnockback(atkdata.atktype, targettype);
	atkdata.knockup = attrib->getKnockUp(atkdata.atktype, targettype);
	atkdata.fromplayer = this;

	return true;
}

bool ClientPlayer::getPunchAtkDataNew(OneAttackData& atkdata, ClientActor* target, int targetIndex)
{
	if (target == NULL || target->isDead()) return false;
	if (!target->canAttackWithItem()) return false;

	ATTACK_TARGET_TYPE targettype = target->getAttackTargetType();

	//memset(&atkdata, 0, sizeof(atkdata));
	atkdata.damage_armor = true;

	LivingAttrib* attrib = getLivingAttrib();
	if (!attrib) return false;
	float atkPoints = 0.f, enchantPoints = 0.f;
	if (isAttrShapeShift())
	{
		MonsterDef* def = m_PlayerAttrib->m_AttrShapeShiftDef;
		if (def)
		{
			// 独立爆炸
			if (def->AttackType == 2)
			{
				atkdata.atkTypeNew |= (1 << ATTACK_EXPLODE);
				// 物理攻击力
				atkdata.explodePoints[ATTACK_PUNCH] = def->Attacks[ATTACK_PUNCH];
				// 元素攻击力
				for (int atkType = ATTACK_FIRE; atkType <= ATTACK_ICE; atkType++)
				{
					atkdata.explodePoints[atkType - ATTACK_EXPLODE] = def->Attacks[atkType + 1];
				}
			}
			else
			{
				// 近战
				if (def->AttackType == 0)
				{
					// 物理
					if (def->Attacks[ATTACK_PUNCH] > 0)
					{
						atkdata.atkTypeNew |= (1 << ATTACK_PUNCH);
						atkdata.atkPointsNew[ATTACK_PUNCH] = def->Attacks[ATTACK_PUNCH];
					}
				}
				// 远程
				//else if (def->AttackType == 1)
				//{
				//	// 物理
				//	if (def->Attacks[ATTACK_RANGE] > 0)
				//	{
				//		atkdata.atkTypeNew |= (1 << ATTACK_RANGE);
				//		atkdata.atkPointsNew[ATTACK_RANGE] = def->Attacks[ATTACK_RANGE];
				//	}
				//}
				for (int atkType = ATTACK_FIRE; atkType <= MAX_MAGIC_ATTACK; atkType++)
				{
					const int index = AtkType2ArmorIndex((ATTACK_TYPE)atkType);
					if (def->Attacks[index] > 0)
					{
						atkdata.atkTypeNew |= (1 << index);
						atkdata.atkPointsNew[index] = def->Attacks[index];
					}
				}
			}
		}
		else
		{
			atkdata.atkTypeNew = (1 << ATTACK_PUNCH);
			atkdata.atkPointsNew[ATTACK_PUNCH] = 10.0f;
		}
	}
	else
	{
		const ToolDef* tooldef = GetDefManagerProxy()->getToolDef(getCurToolID());
		if (tooldef)
		{
			// 工具不能有爆炸类型
			if (tooldef->AttackType == 2)
			{
				char errorMsg[256];
				sprintf(errorMsg, "Tool: %d AttackType error!", getCurToolID());
				SANDBOX_ASSERT(false && errorMsg);
				return false;
			}
		}

		// 只拿近战伤害
		atkdata.atkTypeNew |= (1 << ATTACK_PUNCH);
		atkdata.atkPointsNew[ATTACK_PUNCH] = attrib->getAttackPoint(ATTACK_PUNCH);
		// 附魔攻击力加成（单攻击类型攻击力加成以及所有攻击力加成）
		atkdata.atkPointsNew[ATTACK_PUNCH] += attrib->getEnchantAttackPoint(ATTACK_PUNCH, targettype);

		int index = 0;
		for (int atkType = ATTACK_FIRE; atkType <= MAX_MAGIC_ATTACK; atkType++)
		{
			atkPoints = attrib->getAttackPoint((ATTACK_TYPE)atkType);
			if (atkPoints > 0)
			{
				index = AtkType2ArmorIndex(ATTACK_TYPE(atkType));
				atkdata.atkTypeNew |= (1 << index);
				atkdata.atkPointsNew[index] = atkPoints;
				// 附魔攻击力加成（单攻击类型攻击力加成以及所有攻击力加成）
				atkdata.atkPointsNew[index] += attrib->getEnchantAttackPoint((ATTACK_TYPE)atkType, targettype);
			}
		}
	}

	// 根据玩家的头部的朝向来计算攻击位置
	auto yaw = getLocoMotion()->m_RotateYaw;
	auto pitch = getLocoMotion()->m_RotationPitch;
	Rainbow::Vector3f dir;
	PitchYaw2Direction(dir, yaw, pitch);
	auto headPos = getLocoMotion()->m_Position + WCoord(0, 160, 0); // 大约头部的位置
	WorldRay check_ray;
	check_ray.m_Origin = headPos.toWorldPos();
	check_ray.m_Dir = dir;
	check_ray.m_Range = 500.0f;
	if (check_ray.m_Dir.x == 0)
		check_ray.m_Dir.x = 0.001f;
	if (check_ray.m_Dir.y == 0)
		check_ray.m_Dir.y = 0.001f;
	if (check_ray.m_Dir.z == 0)
		check_ray.m_Dir.z = 0.001f;
	MINIW::Ray ray;
	check_ray.getRelativeRay(ray);

	std::vector<TypeCollideAABB> boxs;
	if (target->getLocoMotion())
		target->getLocoMotion()->getMultiTypeCollidBoxs(boxs);

	bool isIntersect = false;
	auto rot = Rainbow::XYZAngleToQuat(0, yaw + 180, 0);
	std::string findtype;
	float t = 0;
	float min_t = 999999.0f;
	for (auto& typebox : boxs)
	{
		auto& box = typebox.box;
		Rainbow::Vector3f minpt = box.minPos().toVector3();
		Rainbow::Vector3f maxpt = box.maxPos().toVector3();

		if (ray.intersectBoxWithRot(minpt, maxpt, rot, &t))
		{
			if (t < min_t)
			{
				min_t = t;
				atkdata.parttype = MNSandbox::getAttackBodyType(typebox.part);
			}
		}
	}
	if (atkdata.parttype == ATTACK_BODY_HEAD)
		atkdata.critical = true;

	auto RidComp = getRiddenComponent();
	auto functionWrapper = getFuncWrapper();
	// atkdata.critical = functionWrapper && functionWrapper->getFallDistance() > 0 && !getLocoMotion()->m_OnGround && !getLocoMotion()->isOnLadder() && !getLocoMotion()->m_InWater && !(RidComp && RidComp->isRiding());

	atkdata.knockback = 0; // attrib->getKnockback(atkdata.atktype, targettype);
	atkdata.knockup = 0;   // attrib->getKnockUp(atkdata.atktype, targettype);
	atkdata.fromplayer = this;

	// 范围伤害控制参数（现在是随机的，后续可以根据离玩家距离，对actors进行排序） code-by:liya
	if (targetIndex > 1)
	{
		int paramA = GetLuaInterfaceProxy().get_lua_const()->dampingControlA;
		int paramB = GetLuaInterfaceProxy().get_lua_const()->dampingControlB;
		if (targetIndex > 7) targetIndex = 7;
		atkdata.damping = (float)paramA / (paramB * targetIndex);
	}

	return true;
}

/*void ClientPlayer::doActualItemSkillAttack(ClientActor *target, ATTACK_TYPE type, int demage)
{
	OneAttackData atkdata;

	if(target==NULL || target->isDead()) return ;
	if(!target->canAttackWithItem()) return ;
	ATTACK_TARGET_TYPE targettype = target->getAttackTargetType();

	memset(&atkdata, 0, sizeof(atkdata));
	atkdata.damage_armor = true;
	atkdata.atktype = type;
	atkdata.atkpoints = demage;
	atkdata.enchant_atk = 0;
	atkdata.buff_atk = 0;
	LivingAttrib *attrib = getLivingAttrib();
	if(targettype < ATTACK_TARGET_OTHERS) atkdata.buff_atk += attrib->getModAttrib(MODATTR_ATTACK_PLAYER+targettype);
	atkdata.critical = m_FallDistance>0 && !getLocoMotion()->m_OnGround && !getLocoMotion()->isOnLadder() && !getLocoMotion()->m_InWater && !isRiding();
	atkdata.fromplayer = this;
	target->attackedFrom(atkdata, this);
} */


void ClientPlayer::GotoTeamPos(int id)
{
	if (GetWorldManagerPtr())
	{
		WCoord pt = GetWorldManagerPtr()->getTeamPrePoint(this, id);
		IGameMode* gmaker = GetWorldManagerPtr()->m_RuleMgr;
		if (pt.y < 0 || (gmaker && gmaker->getGameStage() == CGAME_STAGE_RUN))
		{
			if (gmaker && gmaker->hasInitialPoint(id))
			{
				pt = gmaker->getInitialPoint(id).y >= 0 ? \
					gmaker->getInitialPoint(id) : \
					gmaker->getInitialPoint(0);
			}
			else
			{
				pt = GetWorldManagerPtr()->getTeamSpawnPoint(this, id);
			}
		}

		if (gmaker && gmaker->getRuleOptionVal(GMRULE_SAVEMODE) != 0)
		{
			this->gotoBlockPos(this->getWorld(), pt, false);
			MpActorTrackerEntry* entry = m_pWorld->getMpActorMgr()->getTrackerEntry(getObjId());
			if (entry) entry->sendActorMovementToClient(getUin(), this, getLocoMotion()->m_RotateYaw, getLocoMotion()->m_RotationPitch);
		}
	}
}


void ClientPlayer::setGVoiceInfo(int speakerswitch)
{
	if (!hasUIControl())
	{
		int nameTexId = 0;
		if (speakerswitch == 1) nameTexId = 128;
		getBody()->addNameTexId(nameTexId);
	}
}

int ClientPlayer::getMass()
{
	return 60000;
}

//void ClientPlayer::attachUIModelView(ModelView *modelview, int index)
//{
//	if(m_UIViewBody == NULL)
//	{
//		m_UIViewBody = ENG_NEW(ActorBody)(this);
//		m_UIViewBody->initPlayer(getBody()->getPlayerIndex());
//	}
//
//	getLivingAttrib()->applyEquips(m_UIViewBody);
//
//	m_UIViewBody->attachUIModelView(modelview, index);
//	m_UIViewBody->playAnim(SEQ_STAND);
//	m_UIModelView = modelview;
//}

//void ClientPlayer::detachUIModelView(int index)
//{
//	m_UIViewBody->detachUIModelView(m_UIModelView, index);
//	m_UIModelView = NULL;
//}

bool ClientPlayer::isSkinning()
{
	return m_isSkinning;
}

void ClientPlayer::setSkinning(bool b)
{
	m_isSkinning = b;
}

bool ClientPlayer::isFlying()
{
	return getFlying();
}

void ClientPlayer::createEnderEye()
{
	auto soundComp = getSoundComponent();
	if (soundComp)
	{
		soundComp->playSound("misc.throw", 1.0f, 1.0f);
	}

	ActorDungeonEye::create(m_pWorld, this);
}

void ClientPlayer::createFirework(int firetype, int firedata)
{
	ActorFirework::create(getWorld(), this, firetype, firedata);
}

//------???? teleport  begin
void ClientPlayer::teleportMap(int targetmap)
{
	ActorBall* ball = dynamic_cast<ActorBall*>(getCatchBall());
	if (ball)
	{
		auto bindAComponent = ball->getBindActorCom();
		if (bindAComponent)
		{
			bindAComponent->setBindInfo(-getObjId(), WCoord(0, 0, 0));
		}
	}
	if (m_pWorld->getPortalPoint().y < 0) m_pWorld->resetPortalPoint(CoordDivBlock(getPosition()));
	GetWorldManagerPtr()->teleportPlayer(this, targetmap);
	/*
	BlockPortal *pblock = static_cast<BlockPortal *>(g_BlockMtlMgr.getMaterial(BLOCK_PORTAL));
	int dir, xoffset, zoffset;
	WCoord origin = pblock->getPortalOriginAndDir(m_pWorld, m_pWorld->getPortalPoint(), dir, xoffset, zoffset);
	m_pWorld->getEffectMgr()->playParticleEffect("particles/portal_feedback.ent", origin*BLOCK_SIZE+pblock->getPortalCenterOffset(dir), 32, WorldEffectContainer::dir2Yaw(dir));
	*/
}

void ClientPlayer::transferMap(int targetmap, int destStarStationId, WCoord& pos)
{
	GetWorldManagerPtr()->transferPlayer(this, targetmap, destStarStationId, pos);
}

WCoord ClientPlayer::getRealLandingPoint(int targetmap, World* pworld)
{
	WCoord landingPt = GetWorldManagerPtr()->getLandingPoint(targetmap);
	if (landingPt.y < 0) //没有世界降落点
	{
		if (targetmap == 0)
			landingPt = GetWorldManagerPtr()->getSpawnPointEx(pworld);
		else
			landingPt = pworld->createSpawnPoint();
		GetWorldManagerPtr()->addLandingPoint(targetmap, landingPt);
	}

	if (m_LandingPoints[targetmap].y > 0) //有角色降落点
	{
		landingPt = m_LandingPoints[targetmap];
	}

	if (isRocketTeleport())
	{
		landingPt.y = GetWorldManagerPtr()->m_SurviveGameConfig->rocketconfig.landing_height;
	}
	if (getWorld())
	{
		m_LandingPoints[getWorld()->getCurMapID()] = CoordDivBlock(getPosition());
	}

	return landingPt;
}

void ClientPlayer::teleportRidingRocket(int targetmap)
{
	World* pworld = GetWorldManagerPtr()->getOrCreateWorld(targetmap, this);
	if (!pworld) return;

	WCoord landingPt = getRealLandingPoint(targetmap, pworld);

	LOG_INFO("kekeke landingPt pre: x:%d, y:%d z:%d", landingPt.x, landingPt.y, landingPt.z);
	landingPt = GetWorldManagerPtr()->getRocketRealLandingPt(landingPt, pworld);
	LOG_INFO("kekeke landingPt after: x:%d, y:%d z:%d", landingPt.x, landingPt.y, landingPt.z);
	pworld->syncLoadChunk(landingPt, 16);

	ActorRocket* rocket = ActorRocket::create(pworld, landingPt.x * BLOCK_SIZE, landingPt.y * BLOCK_SIZE, landingPt.z * BLOCK_SIZE, 0, 0, 0);
	if (rocket)
	{
		mountActor(rocket, true);
		//rocket->changeFuel(100, true);
		rocket->changeState(LANDING);
	}

	//if (g_pPlayerCtrl)
	//{
	//	char sMapID[4];
	//	sprintf(sMapID, "%d", targetmap);
	//	g_pPlayerCtrl->statisticToWorld(getUin(), 30005, "", g_pPlayerCtrl->getCurWorldType(), sMapID);
	//}
}

void ClientPlayer::teleportByRocket(int targetmap)
{
	//changeState(HOLD_STILL);
	mountActor(NULL, true);
	teleportMap(targetmap);
	setRocketTeleport(true);
}

void ClientPlayer::clearTrackerEntry()
{
	m_pTrackerEntrys.clear();
}

void ClientPlayer::addTrackerEntry(MpActorTrackerEntry* entry)
{
	m_pTrackerEntrys.push_back(entry);
}

std::vector<MpActorTrackerEntry*>& ClientPlayer::getTrackerEntrys()
{
	return m_pTrackerEntrys;
}

void ClientPlayer::teleportHome()
{
	auto RidComp = getRiddenComponent();
	if (RidComp && RidComp->isRiding())
		mountActor(NULL);

	ParticlesComponent::playParticles(this, "1031_2.ent", 3 * 20, NULL);

	SandboxContext context(nullptr);
	context.SetData_Number("curToolId", (double)getCurToolID());
	SandboxResult result = Event().Emit("teleportHome", context);

	World* newworld = result.GetData_RefEx<World>("world");
	WCoord pos = result.GetData_UserObject<WCoord>("pos");

	if (newworld)
	{
		addRef();
		getWorld()->getActorMgr()->ToCastMgr()->despawnActor(this);
		gotoBlockPos(newworld, pos, false);
		newworld->getActorMgr()->ToCastMgr()->spawnPlayerAddRef(this);
		release();
		GetWorldManagerPtr()->m_RenderEyeMap = 0;
	}
	else
	{
		gotoBlockPos(getWorld(), pos, false);
	}

	if (!hasUIControl())
	{
		//syncPos2Client();
		//同步客机方式改为通知传送（适配从其他星球返回） by：jeff
		PB_ActorTeleportHC actorTeleportHC;
		actorTeleportHC.set_objid(getObjId());
		actorTeleportHC.set_targetmap(getCurMapID());

		WCoord pos = getPosition();
		actorTeleportHC.mutable_targetpos()->set_x(pos.x);
		actorTeleportHC.mutable_targetpos()->set_y(pos.y);
		actorTeleportHC.mutable_targetpos()->set_z(pos.z);

		GetGameNetManagerPtr()->sendToClient(getUin(), PB_ACTOR_TELEPORT_HC, actorTeleportHC);
		setTeleportPos(pos);
	}

	ParticlesComponent::playParticles(this, "1001.ent");

	auto soundComp = getSoundComponent();
	if (soundComp)
	{
		soundComp->playSound("misc.rebirth", 1.0f, 1.0f);
	}
}

void ClientPlayer::gotoBlockPosAllWorld(int targetMapid, const WCoord& blockpos, bool random_offset)
{
	World* curWorld = getWorld();
	if (!curWorld)
	{
		return;
	}
	if (targetMapid != curWorld->getCurMapID())
	{
		World* newworld = GetWorldManagerPtr()->getOrCreateWorld(targetMapid, this);
		addRef();
		curWorld->getActorMgr()->ToCastMgr()->despawnActor(this);
		gotoBlockPos(newworld, blockpos, false);
		newworld->getActorMgr()->ToCastMgr()->spawnPlayerAddRef(this);
		release();
		GetWorldManagerPtr()->m_RenderEyeMap = 0;
	}
	else
	{
		gotoBlockPos(curWorld, blockpos, false);
	}

	if (!hasUIControl())
	{
		//syncPos2Client();
		//同步客机方式改为通知传送（适配从其他星球返回） by：jeff
		PB_ActorTeleportHC actorTeleportHC;
		actorTeleportHC.set_objid(getObjId());
		actorTeleportHC.set_targetmap(getCurMapID());

		WCoord pos = getPosition();
		actorTeleportHC.mutable_targetpos()->set_x(pos.x);
		actorTeleportHC.mutable_targetpos()->set_y(pos.y);
		actorTeleportHC.mutable_targetpos()->set_z(pos.z);

		GetGameNetManagerPtr()->sendToClient(getUin(), PB_ACTOR_TELEPORT_HC, actorTeleportHC);
		setTeleportPos(pos);
	}
}

// 20211103 ???λ???????? codeby:liusijia
void ClientPlayer::syncPos2Client(bool sync_motion)
{
	PB_SyncPlayerPositionHC syncPos;
	WCoord& pos = getPosition();
	syncPos.mutable_position()->set_x(pos.x);
	syncPos.mutable_position()->set_y(pos.y);
	syncPos.mutable_position()->set_z(pos.z);
	const Rainbow::Vector3f& motion = getLocoMotion()->getMotion();
	if (sync_motion)
	{
		auto mut_motion = syncPos.mutable_motion();
		mut_motion->set_x(motion.x);
		mut_motion->set_y(motion.y);
		mut_motion->set_z(motion.z);
	}
	GetGameNetManagerPtr()->sendToClient(getUin(), PB_SYNC_PLAYER_POS_HC, syncPos);
	if (m_MoveControl && m_MoveControl->isActive())
	{
		m_MoveControl->onSyncToPos(pos, motion);
	}
}

void ClientPlayer::onClientRspSyncPos(unsigned long long tick)
{
	if (m_MoveControl && m_MoveControl->isActive())
	{
		WCoord pos;
		Rainbow::Vector3f motion;
		m_MoveControl->onClientRspSyncToPos(pos, motion, tick);
		
		if (getLocoMotion())
		{
			getLocoMotion()->setPosition(pos.x, pos.y, pos.z);
			getLocoMotion()->m_Motion = motion; // TODO: 包成方法
		}
	}
}

//------???? teleport  end

PLAYER_GENIUS_TYPE ClientPlayer::getGeniusType()
{
	ActorGeniusMgr* geniusMgr = GET_SUB_SYSTEM(ActorGeniusMgr);
	if (geniusMgr && geniusMgr->isNewGeniusSysValid(getUin())) //特长拆分系统生效
	{
		return (PLAYER_GENIUS_TYPE)geniusMgr->getGeniusType(getUin());
	}
	else
	{
		const RoleDef* def = GetDefManagerProxy()->getRoleDef(getBody()->getModelID(), getBody()->getGeniusLv());
		if (def == NULL)
		{
#ifndef IWORLD_SERVER_BUILD
			assert(0);
#endif
			//LOG_INFO("ClientPlayer::getGeniusType failed ModelID=%d, GeniusLv=%d", getBody()->getModelID(), getBody()->getGeniusLv());
			return GENIUS_BASEATK_INC;
		}

		return (PLAYER_GENIUS_TYPE)def->GeniusType;
	}
}

float ClientPlayer::getGeniusValue(PLAYER_GENIUS_TYPE t, float* extvalue)
{
	// ?淨??????????????????Ч
	if (GetWorldManagerPtr() && !GetWorldManagerPtr()->getPlayerPermit(ENABLE_SPECIALPROP))
	{
		if (extvalue)
		{
			extvalue[0] = 0;
			extvalue[1] = 0;
		}
		return 0;
	}

	ActorGeniusMgr* geniusMgr = GET_SUB_SYSTEM(ActorGeniusMgr);
	if (geniusMgr && geniusMgr->isNewGeniusSysValid(getUin())) //特长拆分系统生效
	{
		if (extvalue)
		{
			extvalue[0] = geniusMgr->getPlayerGeniusValue(getUin(), t, 1);
			extvalue[1] = geniusMgr->getPlayerGeniusValue(getUin(), t, 2);
		}

		return geniusMgr->getPlayerGeniusValue(getUin(), t, 0);
	}
	else
	{
		const RoleDef* def = GetDefManagerProxy()->getRoleDef(getBody()->getModelID(), getBody()->getGeniusLv());
		if (def == NULL)
		{
#ifndef IWORLD_SERVER_BUILD
			assert(0);
#endif
			LOG_INFO("ClientPlayer::getGeniusValue failed ModelID=%d, GeniusLv=%d", getBody()->getModelID(), getBody()->getGeniusLv());
			return 0;
		}

		if (def->GeniusType != t)
		{
			if (extvalue)
			{
				extvalue[0] = 0;
				extvalue[1] = 0;
			}
			return 0;
		}
		else
		{
			if (extvalue)
			{
				extvalue[0] = def->GeniusValue[1];
				extvalue[1] = def->GeniusValue[2];
			}
			return def->GeniusValue[0];
		}
	}
}

void ClientPlayer::changeRoleData(PB_RoleData* pRoleData)
{
	//LOG_INFO("ClientPlayer::changeRoleData():");
	storeBuff(pRoleData->mutable_buff(), getLivingAttrib());
	storeDir(pRoleData->mutable_dir(), getLocoMotion());
	PlayerAttrib* pstPlayerAttrib = dynamic_cast<PlayerAttrib*>(getAttrib());
	storeAttr(pRoleData, pstPlayerAttrib);
	if (pstPlayerAttrib->getHP() < 0)
	{
		PB_DieInfo* dieinfo = pRoleData->mutable_dieinfo();
		const SocAttackInfo& attackinfo = getSocAttackInfo();

		dieinfo->set_atktype(attackinfo.atktype);
		dieinfo->set_buffid(attackinfo.buffId);
		dieinfo->set_bufflevel(attackinfo.buffLevel);
		dieinfo->set_toolid(attackinfo.toolid);
		dieinfo->set_playerid(attackinfo.playerid);
		dieinfo->set_length(attackinfo.length);
		dieinfo->set_mobid(attackinfo.mobid);
		dieinfo->set_survival_time(getSurvivalTime());
	}

	pRoleData->set_lastlogintime((int)time(NULL));
	pRoleData->set_loginnum(m_LoginNum);
	pRoleData->set_owid(getOWID());
	pRoleData->set_uin(getUin());
	storePak(pRoleData->mutable_package(), getBackPack(), getCurShortcut());
	storeEXTPak(pRoleData->mutable_package(), getBackPack(), getCurShortcut(), this);
	storePos(pRoleData->mutable_pos(), getLocoMotion());
	pRoleData->mutable_pos()->set_map(getCurMapID());

	WCoord revivePoint;//default 0,0,0
	SandboxResult result = Event().Emit("revive_getRevivePoint", SandboxContext(nullptr));
	if (result.IsExecSuccessed())
		revivePoint = result.GetData_UserObject<WCoord>("point");
	storePos(pRoleData->mutable_package()->mutable_revicepos(), &revivePoint);
	pRoleData->mutable_package()->mutable_revicepos()->set_map(0);

	pRoleData->set_flags(m_Flags);
	auto functionWrapper = getFuncWrapper();
	float FallDistance = functionWrapper ? functionWrapper->getFallDistance() : 0.0f;
	pRoleData->set_falldist(FallDistance);
	pRoleData->set_liveticks(m_LiveTicks);

	auto RidComp = getRiddenComponent();
	WORLD_ID RidingActorObjId = 0;
	if (RidComp)
	{
		RidingActorObjId = RidComp->getRidingActorObjId();
	}
	pRoleData->set_rideactorid(RidingActorObjId);

	auto CarryComp = getCarryComponent();
	WORLD_ID CarringActorID = 0;
	if (CarryComp)
	{
		CarringActorID = CarryComp->getCarringActorID();
	}
	pRoleData->set_carringactorid(CarringActorID);
	return;
}

void ClientPlayer::reStoreRoleData(const PB_RoleData& roleData)
{
	//LOG_INFO("ClientPlayer::reStoreRoleData():");
	restoreBuff(roleData.buff(), getLivingAttrib());
	restoreDir(roleData.dir(), getLocoMotion());
	PlayerAttrib* pstPlayerAttrib = dynamic_cast<PlayerAttrib*>(getAttrib());
	restoreAttr(roleData, pstPlayerAttrib);
	if (pstPlayerAttrib->getHP() < 0)
	{
		const PB_DieInfo& dieinfo = roleData.dieinfo();
		SocAttackInfo& info = getSocAttackInfo();
		info.atktype = (ATTACK_TYPE)dieinfo.atktype();
		info.survival_time = dieinfo.survival_time();
		info.buffId = dieinfo.buffid();
		info.buffLevel = dieinfo.bufflevel();
		info.toolid = dieinfo.toolid();
		info.playerid = dieinfo.playerid();
		info.length = dieinfo.length();
		info.mobid = dieinfo.mobid();
	}
	restorePak(roleData.package(), getBackPack(), this);

	WCoord pos;
	restorePos(roleData.pos(), &pos);
	m_CurMapID = roleData.pos().map();
	getLocoMotion()->gotoPosition(pos, getLocoMotion()->m_RotateYaw, getLocoMotion()->m_RotationPitch);
	m_LoginNum = roleData.loginnum() + 1;

	WCoord revivePoint;
	restorePos(roleData.package().revicepos(), &revivePoint);
	SandboxContext context;
	context.SetData_UserObject("point", revivePoint);
	Event().Emit("revive_setRevivePoint", context);

	m_Flags = roleData.flags();

	auto functionWrapper = getFuncWrapper();
	if (functionWrapper)
	{
		functionWrapper->setFallDistance(roleData.falldist());
	}

	m_LiveTicks = roleData.liveticks();
	//m_RidingActor = roleData.rideactorid();
	if (roleData.rideactorid() != 0)
	{
		auto RidComp = sureRiddenComponent();
		if (RidComp)
		{
			RidComp->setRidingActorObjId(roleData.rideactorid());
		}
	}

	//m_CarryingActor = roleData.carringactorid();

	if (roleData.carringactorid() != 0)
	{
		auto CarryComp = sureCarryComponent();
		if (CarryComp)
		{
			CarryComp->setCarringActorID(roleData.carringactorid());
		}
	}

	m_ServerPosCmp = getLocoMotion()->getPosition();
	m_ServerYawCmp = getLocoMotion()->m_RotateYaw;
	m_ServerPitchCmp = getLocoMotion()->m_RotationPitch;
	return;
}

void ClientPlayer::updateChunkView()
{
	if (m_pWorld)
	{
		int uin = this->getUin();
		m_ChunkViewer.setUin(uin);
		m_ChunkViewer.updateChunkView(m_pWorld, getPosition(), ClientPlayer::GetCurViewRange(this)/*getCurViewRange()*/);
		//int nBlockID = m_pWorld->getBlockID(m_RevivePoint);

#ifndef DEDICATED_SERVER
		//?????ò???
		if (m_ChunkViewer.getLoadChunksSize() == 0)
		{
			MNSandbox::GetGlobalEvent().Emit<>("StatisticTerrgen_ReportChunkBiome");
			//????????????Ч
			if (g_pPlayerCtrl && (g_pPlayerCtrl == this))
			{
				Event2().Emit<>("revive_checkPlayReviveEffect");
			}
		}
#endif
	}
}

void ClientPlayer::addMoveStats(const WCoord& dp)
{
	auto RidComp = getRiddenComponent();
	if (RidComp && RidComp->isRiding()) return;

	if (getLocoMotion()->m_InWater)
	{
		float dist = dp.length() / BLOCK_FSIZE;
		getPlayerAttrib()->useStamina(STAMINA_SWIM, dist);
	}
	else if (getLocoMotion()->m_OnGround)
	{
		WCoord newdp(dp.x, 0, dp.z);
		float dist = newdp.length() / BLOCK_FSIZE;
		if (getRun())
			getPlayerAttrib()->useStamina(STAMINA_SPRINT, dist);
		else
		{
			getPlayerAttrib()->useStamina(STAMINA_WALK, dist);
		}
		//if(dist > 0) m_OwnerActor->addAchievement(2, ACHIEVEMENT_WALKDIST, 0, walkdist);
	}
	else
	{
		//fly
	}
}


void ClientPlayer::onSendChat(const std::string& content)
{
	// ???????????:???????:ж?????
	ObserverEvent_PlayerContent obevent(getUin(), content);
	GetObserverEventManager().OnTriggerEvent("Player.InputContent", &obevent);
}

void ClientPlayer::onInputContent(const std::string& content, bool needSync/*=true*/)
{
	if (!m_pWorld)
		return;

	if (GetWorldManagerPtr()->isGameMakerRunMode())
	{
#if defined(BUILD_MINI_EDITOR_APP) || (OGRE_PLATFORM == OGRE_PLATFORM_WIN32 && defined(STUDIO_SERVER))
		if (auto chat = MNSandbox::GetCurrentChatRoot()->ToCast<MNSandbox::SandboxChatService>())
		{
			chat->GetNotifyNewInputContent()->Emit(getUin(), content);
		}
#endif
	}

	if (m_pWorld->isRemoteMode())
	{
		PB_InputContentCH inputContentCH;
		inputContentCH.set_objid(getObjId());
		inputContentCH.set_content(content);

		GetGameNetManagerPtr()->sendToHost(PB_PLAYER_INPUTCONTENT_CH, inputContentCH);
		return;
	}

	// ???????????
	ObserverEvent_PlayerContent obevent(getUin(), content);
	GetObserverEventManager().OnTriggerEvent("Player.NewInputContent", &obevent);
}

void ClientPlayer::setCheckBoxScale(int scale)
{
	m_checkboxscale = scale > 1 ? scale : 1;
}

bool ClientPlayer::openBoxByPos(int x, int y, int z)
{
	if (!m_pWorld) { return false; }
	// 801 802   ?????? ????ˉ
	WCoord pos = WCoord(x, y, z);
	BlockMaterial* pmtl = m_pWorld->getBlockMaterial(pos);
	if (pmtl)
	{
		int itemid = pmtl->getBlockResID();
		ItemDef* def = GetDefManagerProxy()->getItemDef(itemid);
		if (def && (def->ItemGroup == 802 || def->ItemGroup == 800 || def->ItemGroup == 801))
		{
			Rainbow::Vector3f colpoint = WCoord(x, y, z).toVector3();
			if (m_pWorld->CheckBlockSettingEnable(pmtl, ENABLE_BEOPERATED) > 0 && pmtl->onTrigger(m_pWorld, pos, DirectionType::DIR_POS_Z, this, colpoint))
			{
				if (m_pWorld && !m_pWorld->isRemoteMode()) {
					// ???????????
					ObserverEvent_ActorBlock obevent((long long)getUin(), pmtl->getBlockResID(), pos.x, pos.y, pos.z);
					GetObserverEventManager().OnTriggerEvent("Block.Trigger", &obevent);
				}
			}
			return true;
		}
	}
	return false;
}

bool ClientPlayer::forceOpenBoxUI(int itemid)
{
	ItemDef* def = GetDefManagerProxy()->getItemDef(itemid);
	if (def && (def->ItemGroup == 802 || def->ItemGroup == 800))
	{
		notifyOpenWindow2Self(itemid);
		return true;
	}
	return false;
}

bool ClientPlayer::openDevGoodsBuyDialog(int itemid, const char* desc /* = NULL*/)
{
	if (desc == nullptr)
	{
		desc = "";
	}
	if (hasUIControl())
	{
		MINIW::ScriptVM::game()->callFunction("OpenDevGoodsBuyDialog", "is", itemid, desc);
	}
	else
	{
		PB_OpenDevGoodsBuyDialogHC dialogHC;
		dialogHC.set_itemid(itemid);
		dialogHC.set_desc(desc);
		GetGameNetManagerPtr()->sendToClient(getUin(), PB_OPEN_DEVGOODSBUY_DIALOGHC, dialogHC);
	}

	return true;
}

void ClientPlayer::syncUseItemByHomeLand(int itemid, int num)
{
	if (m_pWorld && !m_pWorld->isRemoteMode() && GetGameNetManagerPtr())
	{
		PB_UseItemByHomelandHC useItemByHomelandHC;
		useItemByHomelandHC.set_itemid(itemid);
		useItemByHomelandHC.set_itemnum(num);

		GetGameNetManagerPtr()->sendToClient(getUin(), PB_USEITEM_BY_HOMELAND_HC, useItemByHomelandHC);
	}
}

EXPORT_SANDBOXGAME void LoadPackContainer(BackPack* backpack, int baseindex, const flatbuffers::Vector<flatbuffers::Offset<FBSave::ItemIndexGrid>>* items)
{
	PackContainer* pack = dynamic_cast<PackContainer*>(backpack->getContainer(baseindex));
	if (items == NULL) return;
	for (size_t i = 0; i < items->size(); i++)
	{
		auto item = items->Get(i);
		BackPackGrid* grid = pack->index2Grid(item->index());
		if (grid) grid->load(item);
	}
}

EXPORT_SANDBOXGAME flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::ItemIndexGrid>>> SavePackContainer(flatbuffers::FlatBufferBuilder& builder, BackPack* backpack, int baseindex)
{
	PackContainer* pack = dynamic_cast<PackContainer*>(backpack->getContainer(baseindex));
	if (pack == NULL) return 0;

	flatbuffers::Offset<FBSave::ItemIndexGrid> grids[128];
	int count = 0;

	for (int i = 0; i < pack->getGridCount(); i++)
	{
		BackPackGrid* itemgrid = &pack->m_Grids[i];

		grids[count++] = itemgrid->saveWithIndex(builder);
	}

	if (count > 0) return builder.CreateVector(grids, count);
	else return 0;
}

flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::ItemIndexGrid>>> SavePackContainerEx(flatbuffers::FlatBufferBuilder& builder, long long owid, int baseindex)
{
	if (GetClientInfoProxy()->findWorldDesc(owid))
	{
		int uin = GetClientInfoProxy()->getUin();
		WorldDesc* wdesc = GetClientInfoProxy()->findWorldDesc(owid);
		int specialType = wdesc->_specialType;

		std::string rootpath = GetWorldRootBySpecialType(specialType);

		char path[256];
		sprintf(path, "%s/w%lld/roles/u%d.p", rootpath.c_str(), owid, uin);
		int buflen = 0;
		void* buf = ReadWholeFile(path, buflen);
		if (buf == NULL) return false;

		flatbuffers::Verifier verifier((const uint8_t*)buf, buflen);
		if (!FBSave::VerifyActorPlayerBuffer(verifier))
		{
			free(buf);
			return 0;
		}

		const FBSave::ActorPlayer* wrole = FBSave::GetActorPlayer(buf);
		if (wrole == NULL)
		{
			free(buf);
			return 0;
		}

		const flatbuffers::Vector<flatbuffers::Offset<FBSave::ItemIndexGrid>>* items = NULL;
		if (baseindex == SHORTCUT_START_INDEX)
		{
			items = wrole->shortcut();
		}
		else if (baseindex == SHORTCUT_START_INDEX_EDIT)
		{
			items = wrole->shortcutEdit();
		}
		else
		{
			items = wrole->shortcutEx();
		}


		if (items == NULL)
		{
			free(buf);
			return 0;
		}

		flatbuffers::Offset<FBSave::ItemIndexGrid> grids[128];
		int count = 0;

		for (size_t i = 0; i < items->size(); i++)
		{
			if (i >= sizeof(grids) / sizeof(grids[0]))
			{
				break;
			}

			const FBSave::ItemIndexGrid* item = items->Get(i);
			if (item)
			{
				BackPackGrid grid;
				grid.load(item);
				grids[count++] = grid.saveWithIndex(builder);
			}
		}

		free(buf);
		if (count > 0) return builder.CreateVector(grids, count);
		else return 0;
	}

	return 0;
}

EXPORT_SANDBOXGAME flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::CraftTask>>> SaveCraftTasks(flatbuffers::FlatBufferBuilder& builder, CraftingQueue* craftingQueue)
{
	if (craftingQueue == nullptr) return 0;

	flatbuffers::Offset<FBSave::CraftTask> tasks[30]; // 假设最大任务数为 30
	int count = 0;

	for (int i = 0; i < craftingQueue->getQueueSize(); i++)
	{
		craft::Task t = craftingQueue->getTask(i);
		tasks[count++] = FBSave::CreateCraftTask(builder, t.craftingId, t.count, t.ticksPerItem, t.remainingTicks);
		if (count >= 128) {
			// 如果实际任务数量超过了数组大小，需要处理这种情况，例如记录日志或抛出异常
			LOG_WARNING("Too many crafting tasks! Truncating to 128.");
			break;
		}
	}

	if (count > 0) {
		return builder.CreateVector(tasks, count);
	}
	else {
		return 0;
	}
}

static int CalUinEncoded(unsigned char* encoded, unsigned int uin)
{
	char buffer[64];
	sprintf(buffer, "uin:%x:enc", uin);
	char md5[16];
	MINIW::Md5Calc(md5, buffer, strlen(buffer));

	int nchar = ((unsigned char)(md5[0]) % 16) + 1;
	memcpy(encoded, md5, nchar);
	return nchar;
}

bool ClientPlayer::isHaveRoleFile(long long owid, int uin, int specialType/* = NORMAL_WORLD*/)
{
	std::string rootpath = GetWorldRootBySpecialType(specialType);

	char path[256];
	sprintf(path, "%s/w%lld/roles/u%d.p", rootpath.c_str(), owid, uin);
	core::string fullpath;
	GetFileManager().ToWritePathFull(path, fullpath);

	FileAutoClose fp(fullpath, O_RDONLY | O_BINARY);
	if (fp.isNull()) return NULL;

	return true;
}


bool ClientPlayer::loadFromFile(long long owid, int uin, bool needload/* =true */, int specialType/* = NORMAL_WORLD*/)
{
	bool hasrole = loadFromFileReal(owid, uin, needload, specialType);
	m_NewPlayerFlag = !hasrole; // ???????????????
	return hasrole;
}

bool ClientPlayer::loadFromFileReal(long long owid, int uin, bool needload, int specialType/* = NORMAL_WORLD*/)
{
	std::string rootpath = GetWorldRootBySpecialType(specialType);

	char path[256];
	sprintf(path, "%s/w%lld/roles/u%d.p", rootpath.c_str(), owid, uin);
	int buflen = 0;
	void* buf = ReadWholeFile(path, buflen);
	if (buf == NULL) return false;

	flatbuffers::Verifier verifier((const uint8_t*)buf, buflen);
	if (!FBSave::VerifyActorPlayerBuffer(verifier))
	{
		free(buf);
		return false;
	}

	const FBSave::ActorPlayer* wrole = FBSave::GetActorPlayer(buf);
	if (wrole == NULL)
	{
		free(buf);
		return false;
	}

	if (!needload)
	{
		//getAccountHorseComponent()->setCurAccountHorse(wrole->curhorse());
		if (wrole->curhorse() != 0)
		{
			auto comp = sureAccountHorseComponent();//by__Logo
			if (comp)
				comp->setCurAccountHorse(wrole->curhorse());
		}
		onSetCurShortcutWithFB(wrole);
		free(buf);
		return false;
	}

	int version = wrole->version();
	if (!loadActorCommon(wrole->basedata())) return false;

	if (m_ObjId > GUEST_UIN && m_ObjId != uin)
	{
		LOG_SEVERE("Wrong uin: objid=%d, uin=%d", int(m_ObjId), uin);
		free(buf);
		return false;
	}

	if (version > 4611 || wrole->uinencoded()) //0.18.3,   
	{
		unsigned char curenc[16];
		int nchar = CalUinEncoded(curenc, (unsigned int)m_ObjId);
		for (int i = 0; i < nchar; i++)
		{
			if (curenc[i] != wrole->uinencoded()->Get(i))
			{
				LOG_SEVERE("Wrong encoded");
				free(buf);
				return false;
			}
		}
	}

	SandboxContext context;
	context.SetData_Userdata("userdata", "wrole", const_cast<FBSave::ActorPlayer*> (wrole));
	Event().Emit("loadFromFile", context);

	SetObjId(uin);
	//m_ObjId = uin;
	//m_RidingActor = wrole->riding();
	if (wrole->riding() != 0)
	{
		auto RidComp = sureRiddenComponent();
		if (RidComp)
		{
			RidComp->setRidingActorObjId(wrole->riding());
		}
	}

	m_LoginNum = wrole->loginnum();
	m_CurMapID = wrole->mapid();

	ConstAtLua* lua_const = GetLuaInterfaceProxy().get_lua_const();

	PlayerAttrib* attr = getPlayerAttrib();

	attr->m_FoodLevel = wrole->food();
	attr->m_FoodSatLevel = 0;

	attr->setBasicMaxHP(lua_const->hpmax);
	attr->setBasicOverflowHP(lua_const->hpoverflow);
	attr->setHP(wrole->hp(), true);
	//护甲和毅力
	attr->setArmor(wrole->armor());
	attr->setPerseverance(wrole->perseverance());

	//attr->setBasicMaxStrength(lua_const->strengthmax);
	//attr->setBasicOverflowStrength(lua_const->strengthoverflow);
	//attr->setStrength(wrole->strength());

	const PlayerAttribCsvDef* attrFood = GetDefManagerProxy()->getPlayerAttribCsvDef(PlayerAttributeType::Food);
	attr->setFoodMaxLevel(attrFood->MaxVal);
	attr->setFoodLevel(wrole->food());

	const PlayerAttribCsvDef* attrThirst = GetDefManagerProxy()->getPlayerAttribCsvDef(PlayerAttributeType::Thirst);
	attr->setBasicMaxThirst(attrThirst->MaxVal);
	attr->setMaxThirst(attrThirst->MaxVal);
	attr->setThirst(wrole->thirst());
	//attr->setThirst(wrole->thirst());

	attr->setOxygen(wrole->oxygen());
	attr->m_UsedStamina = wrole->usedstamina();
	attr->setExp(wrole->exp());
	attr->setStarDebuffTime(wrole->stardebufftime());
	attr->setStarDebuffStage(wrole->stardebuffstage());
	//LOG_INFO("ClientPlayer::loadFromFileReal(): uin = %d", uin);
	attr->toggleUseCompatibleStrength(wrole->useStrength());
	attr->setStrengthFoodShowState(wrole->strengthFoodShowState());
	if (wrole->computerOrderUsed())
	{
		for (size_t i = 0; i < wrole->computerOrderUsed()->size(); i++)//保存电脑指令使用次数
		{
			attr->addComputerOrderIsUsed(wrole->computerOrderUsed()->Get(i));
		}
	}

	m_DieTimes = wrole->deathTimes();

	if (version == 0)
	{
		attr->m_FoodLevel *= 5.0f;
		attr->m_FoodSatLevel *= 5.0f;
		attr->initHP(attr->getHP() * 5.0f);
	}

	if (!attr->m_Attribs.empty()) memset(&attr->m_Attribs[0], 0, attr->m_Attribs.size() * sizeof(AttribModified));
	for (size_t i = 0; i < wrole->buffs()->size(); i++)
	{
		auto b = wrole->buffs()->Get(i);
		attr->addBuffOnLoad(b->buffid(), b->bufflv(), b->ticks());
	}

	LoadPackContainer(getBackPack(), EQUIP_START_INDEX, wrole->equips());
	LoadPackContainer(getBackPack(), BACKPACK_START_INDEX, wrole->backpack());
	LoadPackContainer(getBackPack(), SHORTCUT_START_INDEX, wrole->shortcut());
	LoadPackContainer(getBackPack(), SHORTCUTEX_START_INDEX, wrole->shortcutEx());
	LoadPackContainer(getBackPack(), SHORTCUT_START_INDEX_EDIT, wrole->shortcutEdit());
	LoadPackContainer(getBackPack(), EXT_BACKPACK_START_INDEX, wrole->extbackpack());
	LoadPackContainer(getBackPack(), WITHHOLD_BACKPACK_START_INDEX, wrole->withhold_backpack());

	LockCtrlComponent* lockctrl = GetComponent<LockCtrlComponent>();
	lockctrl->SetLastPasswd(wrole->lastpasswd());

	if (wrole->craft_tasks())
	{
		for (size_t i = 0; i < wrole->craft_tasks()->size(); i++)
		{
			auto t = wrole->craft_tasks()->Get(i);
			getCraftingQueue()->loadAddTask(t->craftingId(), t->count(), t->ticksPerItem(), t->remainingTicks());
		}
	}

	onSetCurShortcutWithFB(wrole);

	// 拷贝高级创造老地图的快捷栏数据到新快捷栏, 71680 = 1.24.0
	// code by 2023.3.3 chenshaobin
	int gameVersionInt = GetClientInfoProxy()->GetGameVersionIntForClientInfo();
	if (g_WorldMgr && (g_WorldMgr->isUGCMode()) && gameVersionInt >= 71680 && version < 71680)
	{
		CopyShortcutForUGC(getBackPack());
	}

	//getAccountHorseComponent()->load(wrole);	
	if (wrole->horses() || wrole->curhorse() != 0)
	{
		auto comp = sureAccountHorseComponent();//by__Logo
		if (comp)
			comp->load(wrole);
	}

	//m_pSkillCDComp->load(wrole->skillcds());
	auto skillCDComp = m_SkillCDComponent;
	if (skillCDComp) skillCDComp->load(wrole->skillcds());
	if (wrole->skillCDExpandData() && wrole->skillCDExpandData()->size() > 0)
	{
		if (!getSkillComponent())
		{
			CreateComponent<SkillComponent>("SkillComponent");
		}
		if (getSkillComponent()) getSkillComponent()->loadCD(wrole->skillCDExpandData());
	}

	m_UnlockItems.clear();
	if (wrole->unlockitems())
	{
		if (wrole->unlockitems()->size() <= 2048) //?????????????????size???? ?????????size????????????????????
		{
			m_UnlockItems.resize(wrole->unlockitems()->size());
			for (size_t i = 0; i < wrole->unlockitems()->size(); i++)
			{
				int itemid = wrole->unlockitems()->Get(i);
				m_UnlockItems[i] = itemid;
			}
		}
	}

	if (wrole->worldtimes())
	{
		for (int i = 0; i < (int)wrole->worldtimes()->size(); i++)
		{
			m_WorldTimes[i] = wrole->worldtimes()->Get(i);
		}
	}

	auto landingpoints = wrole->landingpoints();
	if (landingpoints)
	{
		for (size_t i = 0; i < landingpoints->size(); i++)
		{
			m_LandingPoints[i] = Coord3ToWCoord(landingpoints->Get(i));
		}
	}

	getOpenContainerCom()->load(wrole->containerpasswords());

	auto vehicleComponent = GetComponent<ActorBindVehicle>();
	if (vehicleComponent)
	{
		if (wrole->vehiclecoord())
			vehicleComponent->Bind(wrole->vehicleobj(), Coord3ToWCoord(wrole->vehiclecoord()), false);
		else
			vehicleComponent->Bind(wrole->vehicleobj(), WCoord(0, 0, 0), false);
	}

	if (m_pTaskMgr)
		m_pTaskMgr->load(wrole);

	/*m_NpcShopInfo.clear();
	if (wrole->npcshopinfo()) {
		char filepath[256];
		sprintf(filepath, "%s/w%lld/mods/", rootpath.c_str(), owid);
		std::map<int, std::vector<int> > mNpcShopChangeInfo;
		GetDefManagerProxy()->getNpcShopChangeConfigStatus(filepath, mNpcShopChangeInfo);
		for (size_t i = 0; i < wrole->npcshopinfo()->size(); i++)
		{
			auto data = wrole->npcshopinfo()->Get(i);
			auto itShop = mNpcShopChangeInfo.find(data->shopid());
			if (itShop != mNpcShopChangeInfo.end()) {//reset this shop's sku
				if (itShop->second.size() == 0)
					continue;

				int j = 0;
				for (; j < itShop->second.size(); j++) {
					if (itShop->second[j] == data->skuid()) {
						break;
					}
				}

				if (j != itShop->second.size()) { continue; }
			}
			std::map<int, NpcShopInfo> mNpcShopInfo;
			mNpcShopInfo.clear();

			if (m_NpcShopInfo.find(data->shopid()) != m_NpcShopInfo.end()) {
				mNpcShopInfo = m_NpcShopInfo[data->shopid()];
			}

			NpcShopInfo skuinfo;
			skuinfo.iLeftCount = data->leftcount();
			skuinfo.iEndTime = data->endtime();

			mNpcShopInfo[data->skuid()] = skuinfo;
			m_NpcShopInfo[data->shopid()] = mNpcShopInfo;
		}

		if (mNpcShopChangeInfo.size() > 0) {
			mNpcShopChangeInfo.clear();
			GetDefManagerProxy()->setNpcShopChangeConfigStatus(filepath, mNpcShopChangeInfo);
		}
	} */

	//applyEquips();
	applyEquips(EQUIP_WEAPON);
	updateEquips();

	if (wrole->attr())
	{
		bool isModelvolume = attr->hasStatusEffect(STATUS_EFFECT_MODELVOLUME);
		if (wrole->attr()->modelscale() > 0 && !isModelvolume)
			setScale(wrole->attr()->modelscale());

		if (GetWorldManagerPtr() && GetWorldManagerPtr()->m_RuleMgr)
			GetWorldManagerPtr()->m_RuleMgr->setLockViewMode(wrole->attr()->lockviemode());

		if (wrole->attr()->customscale() > 0 && !isModelvolume)
			setCustomScale(wrole->attr()->customscale());
	}

	if (GetWorldManagerPtr() && GetWorldManagerPtr()->m_RuleMgr && GetWorldManagerPtr()->m_RuleMgr->getRuleOptionVal(GMRULE_SAVEMODE) != 1) {
		auto itemattlist = wrole->itemattaction();
		if (itemattlist && attr) {
			for (int i = 0; i < (int)itemattlist->size(); i++) {
				attr->addItemAttAction(itemattlist->Get(i));
			}
		}
	}

	//???????
	SetLevelMode(wrole->sumlevelexp(), wrole->curlevelexp(), wrole->curlevel());

	//m_pPetFollowListComp->load(wrole->tamedFollows());
	if (wrole->tamedFollows() && wrole->tamedFollows()->size() > 0)
	{
		auto petFollowListComp = surePetFollowListComponent();
		if (petFollowListComp) petFollowListComp->load(wrole->tamedFollows());
	}

	// ???????????ε???????????????????????????з??? codeby chenzihang
	attr->setHP(wrole->hp(), true);
	attr->setStrength(wrole->strength());

	//m_CarryingActor = wrole->carrying();  ????浵??????????????????????????
	bool _isAttrShapeShift = false;
	// 属性变身
	if (wrole->attrbuffs())
	{
		for (size_t i = 0; i < wrole->attrbuffs()->size(); i++)
		{
			auto b = wrole->attrbuffs()->Get(i);
			int iRealStatusId = GetDefManagerProxy()->getRealStatusId(b->buffid(), b->bufflv());
			auto def = GetDefManagerProxy()->getStatusDef(iRealStatusId);
			if (def)
			{
				for (int j = 0; j < MAX_BUFF_ATTRIBS; j++)
				{
					auto effDef = GetDefManagerProxy()->getBuffEffectDef(def->Status.EffInfo[j].CopyID);
					if (effDef && effDef->AttType == BUFFATTRT_ATTR_SHAPESHIFT)  //如果是属性变身
					{
						_isAttrShapeShift = true;
						attr->addBuffOnLoad(b->buffid(), b->bufflv(), b->ticks());
						break;
					}
				}
			}
		}
		if (_isAttrShapeShift)
		{
			for (size_t i = 0; i < wrole->attrbuffs()->size(); i++)
			{
				auto b = wrole->attrbuffs()->Get(i);
				attr->addBuffOnLoad(b->buffid(), b->bufflv(), b->ticks());
			}
			m_PlayerAttrib->initHP(wrole->attrLife());
		}
	}

	attr->setTemperature(wrole->temperature());

	LoadObjectChildAndComponent(wrole->scriptcomponent(), wrole->ugccomponents(), wrole->children());

	free(buf);
	return true;
}

bool ClientPlayer::loadFileByToggleGameMakerMode(long long owid, int uin, bool needload /* = true */, int specialType/* = NORMAL_WORLD*/)
{
	std::string rootpath = GetWorldRootBySpecialType(specialType);

	char path[256];
	sprintf(path, "%s/w%lld/roles/u%d.p", rootpath.c_str(), owid, uin);
	int buflen = 0;
	void* buf = ReadWholeFile(path, buflen);
	if (buf == NULL) return false;

	flatbuffers::Verifier verifier((const uint8_t*)buf, buflen);
	if (!FBSave::VerifyActorPlayerBuffer(verifier))
	{
		free(buf);
		return false;
	}

	const FBSave::ActorPlayer* wrole = FBSave::GetActorPlayer(buf);
	if (wrole == NULL)
	{
		free(buf);
		return false;
	}

	if (!needload)
	{
		//getAccountHorseComponent()->setCurAccountHorse(wrole->curhorse());
		if (wrole->curhorse() != 0)
		{
			auto comp = sureAccountHorseComponent();//by__Logo
			if (comp)
				comp->setCurAccountHorse(wrole->curhorse());
		}
		free(buf);
		return false;
	}

	SandboxContext context;
	context.SetData_Userdata("userdata", "wrole", const_cast<FBSave::ActorPlayer*> (wrole));
	Event().Emit("loadFileByToggleGameMakerMode", context);

	ConstAtLua* lua_const = GetLuaInterfaceProxy().get_lua_const();

	PlayerAttrib* attr = getPlayerAttrib();
	attr->m_FoodLevel = wrole->food();
	attr->m_FoodSatLevel = 0;

	attr->setBasicMaxHP(lua_const->hpmax);
	attr->setBasicOverflowHP(lua_const->hpoverflow);
	attr->setHP(wrole->hp(), true);

	//LOG_INFO("ClientPlayer::loadFileByToggleGameMakerMode(): wrole->strength() = %.2f", wrole->strength());
	attr->setBasicMaxStrength(lua_const->strengthmax);
	attr->setBasicOverflowStrength(lua_const->strengthoverflow);
	attr->setStrength(wrole->strength());

	//attr->m_Life = wrole->hp();
	attr->setOxygen(wrole->oxygen());
	attr->setExp(wrole->exp());
	attr->setStarDebuffTime(wrole->stardebufftime());
	attr->setStarDebuffStage(wrole->stardebuffstage());
	if (wrole->computerOrderUsed())
	{
		for (size_t i = 0; i < wrole->computerOrderUsed()->size(); i++)//保存电脑指令使用次数
		{
			attr->addComputerOrderIsUsed(wrole->computerOrderUsed()->Get(i));
		}
	}
	// 开发者编辑模式加载buff 没有实际意义
	//for (size_t i = 0; i < wrole->buffs()->size(); i++)
	//{
	//	auto b = wrole->buffs()->Get(i);
	//	attr->addBuffOnLoad(b->buffid(), b->bufflv(), b->ticks());
	//}

	LoadPackContainer(getBackPack(), EQUIP_START_INDEX, wrole->equips());
	LoadPackContainer(getBackPack(), BACKPACK_START_INDEX, wrole->backpack());
	LoadPackContainer(getBackPack(), SHORTCUT_START_INDEX, wrole->shortcut());
	LoadPackContainer(getBackPack(), SHORTCUTEX_START_INDEX, wrole->shortcutEx());
	LoadPackContainer(getBackPack(), SHORTCUT_START_INDEX_EDIT, wrole->shortcutEdit());
	onSetCurShortcut(getCurShortcut());

	//updateEquips();
	applyEquips();

	if (wrole->attr())
	{
		bool isModelvolume = attr->hasStatusEffect(STATUS_EFFECT_MODELVOLUME);
		if (wrole->attr()->modelscale() > 0 && !isModelvolume)
			setScale(wrole->attr()->modelscale());

		if (GetWorldManagerPtr() && GetWorldManagerPtr()->m_RuleMgr)
			GetWorldManagerPtr()->m_RuleMgr->setLockViewMode(wrole->attr()->lockviemode());

		if (wrole->attr()->customscale() > 0 && !isModelvolume)
			setCustomScale(wrole->attr()->customscale());
	}

	if (GetWorldManagerPtr() && GetWorldManagerPtr()->m_RuleMgr && GetWorldManagerPtr()->m_RuleMgr->getRuleOptionVal(GMRULE_SAVEMODE) != 1) {
		auto itemattlist = wrole->itemattaction();
		if (itemattlist && attr) {
			for (int i = 0; i < (int)itemattlist->size(); i++) {
				attr->addItemAttAction(itemattlist->Get(i));
			}
		}
	}

	//???????
	SetLevelMode(wrole->sumlevelexp(), wrole->curlevelexp(), wrole->curlevel());

	LockCtrlComponent* lockctrl = GetComponent<LockCtrlComponent>();
	lockctrl->SetLastPasswd(wrole->lastpasswd());

	free(buf);
	return true;
}

extern flatbuffers::Offset<flatbuffers::Vector<const FBSave::ActorBuff*>> SaveActorBuffs(SAVE_BUFFER_BUILDER& builder, LivingAttrib* attrib);
bool ClientPlayer::saveToFile(long long owid, ChunkIOMgr* iomgr)
{
#if defined(IWORLD_SERVER_BUILD) && !STUDIO_SERVER
	if (GetGameInfoProxy()->GetRoomHostType() == ROOM_SERVER_RENT)
	{
		//租赁服，需要写玩家存档文件
	}
	if (init_failed)
		return true;
#endif
	flatbuffers::FlatBufferBuilder builder;

	PlayerAttrib* attr = getPlayerAttrib();

	auto basedata = saveActorCommon(builder);
	auto buffs = SaveActorBuffs(builder, attr);
	std::vector<FBSave::ActorBuff>buffarray;
	float attrHp = attr->getHP();
	float attrStrength = attr->getStrength();
	if (isAttrShapeShift())
	{
		buffarray.reserve(attr->m_Buffs.size());
		for (size_t i = 0; i < attr->m_Buffs.size(); i++)
		{
			ActorBuff& b = attr->m_Buffs[i];
			buffarray.push_back(FBSave::ActorBuff(b.buffid, b.bufflv, b.ticks));
		}

		std::vector<FBSave::ActorBuff> oldbuffarray;
		oldbuffarray.reserve(attr->m_oldBuffs.size());
		for (size_t i = 0; i < attr->m_oldBuffs.size(); i++)
		{
			ActorBuff& b = attr->m_oldBuffs[i];
			oldbuffarray.push_back(FBSave::ActorBuff(b.buffid, b.bufflv, b.ticks));
		}

		buffs = builder.CreateVectorOfStructs(oldbuffarray);
		attrHp = attr->m_oldLife;
		attrStrength = attr->m_oldStrength;
	}
	auto attrbuffs = builder.CreateVectorOfStructs(buffarray);
	auto equips = SavePackContainer(builder, getBackPack(), EQUIP_START_INDEX);
	auto backpack = SavePackContainer(builder, getBackPack(), BACKPACK_START_INDEX);
	auto extbackpack = SavePackContainer(builder, getBackPack(), EXT_BACKPACK_START_INDEX);
	auto withhold_backpack = SavePackContainer(builder, getBackPack(), WITHHOLD_BACKPACK_START_INDEX);
	auto creaft_queue = getCraftingQueue();
	auto tasks = SaveCraftTasks(builder, creaft_queue);
#ifdef IWORLD_SERVER_BUILD
	std::vector<int> _pack_to_record;
	_pack_to_record.push_back(EQUIP_START_INDEX);
	_pack_to_record.push_back(BACKPACK_START_INDEX);
	_pack_to_record.push_back(EXT_BACKPACK_START_INDEX);
#endif

	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::ItemIndexGrid>>> shortcut = 0;
	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::ItemIndexGrid>>> shortcutEx = 0;
	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::ItemIndexGrid>>> shortcutEdit = 0;

	//家园编辑模式
	if (isHomeLandGameMakerMode())
	{
		shortcutEx = SavePackContainer(builder, getBackPack(), SHORTCUTEX_START_INDEX);
		shortcutEdit = SavePackContainer(builder, getBackPack(), SHORTCUT_START_INDEX_EDIT);
		if (m_pWorld)
			shortcut = SavePackContainerEx(builder, m_pWorld->getOWID(), SHORTCUT_START_INDEX);
#ifdef IWORLD_SERVER_BUILD
		_pack_to_record.push_back(SHORTCUTEX_START_INDEX);
#endif
	}
	else
	{
		shortcut = SavePackContainer(builder, getBackPack(), SHORTCUT_START_INDEX);
		shortcutEdit = SavePackContainer(builder, getBackPack(), SHORTCUT_START_INDEX_EDIT);
		if (m_pWorld)
			shortcutEx = SavePackContainerEx(builder, m_pWorld->getOWID(), SHORTCUTEX_START_INDEX);
#ifdef IWORLD_SERVER_BUILD
		_pack_to_record.push_back(SHORTCUT_START_INDEX);
#endif
	}

	auto comp = GetComponent<AccountHorseComponent>();
	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::AccountHorseData>>> horsesoffset = 0;
	if (comp)
	{
		horsesoffset = comp->save(builder);
	}

	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::SkillCDData>>> skillcdsoffset = 0;
	auto skillCDComp = m_SkillCDComponent;
	if (skillCDComp) skillcdsoffset = skillCDComp->save(builder);
	//技能拓展
	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::SkillCDExpandData>>> skillcdexpandsoffset = 0;
	auto skillComp = getSkillComponent();
	if (skillComp) skillcdexpandsoffset = skillComp->saveCD(builder);

	std::vector<int> worldtimes;
	for (int i = 0; i < MAX_MAP; i++)
	{
		worldtimes.push_back(m_WorldTimes[i]);
	}

	std::vector<FBSave::Coord3> landingpts;
	for (int i = 0; i < MAX_MAP; i++)
	{
		landingpts.push_back(WCoordToCoord3(m_LandingPoints[i]));
	}

	auto containerpasswords_offset = getOpenContainerCom()->save(builder);

	unsigned char uinencoded[16];
	int nchar = CalUinEncoded(uinencoded, (unsigned int)m_ObjId);

	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::TaskData>>> taskoffset = 0;
	if (m_pTaskMgr)
		taskoffset = m_pTaskMgr->save(builder);


	/*std::vector<flatbuffers::Offset<FBSave::NpcShopSkuData>> vNpcShop;
	vNpcShop.clear();
	for (auto it = m_NpcShopInfo.begin(); it != m_NpcShopInfo.end(); it++) {
		for (auto skuIt = it->second.begin(); skuIt != it->second.end(); skuIt++) {
			vNpcShop.push_back(FBSave::CreateNpcShopSkuData(builder, it->first, skuIt->first, skuIt->second.iLeftCount, skuIt->second.iEndTime));
		}
	}

	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::NpcShopSkuData>>> npcshop = 0;
	if (vNpcShop.size() > 0) {
		npcshop = builder.CreateVector(vNpcShop);
	}  */

	bool isModelvolume = attr->hasStatusEffect(STATUS_EFFECT_MODELVOLUME);
	float scale = 1;
	if (getBody() && !isModelvolume)
		scale = getBody()->getBodyScale();
	bool lockViewMode = false;
	if (GetWorldManagerPtr() && GetWorldManagerPtr()->m_RuleMgr)
		lockViewMode = GetWorldManagerPtr()->m_RuleMgr->isLockViewMode();
	float customscale = 1.0f;
	if (!isModelvolume)
		customscale = getCustomScale();

	auto playerAttr = FBSave::CreatePlayerAttr(builder, scale, lockViewMode, customscale);

	std::vector<int> vItemAttList;
	vItemAttList.clear();
	if (GetWorldManagerPtr() && GetWorldManagerPtr()->m_RuleMgr && GetWorldManagerPtr()->m_RuleMgr->getRuleOptionVal(GMRULE_SAVEMODE) != 1) {
		attr->getItemAttAction(vItemAttList);
	}
	auto vehicleComponent = GetComponent<ActorBindVehicle>();
	auto vehiclecoord = WCoordToCoord3(vehicleComponent ? vehicleComponent->getVehicleAttachPos() : WCoord(0, 0, 0));

	//???????
	int nGainedSumExp = 0;
	int nCurLevelExp = 0;
	int nCurLevel = 0;

	PlayerLevelMode* pLevelMode = this->getPlayerAttrib()->m_pLevelMode;
	if (pLevelMode)
	{
		nGainedSumExp = pLevelMode->GetSumExp();
		nCurLevelExp = pLevelMode->GetCurExp();
		nCurLevel = pLevelMode->GetCurLevel();
	}

	std::vector<int64_t> vTamedFollows;
	auto petFollowListComp = GetComponent<PetFollowListComponent>();
	if (petFollowListComp)
	{
		vTamedFollows = petFollowListComp->getList();
	}

	SandboxContext context;
	context.SetData_Userdata("userdata", "builder", &builder);
	SandboxResult result = Event().Emit("saveToFile", context);
	assert(result.IsExecSuccessed());

	FBSave::Coord3 revivePoint = result.GetData_UserObject<FBSave::Coord3>("revive");
	FBSave::Coord3 spawnPoint = result.GetData_UserObject<FBSave::Coord3>("spawn");
	FBSave::Coord3 triggerPoint = result.GetData_UserObject<FBSave::Coord3>("trigger");
	auto worldpointoffset = result.GetData_UserObject<flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::AccountWorldPoint>>>>("worldpointoffset"); //
	WORLD_ID m_CurAccountHorse = 0;
	if (comp)
		m_CurAccountHorse = comp->getCurAccountHorse();

	auto CarryComp = getCarryComponent();
	WORLD_ID CarringActorID = 0;
	if (CarryComp)
	{
		CarringActorID = CarryComp->getCarringActorID();
	}
	auto RidComp = getRiddenComponent();
	WORLD_ID RidingActorObjId = 0;
	if (RidComp)
	{
		RidingActorObjId = RidComp->getRidingActorObjId();
	}
	//护甲值
	float armor = getArmor();
	//毅力值
	float Perseverancs = getPerseverance();

	//last input passwd
	LockCtrlComponent *lockctrl = GetComponent<LockCtrlComponent>();
	int lastpasswd = lockctrl->GetLastPasswd();

	string scriptComponent = "";
	flatbuffers::Offset<flatbuffers::Vector<int8_t>> childrenOffset = 0;
	flatbuffers::Offset<flatbuffers::Vector<int8_t>> ugccomponentsOffset = 0;
	SaveObjectChildAndComponent(builder, scriptComponent, ugccomponentsOffset, childrenOffset);

	auto s = FBSave::CreateActorPlayer(builder,
		basedata,
		RidingActorObjId,
		m_LoginNum,
		m_CurMapID,
		&revivePoint,//&rp,//auto rp = WCoordToCoord3(m_RevivePoint);
		attrHp,
		attr->getFoodLevel(),
		attr->getThirst(),
		attr->getTemperature(),
		attr->getRadiation(),
		attr->getOxygen(),
		attr->m_UsedStamina,
		attr->getExp(),
		buffs,
		equips,
		backpack,
		shortcut,
		attr->m_CurShotcut,
		0,
		horsesoffset,
		m_CurAccountHorse,//getAccountHorseComponent()->getCurAccountHorse(),
		CommonUtil::GetInstance().GetGameVersionInt(),
		skillcdsoffset,
		builder.CreateVector(uinencoded, nchar),
		builder.CreateVector(m_UnlockItems),
		builder.CreateVector(worldtimes),
		builder.CreateVectorOfStructs(landingpts),
		containerpasswords_offset,
		taskoffset,
		&spawnPoint,//&spawnpoint, //auto spawnpoint = WCoordToCoord3(m_SpawnPoint);
		0,
		playerAttr,
		vItemAttList.size() == 0 ? 0 : builder.CreateVector(vItemAttList),
		&triggerPoint,//&triggerrp, //auto triggerrp = WCoordToCoord3(m_TriggerRevivePoint);
		vehicleComponent ? vehicleComponent->getVehicleID() : 0,
		&vehiclecoord,
		nGainedSumExp,
		nCurLevelExp,
		nCurLevel,
		CarringActorID,
		shortcutEx,
		attrStrength,//attr->getStrength(),
		builder.CreateVector(vTamedFollows),
		worldpointoffset, //Offset<Vector<Offset<FBSave::AccountWorldPoint>>> worldpointoffset = 0;//m_AccountWorldPoint
		attr->useCompatibleStrength(),
		armor,
		Perseverancs,
		shortcutEdit,
		attr->m_CurShotcutEdit,
		attrbuffs,
		attrHp,
		attr->strengthFoodShowState(),
		attr->getStarDebuffTime(),
		attr->getStarDebuffStage(),
		skillcdexpandsoffset,
		m_DieTimes,
		builder.CreateString(scriptComponent),
		ugccomponentsOffset,
		childrenOffset,
		builder.CreateVector(attr->getComputerOrderVec()),
		extbackpack,
		tasks, 
		withhold_backpack,
		lastpasswd
		);
	builder.Finish(s);

	//builder.Finish(actorPlayerBuilder.Finish());

	if (IsFlatBufferCompleteZero(builder.GetBufferPointer(), builder.GetSize())) return false;

#if defined(IWORLD_SERVER_BUILD) && !STUDIO_SERVER
	long long appid = GetClientInfoProxy()->GetServerAppID();
	long long wid = g_WorldMgr->getFromWorldID();
	if (appid > 0) {
		bool useAppID = Rainbow::GetICloudProxyPtr()->PlayerDataUseAppId();
		if (useAppID)
			wid = appid;
		// TODO: 未来玩家数据会迁移到kv存储中
		// g_zmqMgr->SavePlayerDataToKV(builder.GetBufferPointer(), builder.GetSize(), appId, getUin());
	}
	g_zmqMgr->SavePlayerDataToDataServer(builder.GetBufferPointer(), builder.GetSize(), wid, getUin());

	auto logtype = GetICloudProxyPtr()->GetItemLogType();
	if (getBackPack() && logtype >= 0)
	{
		BackPackStringRecorder _recorder(logtype);
		for (auto iter = _pack_to_record.begin(); iter != _pack_to_record.end(); ++iter)
		{
			PackContainer* pack = dynamic_cast<PackContainer*>(getBackPack()->getContainer(*iter));
			if (pack)
				pack->visitPack(&_recorder);
		}
		_recorder.end();
		const std::string& _pack_str = _recorder.m_Index2ItemRecord.str();
		if (m_LastSavePackString != _pack_str)
		{
			m_LastSavePackString = _pack_str;
			Rainbow::GetICloudProxyPtr()->packLog(getUin(), "pack_save", _pack_str, true);
		}
	}
	if (builder.GetSize() > 50000 && (++m_SaveSizeRecorded % 10 == 1))
	{
		jsonxx::Object obj;
		obj << "data_len" << builder.GetSize()
			<< "buff_count" << attr->m_Buffs.size()
			<< "old_buff_count" << attr->m_oldBuffs.size()
			<< "unlock_count" << m_UnlockItems.size()
			<< "item_att" << vItemAttList.size()
			<< "tame_follows" << vTamedFollows.size()
			<< "horses" << (comp ? comp->getAccountHorseCount() : 0)
			<< "SkillCD" << (skillCDComp ? skillCDComp->getSkillCDSize() : 0)
			<< "containerpw" << getOpenContainerCom()->getPasswordCount()
			<< "tasks" << (m_pTaskMgr ? m_pTaskMgr->getTaskNum() : 0)
			;
		Rainbow::GetICloudProxyPtr()->InfoLog(getUin(), 0, "player_data_size", obj);
	}
#else
	if (owid != 0)
	{
		std::string rootpath = GetWorldRootBySpecialType(iomgr->getSpecialType());

		char path[256];
		sprintf(path, "%s/w%lld/roles/u%d.p", rootpath.c_str(), owid, getUin());
		return GetFileManager().SaveToWritePath(path, builder.GetBufferPointer(), builder.GetSize());
	}
	else
	{
		assert(iomgr);
		if (iomgr) iomgr->pushCmd(CIOCMD_SAVEPLAYER, builder.GetBufferPointer(), builder.GetSize(), getUin());
		return true;
	}
#endif
	return true;
}

void ClientPlayer::addDirtyIndex(int index)
{
	if (nullptr != m_pWorld && !m_pWorld->isRemoteMode())
		mDirtyGridIndex.insert(index);
}

void ClientPlayer::setOperate(int op, int totalticks /* = 0 */, int opdata /* = 0 */, long long lopdata/* =0 */, long long tick /*=0*/)
{
	m_CurOperate = op;
	m_OperateTicks = 0;
	m_OperateTotalTicks = totalticks;
	m_OperateData = opdata;
	m_lOperrateData = lopdata;
	m_OperateStartTick = tick? tick:GetIClientGameManagerInterface()->getICurGame() ? GetIClientGameManagerInterface()->getICurGame()->getGameTick() : 0;
}


//ClientActor *ClientPlayer::spawnItem(int itemid, int num, int offset/* =0 */)
//{
//	BackPackGrid grid;
//	SetBackPackGrid(grid, itemid, num);
//
//	Rainbow::Vector3f dir = Yaw2FowardDir(getLocoMotion()->m_RotateYaw);
//
//	WCoord pos1 = getPosition()+WCoord(0, 30, 0);
//	WCoord pos2 = pos1 + WCoord(dir*(BLOCK_FSIZE*4.0f));
//
//	Rainbow::Vector3f dir1 = Yaw2FowardDir(getLocoMotion()->m_RotateYaw+90);
//	pos2 +=  WCoord(dir1*(BLOCK_FSIZE*offset));
//
//	ClientItem *item = ENG_NEW(ClientItem)(grid);
//	item->getLocoMotion()->gotoPosition(pos2, 0, 0);
//	getActorMgr()->spawnActor(item, true);
//	if(m_pWorld->isGodMode()) item->m_LiveTicks = 4800;
//
//	return item;
//}

int ClientPlayer::getPlaceDirToBlockWithWorld(World* pworld, const WCoord& blockpos)
{
	auto vehicleWorld = dynamic_cast<VehicleWorld*>(pworld);
	if (vehicleWorld)
	{
		auto worldPos = vehicleWorld->getActorVehicleAssemble()->getRealWorldPosWithPos(blockpos);
		return getPlaceDirToBlock(WCoord(CoordDivBlock(worldPos.x), CoordDivBlock(worldPos.y), CoordDivBlock(worldPos.z)));
	}
	return getPlaceDirToBlock(blockpos);
}

int ClientPlayer::getPlaceDirToBlock(const WCoord& blockpos)
{
	WCoord pos = BlockBottomCenter(blockpos);
	WCoord actorpos = getPosition();
	if (Rainbow::Abs(actorpos.x - pos.x) < BLOCK_SIZE && Rainbow::Abs(actorpos.z - pos.z) < BLOCK_SIZE)
	{
		int y = actorpos.y + 182 - getLocoMotion()->m_yOffset;
		if (y - pos.y > 2 * BLOCK_SIZE)
		{
			return DIR_POS_Y;
		}
		else if (pos.y - y > 0)
		{
			return DIR_NEG_Y;
		}
	}

	return getCurPlaceDir();
}

int ClientPlayer::getFreeStandPlaceDir()
{
	return int((getLocoMotion()->m_RotateYaw + 180.0f) * 16 / 360.0f + 0.5f) & 15;
}


bool ClientPlayer::writeLetters(int tgtGridIdx, std::string txt)
{
	auto backpack = getBackPack();
	auto grid = backpack->index2Grid(tgtGridIdx);

	if (grid == nullptr || grid->getItemID() != ITEM_LETTERS) return false;

	std::string skey = "";
	std::string reportContent = "";
	std::string curTitle = "";
	std::string curText = "";
	std::string oldTitle = "";
	std::string oldText = "";
	int curchangeTime = 0;
	int shouldAnonuminty = 0;
	int fromDriftBottle = 0;
	std::string serviceID;
	if (!grid->userdata_str.empty())
	{
		jsonxx::Object oldTxtObj;
		if (oldTxtObj.parse(grid->userdata_str))
		{
			if (oldTxtObj.has<jsonxx::String>("key"))
			{
				skey = oldTxtObj.get<jsonxx::String>("key");
			}
			if (oldTxtObj.has<jsonxx::String>("title"))
			{
				curTitle = oldTxtObj.get<jsonxx::String>("title");
			}
			if (oldTxtObj.has<jsonxx::String>("context"))
			{
				curText = oldTxtObj.get<jsonxx::String>("context");
			}

			if (oldTxtObj.has<jsonxx::Number>("changetime"))
			{
				curchangeTime = oldTxtObj.get<jsonxx::Number>("changetime");
			}
			if (oldTxtObj.has<jsonxx::String>("oldtitle"))
			{
				oldTitle = oldTxtObj.get<jsonxx::String>("oldtitle");
			}
			if (oldTxtObj.has<jsonxx::String>("oldcontext"))
			{
				oldText = oldTxtObj.get<jsonxx::String>("oldcontext");
			}
			if (oldTxtObj.has<jsonxx::Number>("shouldAnonymity"))
			{
				shouldAnonuminty = oldTxtObj.get<jsonxx::Number>("shouldAnonymity");
			}
			if (oldTxtObj.has<jsonxx::Number>("fromDriftBottle"))
			{
				fromDriftBottle = oldTxtObj.get<jsonxx::Number>("fromDriftBottle");
			}
			if (oldTxtObj.has<jsonxx::String>("serviceID"))
			{
				serviceID = oldTxtObj.get<jsonxx::String>("serviceID");
			}
		}
	}

	if (skey.empty())
	{
		char ckey[64] = { 0 };
#if defined(_WIN32)
		sprintf(ckey, "%d%I64d", getUin(), time(NULL));
#else
		sprintf(ckey, "%d%ld", getUin(), time(NULL));
#endif
		skey = ckey;
	}


	jsonxx::Object lettersObj;
	lettersObj << "uin" << getUin();
	lettersObj << "authorname" << m_Nickname; // getNickname();
	lettersObj << "shouldAnonymity" << shouldAnonuminty;
	lettersObj << "fromDriftBottle" << fromDriftBottle;
	lettersObj << "serviceID" << fromDriftBottle;

	std::string title = "";
	std::string context = "";

	jsonxx::Object txtObj;
	if (txtObj.parse(txt))
	{
		//nType:???????ntype???, ????????汾.
		int nType = 0;
		if (txtObj.has<jsonxx::Number>("ntype"))
		{
			nType = (int)txtObj.get<jsonxx::Number>("ntype");
		}

		if (nType == 1)
		{
			//???????
			lettersObj << "ntype" << nType;
			int lang = GetClientInfoProxy()->getArchiveLang();		//???????????

			if (txtObj.has<jsonxx::Object>("title"))
			{
				jsonxx::Object titleObj = txtObj.get<jsonxx::Object>("title");
				title = titleObj.json();

				reportContent = GetClientInfoProxy()->parseTextFromLanguageJson(title, lang) + ";";
			}

			if (txtObj.has<jsonxx::Object>("context"))
			{
				jsonxx::Object contextObj = txtObj.get<jsonxx::Object>("context");
				context = contextObj.json();

				reportContent += GetClientInfoProxy()->parseTextFromLanguageJson(context, lang);
			}


			//parseTextFromLanguageJson
		}
		else
		{
			//???
			if (txtObj.has<jsonxx::String>("title"))
			{
				title = txtObj.get<jsonxx::String>("title");
				reportContent = title + ";";
			}
			if (txtObj.has<jsonxx::String>("context"))
			{
				context = txtObj.get<jsonxx::String>("context");
				reportContent += context;
			}
		}
		if (txtObj.has<jsonxx::Number>("shouldAnonymity"))
		{
			shouldAnonuminty = txtObj.get<jsonxx::Number>("shouldAnonymity");
		}
		else
		{
			shouldAnonuminty = 0;
		}
		if (txtObj.has<jsonxx::String>("serviceID"))
		{
			serviceID = txtObj.get<jsonxx::String>("serviceID");
		}
	}
	else
	{
		string temp("");
		int index = 0;
		for (size_t i = 0; i < txt.size(); i++)
		{
			if (txt[i] == '|')
			{
				if (index == 0)
					title = temp;
				temp = "";
				index++;
			}
			else
				temp += txt[i];
		}
		context = temp;
	}

	lettersObj << "title" << title;
	lettersObj << "context" << context;
	lettersObj << "key" << skey;
	lettersObj << "shouldAnonymity" << shouldAnonuminty;
	lettersObj << "fromDriftBottle" << fromDriftBottle;
	lettersObj << "serviceID" << fromDriftBottle;

	if (!grid->userdata_str.empty())
	{
		int uin_int = 0;
		jsonxx::Object gridObj;
		if (gridObj.parse(grid->userdata_str))
		{
			if (gridObj.has<jsonxx::Number>("uin"))
			{
				uin_int = (int)gridObj.get<jsonxx::Number>("uin");
			}
		}

		// 不是作者本人，不能修改
		if (getUin() != uin_int)
		{
			return false;
		}
		//来自漂流瓶的不能修改
		if (fromDriftBottle == 1)
		{
			return false;
		}
		// 内容没有修改
		if (lettersObj.json() == grid->userdata_str)
		{
			return false;
		}
	}

	// 保存修改内容
	if (txt.empty())
	{
		grid->userdata_str = "";
	}
	else
	{
		if (curchangeTime > 0)
		{
			int examineTime = 0;
			MINIW::ScriptVM::game()->callFunction("GetMapExamineTime", ">i", &examineTime);
			if (curchangeTime < examineTime)
			{
				oldTitle = curTitle;
				oldText = curText;
			}

		}

		//写入文字保存时间
		int serverTime;
		MINIW::ScriptVM::game()->callFunction("GetServerCurrentTime", ">i", &serverTime);
		if (oldTitle != "")
		{
			lettersObj << "oldtitle" << oldTitle;
		}
		if (oldText != "")
		{
			lettersObj << "oldcontext" << oldText;
		}

		lettersObj << "changetime" << serverTime;
		grid->userdata_str = lettersObj.json();
	}

	if (hasUIControl())
	{
		//ge GetGameEventQue().postBackpackChange(tgtGridIdx);
		MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
			SetData_Number("grid_index", tgtGridIdx);
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_BACKPACK_CHANGE", sandboxContext);
		}
	}

	//刷新手上信纸, 这里只需主机刷新, 客机不用再这里刷新, 会在回调协议中刷新, 不然就会出现闪烁的现象(刷了两次)
	//if (!m_pWorld->isRemoteMode())
	//	onSetCurShortcut(getPlayerAttrib()->m_CurShotcut);

	GetWorldStringManagerProxy()->insert(skey, reportContent, SAVEFILETYPE::LETTER);
	//SandboxEventDispatcherManager::GetGlobalInstance().Emit("WorldStringManager_insert", SandboxContext(nullptr)
	//	.SetData_Number("type", 8/*SAVEFILETYPE::LETTER*/)
	//	.SetData_String("content", reportContent)
	//	.SetData_String("key", skey));
	return true;
}

void ClientPlayer::setUserDataStr(int tgtGridIdx, std::string txt)
{
	auto backpack = getBackPack();
	auto grid = backpack->index2Grid(tgtGridIdx);

	if (grid)
	{
		grid->userdata_str = txt;
		if (hasUIControl())
		{
			//ge GetGameEventQue().postBackpackChange(tgtGridIdx);
			MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
				SetData_Number("grid_index", tgtGridIdx);
			if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
				MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_BACKPACK_CHANGE", sandboxContext);
			}
		}
	}
}

bool ClientPlayer::writeBlueprint(int tgtGridIdx, std::string txt)
{
	std::ostringstream ostr;
	auto backpack = getBackPack();
	auto grid = backpack->index2Grid(tgtGridIdx);

	if (grid == nullptr || grid->getItemID() != ITEM_BLUEPRINT) return false;

	ostr << getUin() << "|" << getNickname() << "|" << txt;

	if (!grid->userdata_str.empty())
	{
		std::string uin_str;

		// ???????????????????
		uin_str = grid->userdata_str.substr(0, grid->userdata_str.find('|'));
		if (getUin() != atoi(uin_str.c_str()))
		{
			return false;
		}

		// ??????????
		if (ostr.str() == grid->userdata_str)
		{
			return false;
		}
	}


	// ???????????
	if (txt.empty())
	{
		grid->userdata_str = "";
	}
	else
	{
		grid->userdata_str = ostr.str();
	}

	if (hasUIControl())
	{
		//ge GetGameEventQue().postBackpackChange(tgtGridIdx);
		MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
			SetData_Number("grid_index", tgtGridIdx);
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_BACKPACK_CHANGE", sandboxContext);
		}
	}

	return true;
}

bool ClientPlayer::writeInstruction(int tgtGridIdx, std::string txt)
{
	//std::ostringstream ostr;
	auto backpack = getBackPack();
	auto grid = backpack->index2Grid(tgtGridIdx);

	if (grid == nullptr || grid->getItemID() != ITEM_INSTRUCTION) return false;

	bool isJson = false;
	std::string targetStr;
	jsonxx::Object instructionObj;
	if (!instructionObj.parse(txt))
	{
		std::string title = "";
		std::string value = "";
		std::string itable = "";
		std::string stable = "";
		string temp("");
		int index = 0;
		for (size_t i = 0; i < txt.size(); i++)
		{
			if (txt[i] == '|')
			{
				if (index == 0)
					title = temp;
				else if (index == 1)
					value = temp;
				else if (index == 2)
					itable = temp;
				temp = "";
				index++;
			}
			else
				temp += txt[i];
		}
		stable = temp;

		instructionObj << "title" << title;
		instructionObj << "value" << value;
		instructionObj << "itable" << itable;
		instructionObj << "stable" << stable;

		targetStr = title;
	}

	if (!grid->userdata_str.empty())
	{
		std::string uin_str;

		// ??????????
		if (instructionObj.json() == grid->userdata_str)
		{
			return false;
		}
	}

	// �����޸�����
	if (txt.empty())
	{
		grid->userdata_str = "";
	}
	else
	{
		grid->userdata_str = instructionObj.json();
		if (instructionObj.has<std::string>("title"))
		{
			targetStr = instructionObj.get<jsonxx::String>("title");
		}
		if (!targetStr.empty() && m_pWorld && !m_pWorld->isRemoteMode())
		{
			char ckey[64] = { 0 };
			sprintf(ckey, "%d%d", getUin(), grid->getIndex());

			GetWorldStringManagerProxy()->insert(ckey, targetStr, SAVEFILETYPE::WSB_INSTRUCTION_CHIP);
			//SandboxEventDispatcherManager::GetGlobalInstance().Emit("WorldStringManager_insert", SandboxContext(nullptr)
			//	.SetData_Number("type", 9/*SAVEFILETYPE::WSB_INSTRUCTION_CHIP*/)
			//	.SetData_String("content", targetStr)
			//	.SetData_String("key", ckey));
		}
	}

	if (hasUIControl())
	{
		//ge GetGameEventQue().postBackpackChange(tgtGridIdx);
		MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
			SetData_Number("grid_index", tgtGridIdx);
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_BACKPACK_CHANGE", sandboxContext);
		}
	}

	return true;
}


int ClientPlayer::selectAllCacheMobsNum(int iMobDefID, int grouptype, int range)
{
	CollideAABB box;
	getLocoMotion()->getCollideBox(box);
	box.expand(range, range / 2, range);

	actorsfind.clear();

	std::vector<IClientActor*>actors;
	m_pWorld->getActorsOfTypeInBox(actors, box, OBJ_TYPE_MONSTER);

	float fDist = 99999999.0f;
	ClientMob* pTarget = NULL;
	WCoord pos = getPosition();

	for (size_t i = 0; i < actors.size(); i++)
	{
		ClientMob* mob = dynamic_cast<ClientMob*>(actors[i]);

		if ((mob->getDef()->ID == iMobDefID) || (grouptype && mob->getDef()->BabyID == iMobDefID) || iMobDefID == 0)
		{
			WCoord vec = mob->getLocoMotion()->getPosition() - pos;
			float dist = vec.length();
			if (dist < range)
			{
				actorsfind.push_back(mob);
			}
		}
	}

	return actorsfind.size();
}

ClientMob* ClientPlayer::getCacheMob(int index)
{
	if (index < (int)actorsfind.size())
	{
		return actorsfind[index];
	}
	return NULL;
}

int ClientPlayer::composePlayerIndex(int modelid, int geniuslv/* =0 */, int skinid/* =0 */)
{
	if (modelid <= 0 || modelid >= 16) modelid = 1;
	if (geniuslv < 0 || geniuslv >= 16) geniuslv = 0;
	//if (skinid < 0 || skinid >= 256) skinid = 0;
	if (skinid < 0 || (skinid >> 16)>0) skinid = 0;

	return modelid | (geniuslv << 4) | (skinid << 8);
}

void ClientPlayer::setGunInfo(float spread, float jaw, float pitch, Rainbow::Vector3f pos)
{
	if (m_pGunComponent != NULL)
		m_pGunComponent->setGunInfo(spread, jaw, pitch, pos);
}

GunUseComponent* ClientPlayer::getGunLogical()
{
	return getGunComponent();
}

void ClientPlayer::resetCheckClientInputVariable()
{
	m_nGuardTick = Timer::getSystemTick();
	m_nPosYPos = 0;
	m_nPosYNeg = 0;
	m_nPosXZ = 0;
	m_bPreOnGround = true;
	m_bOffGrounding = false;
	m_nReciveCount = 0;
}

bool ClientPlayer::checkClientInputMotion(const WCoord& dp, bool client_onground)
{
	return true;

	if (isInSpectatorMode())
		return true;
	PlayerLocoMotion* locmove = static_cast<PlayerLocoMotion*>(getLocoMotion());
	if (m_nGuardTick == 0)
	{
		m_nGuardTick = Timer::getSystemTick();
		m_bPreOnGround = client_onground;
		m_bOffGrounding = !m_bPreOnGround;
	}
	int ticknow = Timer::getSystemTick();

	if (m_nGuardSafeTick > 0)
	{
		m_nGuardSafeTick = m_nGuardSafeTick - (ticknow - m_nGuardTick);
		m_nGuardTick = ticknow;
		return true;
	}

	if (GetWorldManagerPtr()->isGameMakerRunMode() && (GetWorldManagerPtr()->m_RuleMgr->getGameStage() == CGAME_STAGE_PREPARE))
	{
		m_nGuardTick = ticknow;
		return true;
	}
	//??????
	if (m_bGuardError)
		return false;
	if (ticknow - m_nGuardTick > 3000)
	{
		m_nQuickSpeedCount = 0;
		float diff = ((float)(ticknow - m_nGuardTick)) / 1000.0f;
		if (!GetWorldManagerPtr()->isGodMode())
		{
			auto functionWrapper = getFuncWrapper();
			if (!(functionWrapper && functionWrapper->getJetpackFlying()) && (!(getCurDorsumID() == ITEM_FIRE_ROCKET)) && ((m_nPosYPos / diff) > 1000.0f))
			{
				LOG_INFO("checkClientInputMotion: check failed 1");
				m_bGuardError = true;
				return false;
			}

			if ((m_nPosXZ / diff) > 1000.0f && (!(getCurDorsumID() == ITEM_FIRE_ROCKET)))
			{
				LOG_INFO("checkClientInputMotion: check failed 2");
				m_bGuardError = true;
				return false;
			}
		}
		m_nGuardTick = ticknow;

		auto functionWrapper = getFuncWrapper();
		float new_my = locmove->calGravityMotionY(0);
		if (!GetWorldManagerPtr()->isGodMode() && m_bOffGrounding && !m_bPreOnGround && !client_onground
			&& !(locmove->m_InWater || locmove->m_InLava || locmove->isOnLadder() || (functionWrapper && functionWrapper->getJetpackFlying()) || (getCurDorsumID() == ITEM_FIRE_ROCKET))
			&& (abs(new_my) > 2) && m_nReciveCount >= 2)
		{
			IGameMode* gmaker = GetWorldManagerPtr()->m_RuleMgr;
			if (abs(m_nPosYPos) < 5 && m_nPosYNeg < 5
				&& (!(GetWorldManagerPtr()->isGameMakerRunMode() && (gmaker->getGameStage() == CGAME_STAGE_PREPARE))))
			{
				LOG_INFO("checkClientInputMotion: check failed 3");
				m_bGuardError = true;
				return false;
			}
		}

		m_nPosYPos = 0;
		m_nPosYNeg = 0;
		m_nPosXZ = 0;
		m_nReciveCount = 0;

		m_bPreOnGround = client_onground;
		m_bOffGrounding = !m_bPreOnGround;
	}
	else
	{

		float scale = 1.0f;
		bool ridingcar = false;

		auto RidComp = getRiddenComponent();
		auto funcWrapper = getFuncWrapper();
		if (RidComp && RidComp->isRiding())
		{
			ClientActor* riding = RidComp->getRidingActor();
			funcWrapper = riding->getFuncWrapper();
			if (riding && riding->getObjType() == OBJ_TYPE_MINECART)
			{
				ridingcar = true;
				scale = 100.0f;
			}
			else if (riding)
			{
				scale = (funcWrapper ? funcWrapper->getAIMoveSpeed() : 200) / 10.0f;
			}
		}
		else
		{
			scale = (funcWrapper ? funcWrapper->getAIMoveSpeed() : 200) / 10.0f;
		}
		if (scale < 1.0f)
			scale = 1.0f;

		float new_my = locmove->calGravityMotionY(0);
		ActorLiving* living = dynamic_cast<ActorLiving*>(this);
		if (dp.y > 0)
		{
			auto functionWrapper = getFuncWrapper();
			if (!(functionWrapper && functionWrapper->getJetpackFlying())
				&& (!(getCurDorsumID() == ITEM_FIRE_ROCKET))
				&& (!(living != nullptr && living->getLivingAttrib()->hasBuff(BOUND_BUFF)))
				&& (!ridingcar))
			{
				if (new_my < 0)
				{
					if (dp.y > 200.0f && m_nQuickSpeedCount < 3)
					{
						m_nPosYPos += 9.0f;
						m_nQuickSpeedCount++;
					}
					else
						m_nPosYPos += dp.y;
				}
				else if (dp.y > 2.0f)
				{
					m_nPosYPos += 2.0f;
				}
				else
				{
					m_nPosYPos += dp.y;
				}
			}
			else
			{
				if (dp.y > 5.0f)
					m_nPosYPos += 5.0f;
				else
					m_nPosYPos += dp.y;
			}
		}
		else
			m_nPosYNeg += abs(dp.y);

		if (client_onground)
		{
			int blockid = m_pWorld->getBlockID(DownCoord(CoordDivBlock(getPosition())));
			if (blockid) //&& (GetDefManagerProxy()->getBlockDef(blockid)->Slipperiness > 1.001f))
			{
				int blockdata = m_pWorld->getBlockData(DownCoord(CoordDivBlock(getPosition())));
				auto mtl = g_BlockMtlMgr.getMaterial(blockid);
				if (mtl && mtl->getBlockSlipperiness(blockdata) > 1.001f)
				{
					scale = 100.0f;
				}
			}
		}

		float dplen = Sqrt((float)((dp.x * dp.x) + (dp.z * dp.z)));
		if ((((dplen / scale) > 9.0f) && (dplen > 200.0f)) && m_nQuickSpeedCount < 3) //����3�γ����ٵĻ���,���������������
		{
			dplen = 9.0f;
			m_nQuickSpeedCount++;
		}

		auto functionWrapper = getFuncWrapper();
		if ((functionWrapper && functionWrapper->getJetpackFlying()) || (!client_onground))
			m_nPosXZ += dplen / 1.50f / scale;
		else
			m_nPosXZ += dplen / scale;

		if (m_bOffGrounding && (m_bPreOnGround == client_onground) && (abs(new_my) > 2))
			m_bOffGrounding = true;
		else
			m_bOffGrounding = false;
		m_nReciveCount++;
	}

	return true;
}

void ClientPlayer::setSelectedColor(int color)
{
	if (color != m_SelectedColor)
	{
		m_SelectedColor = color;

		// ?????????????????????
		if (m_pWorld->isRemoteMode())
		{
			PB_SetInfoCH setInfoCH;
			setInfoCH.set_color(m_SelectedColor);

			GetGameNetManagerPtr()->sendToHost(PB_SYNC_SETINFO_CH, setInfoCH);
		}
	}
}

bool ClientPlayer::isCrabClamp()
{
	auto actor = getActorMgr()->findActorByWID(m_clampId);
	if (actor)
	{
		return true;
	}
	return false;
}
bool ClientPlayer::isRidingByHippocompus()
{
	auto RidComp = getRiddenComponent();
	if (RidComp)
	{
		auto* ride = RidComp->getRidingActor();
		ActorHorse* riding = dynamic_cast<ActorHorse*>(ride);
		return (riding && riding->getDefID() == 3625);//海马坐骑状态
	}
	return false;
}
int ClientPlayer::addCrabClick()
{
	m_crabClickCount += 1;

	if (m_crabClickCount > GenRandomInt(3, GetLuaInterfaceProxy().get_lua_const()->clamp_click_max))
	{

		if (m_pWorld->isRemoteMode())
		{
			jsonxx::Object context;
			context << "uin" << g_pPlayerCtrl->getUin();
			SandBoxManager::getSingleton().sendToHost("PB_CRAB_CLICKCOUNT_RESET", context.bin(), context.binLen());
		}
		else
		{
			auto actor = getActorMgr()->findActorByWID(m_clampId);
			ActorCrab* crab = dynamic_cast<ActorCrab*>(actor);
			if (crab)
			{
				crab->setRidingActor(nullptr);
			}
			setCrabClamp(0);
			m_crabClickCount = 0;
		}
	}
	return m_crabClickCount;
}
void ClientPlayer::moveItemInner(int fromindex, int toindex, int num)
{
	if (num <= 0) getBackPack()->shiftMoveItem(fromindex, toindex);
	else getBackPack()->moveItem(fromindex, toindex, num);
}


ClientPlayer* ClientPlayer::getToSpectatorPlayer()
{
	if (m_nToSpectatorUin)
	{
		ClientPlayer* player = m_pWorld->getActorMgr()->ToCastMgr()->findPlayerByUin(m_nToSpectatorUin);
		if (player && player->isInSpectatorMode())
			return NULL;
		else
			return player;
	}
	return NULL;
}

void ClientPlayer::setMainPlayerAttrib()
{
	if (this == g_pPlayerCtrl)
	{
		ClientPlayer* target = getToSpectatorPlayer();
		if (m_nSpectatortype == SPECTATOR_TYPE_FOLLW && target)
		{
			MINIW::ScriptVM* scriptvm = MINIW::ScriptVM::game();
			scriptvm->setUserTypePointer("MainPlayerAttrib", "PlayerAttrib", target->getAttrib());
		}
		else
		{
			MINIW::ScriptVM* scriptvm = MINIW::ScriptVM::game();
			scriptvm->setUserTypePointer("MainPlayerAttrib", "PlayerAttrib", getAttrib());
		}
		//GetGameEventQue().postPlayerAttrChange();
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYERATTR_CHANGE", MNSandbox::SandboxContext(nullptr));
	}
}

void ClientPlayer::setSpectatorType(PLAYER_SPECTATOR_TYPE specttype)
{
	m_nSpectatortype = specttype;
	setMainPlayerAttrib();
}

bool ClientPlayer::canShowShotTip()
{
	if (this == g_pPlayerCtrl
		|| (g_pPlayerCtrl && g_pPlayerCtrl->isInSpectatorMode() && (g_pPlayerCtrl->getSpectatorType() == SPECTATOR_TYPE_FOLLW) && g_pPlayerCtrl->getToSpectatorPlayerUin() == getUin()))
	{
		return true;
	}
	else
	{
		return false;
	}
}

int ClientPlayer::doPick(Rainbow::Vector3f& currentEyePos, Rainbow::Vector3f& currentDir)
{
	MINIW::WorldRay ray;
	ray.m_Origin = WCoord(currentEyePos).toWorldPos();
	ray.m_Dir = currentDir;

	float range = GetWorldManagerPtr()->isGodMode() ? 500.0f : 400.0f; //ray.m_Range = 500.0f;
	const ItemSkillDef* skilldef = getCurItemSkillDef();

	if (skilldef && skilldef->Distance > 0.0001f)
	{
		range = skilldef->Distance;
	}

	if (getOPWay() == PLAYEROP_WAY_FOOTBALLER) range = GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.catch_ball_range;;

	//???????????λ????????λ?t????ж?????????????????????????ж????? by Jeff
	if (skilldef && skilldef->ID == 327)
	{
		float extraRange = Distance(currentEyePos, getPosition().toVector3());
		if (extraRange >= 250.0f) //???????????
		{
			range += extraRange;
		}
	}

	ray.m_Range = range;

	m_PickResult.liquids.resize(0);
	m_PickResult.intersect_actor = m_PickResult.intersect_block = m_PickResult.isIntersectLiquid = false;

	ActorExcludes excludes;
	excludes.addActorWithRiding(this);

	return (int)m_pWorld->pickAll(ray, &m_PickResult, excludes);
}

std::vector<WCoord> ClientPlayer::doPickBlockByItemSkill(int skillid, Rainbow::Vector3f& currentEyePos, Rainbow::Vector3f& currentDir)
{
	std::vector<WCoord> wCoordVec;
	const ItemSkillDef* skilldef = GetDefManagerProxy()->getItemSkillDef(skillid);
	if (skilldef)
	{
		if (skilldef->RangeType == 0)//????
		{
			if (skilldef->SkillType == 0)//?????
			{
				if (doPick(currentEyePos, currentDir) == 1
					&& (skilldef->Distance < 0.001 || skilldef->Distance > getDistanceToPos(m_PickResult.block.x * BLOCK_SIZE, m_PickResult.block.y * BLOCK_SIZE, m_PickResult.block.z * BLOCK_SIZE)))
				{
					wCoordVec.push_back(m_PickResult.block);
					WCoord placepos = NeighborCoord(m_PickResult.block, m_PickResult.face);
					wCoordVec.push_back(placepos);
				}
			}
			else
			{
				WCoord wcoord = CoordDivBlock(getPosition());
				//wcoord.y--;
				wCoordVec.push_back(wcoord);
			}
		}
		else if (skilldef->RangeType == 1)//????	
		{
			//???????ж??????
			bool sameHight = false;
			//???λ??
			WCoord coord;
			if (skilldef->SkillType == 0)//?????
			{
				if (doPick(currentEyePos, currentDir) == 1
					&& (skilldef->Distance < 0.001 || skilldef->Distance > getDistanceToPos(m_PickResult.block.x * BLOCK_SIZE, m_PickResult.block.y * BLOCK_SIZE, m_PickResult.block.z * BLOCK_SIZE)))
				{
					coord = m_PickResult.block;

					if ((CoordDivBlock(getPosition()).y - 1) == coord.y)
					{
						sameHight = true;
					}
				}
				else
				{
					return wCoordVec;
				}
			}
			else
			{
				coord = CoordDivBlock(getPosition());
			}

			//???????
			Rainbow::Vector3f origin = currentEyePos;
			Rainbow::Vector3f dir = currentDir;

			dir = MINIW::Normalize(dir);
			Rainbow::Vector3f dir2 = CrossProduct(Rainbow::Vector3f(0, 1.0f, 0), dir);
			dir2 = MINIW::Normalize(dir2);
			Rainbow::Vector3f dir3 = CrossProduct(dir2, dir);
			dir3 = MINIW::Normalize(dir3);

			float length = skilldef->RangeVal1;
			float width = skilldef->RangeVal2 / 2.0f;
			for (float x = 80.0f; x <= length; x += BLOCK_SIZE / 2.0f)
			{
				for (float y = -width; y <= width; y += BLOCK_SIZE / 2.0f)
				{
					for (float z = -width; z <= width; z += BLOCK_SIZE / 2.0f)
					{
						Rainbow::Vector3f dp = dir2 * y + dir3 * z;
						if (dp.LengthSqr() < width * width)
						{
							Rainbow::Vector3f blockPosV3 = origin + dir * x + dp;
							WCoord blockpos(CoordDivBlock(WCoord((int)blockPosV3.x, (int)blockPosV3.y, (int)blockPosV3.z)));

							int blockid = getWorld()->getBlockID(blockpos);
							const BlockDef* def = GetDefManagerProxy()->getBlockDef(blockid);
							if (blockid == 0 ||
								(def && (skilldef->TargetClass == 0 || skilldef->TargetClass == def->MineTool)))
							{
								wCoordVec.push_back(blockpos);
							}
						}
					}

				}
			}
		}
		else  //????
		{
			float length = skilldef->RangeVal1 / 2.0f;
			float width = skilldef->RangeVal2 / 2.0f;
			float height = skilldef->RangeVal3 / 2.0f;
			//???λ??
			WCoord coord;
			if (skilldef->SkillType == 0)//?????
			{
				if (doPick(currentEyePos, currentDir) == 1
					&& (skilldef->Distance < 0.001 || (skilldef->Distance < 0.001 || skilldef->Distance > getDistanceToPos(m_PickResult.block.x * BLOCK_SIZE, m_PickResult.block.y * BLOCK_SIZE, m_PickResult.block.z * BLOCK_SIZE))))
				{
					coord = m_PickResult.block * BLOCK_SIZE;
				}
				else
				{
					return wCoordVec;
				}
			}
			else
			{
				coord = getPosition();
			}

			//?????巽??
			Rainbow::Vector3f origin = currentEyePos;
			Rainbow::Vector3f dir = currentDir;

			//?????????x,y,z????
			float rotateYaw;
			float rotationPitch;
			Direction2PitchYaw(&rotateYaw, &rotationPitch, dir);
			Quaternionf rotation = AngleEulerToQuaternionf(Vector3f(rotationPitch, rotateYaw, 0));
			//rotation.setEulerAngle(rotateYaw, rotationPitch, 0);


			dir = rotation.GetAxisZ();
			Rainbow::Vector3f dir2 = rotation.GetAxisY();
			Rainbow::Vector3f dir3 = rotation.GetAxisX();
			for (float x = -length; x <= length; x += BLOCK_SIZE / 2.0f)
			{
				for (float y = -height; y <= height; y += BLOCK_SIZE / 2.0f)
				{
					for (float z = -width; z <= width; z += BLOCK_SIZE / 2.0f)
					{
						Rainbow::Vector3f blockPosV3 = origin + dir * x + dir2 * y + dir3 * z;
						WCoord blockpos(CoordDivBlock(WCoord((int)blockPosV3.x, (int)blockPosV3.y, (int)blockPosV3.z)));

						//?ж??????????????
						int blockid = getWorld()->getBlockID(blockpos);
						const BlockDef* def = GetDefManagerProxy()->getBlockDef(blockid);
						if (blockid == 0 ||
							(def && (skilldef->TargetClass == 0 || skilldef->TargetClass == def->MineTool)))
						{
							wCoordVec.push_back(blockpos);
						}
					}

				}
			}


		}
	}
	return wCoordVec;
}

std::vector<WORLD_ID> ClientPlayer::doPickActorByItemSkill(int skillid, WCoord& centerPos, Rainbow::Vector3f& currentEyePos, Rainbow::Vector3f& currentDir)
{
	std::vector<WORLD_ID> idvec;
	const ItemSkillDef* skilldef = GetDefManagerProxy()->getItemSkillDef(skillid);
	if (skilldef)
	{
		if (skilldef->RangeType == 0)//????
		{
			if (skilldef->SkillType == 0)//?????
			{
				if (doPick(currentEyePos, currentDir) == 2 && m_PickResult.actor && (skilldef->Distance < 0.001 || skilldef->Distance * skilldef->Distance > getSquareDistToActor(m_PickResult.actor->GetActor())))
				{
					auto pickActor = m_PickResult.actor->GetActor();
					if (pickActor->canAttackByItemSkill(skillid, this))
					{
						idvec.push_back(pickActor->getObjId());
					}
				}
			}
			else if (skilldef->SkillType == 1)//?????
			{
				idvec.push_back(getObjId());
			}
		}
		else if (skilldef->RangeType == 1)//????	
		{
			WORLD_ID objOne = 0;
			if (doPick(currentEyePos, currentDir) == 2 && m_PickResult.actor && (skilldef->RangeVal1 * skilldef->RangeVal1 > getSquareDistToActor(m_PickResult.actor->GetActor())))
			{
				ClientActor* pickActor = m_PickResult.actor->GetActor();
				if (pickActor->canAttackByItemSkill(skillid, this))
				{
					idvec.push_back(m_PickResult.actor->getObjId());
					objOne = m_PickResult.actor->getObjId();
				}
			}

			float length = skilldef->RangeVal1;
			float width = skilldef->RangeVal2 / 2.0f;
			int range = (int)max(length, width);
			CollideAABB box;
			getCollideBox(box);
			box.expand(range, range, range);
			std::vector<IClientActor*>tmpactors;
			m_pWorld->getActorsInBox(tmpactors, box);

			Rainbow::Vector3f origin = currentEyePos;
			Rainbow::Vector3f dir = currentDir;
			for (size_t i = 0; i < tmpactors.size(); i++)
			{
				ClientActor* actor = static_cast<ClientActor*>(tmpactors[i]);
				if (!actor->canAttackByItemSkill(skillid, this))
				{
					continue;
				}
				WCoord originpos((int)origin.x, (int)origin.y, (int)origin.z);
				WCoord hitcenter(actor->getPosition().x, actor->getPosition().y + BLOCK_SIZE / 2, actor->getPosition().z);
				Rainbow::Vector3f dp = (hitcenter - originpos).toVector3();

				float t = DotProduct(dp, dir);
				if (t > 0 && t < length)
				{
					Rainbow::Vector3f tmp = CrossProduct(dp, dir);
					if (tmp.LengthSqr() < width * width && actor->getObjId() != getObjId() && objOne != actor->getObjId()) idvec.push_back(actor->getObjId());
				}
			}
		}
		else //????
		{
			float length = skilldef->RangeVal1 / 2.0f;
			float width = skilldef->RangeVal2 / 2.0f;
			float height = skilldef->RangeVal3 / 2.0f;
			float range = max(length, width);
			range = max(range, height) * 1.5f;

			CollideAABB box;
			if (skilldef->SkillType == 0)//?????
			{
				if (doPick(currentEyePos, currentDir) != 2 || !m_PickResult.actor || (skilldef->Distance > 0.001 && skilldef->Distance * skilldef->Distance < getSquareDistToActor(m_PickResult.actor->GetActor())))
				{
					return idvec;
				}
				else
				{
					m_PickResult.actor->getCollideBox(box);
				}
			}
			else
			{
				getCollideBox(box);
			}
			centerPos.x = box.centerX();
			centerPos.y = box.centerY();
			centerPos.z = box.centerZ();
			//???????
			Rainbow::Vector3f origin = currentEyePos;
			Rainbow::Vector3f dir = currentDir;

			box.expand((int)range, (int)range, (int)range);
			std::vector<IClientActor*>tmpactors;
			m_pWorld->getActorsInBox(tmpactors, box);

			//?????????x,y,z????
			float rotateYaw;
			float rotationPitch;
			Direction2PitchYaw(&rotateYaw, &rotationPitch, dir);
			Quaternionf rotation = AngleEulerToQuaternionf(Vector3f(rotationPitch, rotateYaw, 0));;
			//rotation.setEulerAngle(rotateYaw, rotationPitch, 0);
			dir = rotation.GetAxisX();
			Rainbow::Vector3f dir2 = rotation.GetAxisY();
			Rainbow::Vector3f dir3 = rotation.GetAxisZ();

			for (size_t i = 0; i < tmpactors.size(); i++)
			{
				ClientActor* actor = static_cast<ClientActor*>(tmpactors[i]);
				if (!actor->canAttackByItemSkill(skillid, this))
				{
					continue;
				}
				CollideAABB hitbox;
				actor->getHitCollideBox(hitbox);
				WCoord originpos(box.centerX(), box.centerY(), box.centerZ());
				WCoord hitcenter(hitbox.centerX(), hitbox.centerY(), hitbox.centerZ());
				Rainbow::Vector3f dp = (hitcenter - originpos).toVector3();

				if (actor->getObjId() == getObjId())
				{
					continue;
				}
				float t = DotProduct(dp, dir);
				if (t == 0 || abs(t) > length)
				{
					continue;
				}
				t = DotProduct(dp, dir2);
				if (t == 0 || abs(t) > width)
				{
					continue;
				}
				t = DotProduct(dp, dir3);
				if (t == 0 || abs(t) > height)
				{
					continue;
				}

				idvec.push_back(actor->getObjId());
			}
		}
	}
	return idvec;
}

std::vector<WORLD_ID> ClientPlayer::doPickPhysicsActorByItemSkill(int skillid, WCoord& centerPos, Rainbow::Vector3f& currentEyePos, Rainbow::Vector3f& currentDir)
{
	std::vector<WORLD_ID> idvec;
	const ItemSkillDef* skilldef = GetDefManagerProxy()->getItemSkillDef(skillid);
	if (skilldef)
	{
		if (skilldef->RangeType == 0)//????
		{
			if (skilldef->SkillType == 0)//?????
			{
				for (size_t i = 0; i < skilldef->SkillFuncions.size(); i++)
				{
					ItemSkillDef::SkillFuncionsDef* functiondef = (ItemSkillDef::SkillFuncionsDef*)&skilldef->SkillFuncions[i];
					if (functiondef->oper_id == 13) {//???????????
						ActorVehicleAssemble* pVehicle = dynamic_cast<ActorVehicleAssemble*>(m_PickResult.actor);
						if (pVehicle) {
							shortcutItemUsed();
							pVehicle->setNeedClear();
							idvec.clear();
							return idvec;
						}
					}
				}
				auto pickActor = static_cast<ClientActor*>(m_PickResult.actor);
				if (doPick(currentEyePos, currentDir) == 2 && pickActor && (skilldef->Distance < 0.001 || skilldef->Distance * skilldef->Distance > getSquareDistToActor(pickActor)))
				{
					PhysicsLocoMotion* loc = dynamic_cast<PhysicsLocoMotion*>(pickActor->getLocoMotion());
					if (loc && loc->m_hasPhysActor)
					{
						idvec.push_back(m_PickResult.actor->getObjId());
					}
				}
			}
			else if (skilldef->SkillType == 1)//?????
			{
				idvec.push_back(getObjId());
			}
		}
		else if (skilldef->RangeType == 1)//????	
		{
			WORLD_ID objOne = 0;
			auto pickActor = static_cast<ClientActor*>(m_PickResult.actor);
			if (doPick(currentEyePos, currentDir) == 2 && pickActor && (skilldef->RangeVal1 * skilldef->RangeVal1 > getSquareDistToActor(pickActor)))
			{
				PhysicsLocoMotion* loc = dynamic_cast<PhysicsLocoMotion*>(pickActor->getLocoMotion());
				if (loc && loc->m_hasPhysActor)
				{
					idvec.push_back(m_PickResult.actor->getObjId());
					objOne = m_PickResult.actor->getObjId();
				}
			}

			float length = skilldef->RangeVal1;
			float width = skilldef->RangeVal2 / 2.0f;
			int range = (int)max(length, width);
			CollideAABB box;
			getCollideBox(box);
			box.expand(range, range, range);
			std::vector<IClientActor*>tmpactors;
			m_pWorld->getActorsInBox(tmpactors, box);

			Rainbow::Vector3f origin = currentEyePos;
			Rainbow::Vector3f dir = currentDir;
			for (size_t i = 0; i < tmpactors.size(); i++)
			{
				ClientActor* actor = static_cast<ClientActor*>(tmpactors[i]);
				if (actor == NULL) continue;
				PhysicsLocoMotion* loc = dynamic_cast<PhysicsLocoMotion*>(actor->getLocoMotion());
				if (loc == NULL)
				{
					continue;
				}
				if (loc->m_hasPhysActor == false)
				{
					continue;
				}
				WCoord originpos((int)origin.x, (int)origin.y, (int)origin.z);
				WCoord hitcenter(actor->getPosition().x, actor->getPosition().y + BLOCK_SIZE / 2, actor->getPosition().z);
				Rainbow::Vector3f dp = (hitcenter - originpos).toVector3();

				float t = DotProduct(dp, dir);
				if (t > 0 && t < length)
				{
					Rainbow::Vector3f tmp = CrossProduct(dp, dir);
					if (tmp.LengthSqr() < width * width && actor->getObjId() != getObjId() && objOne != actor->getObjId()) idvec.push_back(actor->getObjId());
				}
			}
		}
		else //????
		{
			float length = skilldef->RangeVal1 / 2.0f;
			float width = skilldef->RangeVal2 / 2.0f;
			float height = skilldef->RangeVal3 / 2.0f;
			float range = max(length, width);
			range = max(range, height) * 1.5f;

			CollideAABB box;
			if (skilldef->SkillType == 0)//?????
			{
				auto pickActor = static_cast<ClientActor*>(m_PickResult.actor);
				if (doPick(currentEyePos, currentDir) != 2 || !pickActor || (skilldef->Distance > 0.001 && skilldef->Distance * skilldef->Distance < getSquareDistToActor(pickActor)))
				{
					return idvec;
				}
				else
				{
					pickActor->getCollideBox(box);
				}
			}
			else
			{
				getCollideBox(box);
			}
			centerPos.x = box.centerX();
			centerPos.y = box.centerY();
			centerPos.z = box.centerZ();
			//???????
			Rainbow::Vector3f origin = currentEyePos;
			Rainbow::Vector3f dir = currentDir;

			box.expand((int)range, (int)range, (int)range);
			std::vector<IClientActor*>tmpactors;
			m_pWorld->getActorsInBox(tmpactors, box);

			//?????????x,y,z????
			float rotateYaw;
			float rotationPitch;
			Direction2PitchYaw(&rotateYaw, &rotationPitch, dir);
			Quaternionf rotation = AngleEulerToQuaternionf(Vector3f(rotationPitch, rotateYaw, 0));;
			//rotation.setEulerAngle(rotateYaw, rotationPitch, 0);
			dir = rotation.GetAxisX();
			Rainbow::Vector3f dir2 = rotation.GetAxisY();
			Rainbow::Vector3f dir3 = rotation.GetAxisZ();

			for (size_t i = 0; i < tmpactors.size(); i++)
			{
				ClientActor* actor = static_cast<ClientActor*>(tmpactors[i]);
				if (actor == NULL) continue;
				PhysicsLocoMotion* loc = dynamic_cast<PhysicsLocoMotion*>(actor->getLocoMotion());
				if (loc == NULL)
				{
					continue;
				}
				if (loc->m_hasPhysActor == false)
				{
					continue;
				}
				CollideAABB hitbox;
				actor->getHitCollideBox(hitbox);
				WCoord originpos(box.centerX(), box.centerY(), box.centerZ());
				WCoord hitcenter(hitbox.centerX(), hitbox.centerY(), hitbox.centerZ());
				Rainbow::Vector3f dp = (hitcenter - originpos).toVector3();

				if (actor->getObjId() == getObjId())
				{
					continue;
				}
				float t = DotProduct(dp, dir);
				if (t == 0 || abs(t) > length)
				{
					continue;
				}
				t = DotProduct(dp, dir2);
				if (t == 0 || abs(t) > width)
				{
					continue;
				}
				t = DotProduct(dp, dir3);
				if (t == 0 || abs(t) > height)
				{
					continue;
				}

				idvec.push_back(actor->getObjId());
			}
		}
	}
	return idvec;
}

bool ClientPlayer::GetScreenSpacePos(Rainbow::Vector3f pos, int& x, int& y)
{
#ifndef IWORLD_SERVER_BUILD
	x = -1;
	y = -1;
	Vector3f result(0, 0, 0);
	Camera* camera = CameraManager::GetInstance().getEngineCamera();
	if (camera)
	{
		result = camera->WorldToScreenPoint(pos);
		x = result.x;
		y = result.y;
		return true;
	}
#endif
	return false;
}


void ClientPlayer::setLandingPoint(int mapid, WCoord pos)
{
	if (mapid < MAX_MAP)
		m_LandingPoints[mapid] = pos;
}

WCoord ClientPlayer::getLandingPoint(int mapid)
{
	if (mapid < MAX_MAP)
		return m_LandingPoints[mapid];

	return WCoord(0, -1, 0);
}

bool ClientPlayer::isOwnCreateWorld()
{
	if (m_pWorld && m_pWorld->isRemoteMode()) return false;

	if (m_pWorld && !m_pWorld->isRemoteMode())
	{
		auto desc = GetClientInfoProxy()->getCurWorldDesc();
		if (desc && desc->realowneruin == getUin()) return true;
	}

	return false;
}
int ClientPlayer::updateNpcShopItemChange(int shopid, int skuid, int buycount)
{
	NpcShopDef* pNpcShopDef = GetDefManagerProxy()->getNpcShopDef(shopid);
	if (!pNpcShopDef) { return -1; }

	NpcShopItemDef* pSkuInfo = pNpcShopDef->getNpcShopSkuDef(skuid);
	if (!pSkuInfo) { return -1; }

	ItemDef* pItemDef = GetDefManagerProxy()->getAutoUseForeignID(pSkuInfo->iCostItemInfo1 / 1000);
	int iCostItemId1 = pItemDef ? pItemDef->ID : 0;
	int iCostCount1 = pSkuInfo->iCostItemInfo1 % 1000;

	pItemDef = pSkuInfo->iCostItemInfo2 > 0 ?
		GetDefManagerProxy()->getAutoUseForeignID(pSkuInfo->iCostItemInfo2 / 1000) : nullptr;
	int iCostItemId2 = pItemDef ? pItemDef->ID : 0;
	int iCostCount2 = pSkuInfo->iCostItemInfo2 % 1000;

	bool item1Enough = true;
	bool item2Enough = true;

	jsonxx::Object log;
	log << "server_shopid" << shopid;
	log << "client_buy_skuid" << skuid;
	log << "client_buy_count" << buycount;
	log << "server_sku_cost1" << pSkuInfo->iCostItemInfo1;
	log << "server_sku_cost2" << pSkuInfo->iCostItemInfo2;
	log << "server_sku_costitem1" << iCostItemId1;
	log << "server_sku_costitem2" << iCostItemId2;

	int haveCount1 = getBackPack()->getItemCountInNormalPack(iCostItemId1);
	item1Enough = (haveCount1 >= (buycount * iCostCount1));
	log << "server_item_have1" << haveCount1;
	log << "server_item_need1" << buycount * iCostCount1;

	// Item2是可能不配置的
	if (iCostItemId2 > 0)
	{
		int haveCount2 = getBackPack()->getItemCountInNormalPack(iCostItemId2);
		item2Enough = (haveCount2 >= (buycount * iCostCount2));
		log << "server_item_have2" << haveCount2;
		log << "server_item_need2" << buycount * iCostCount2;
	}
	log << "server_enough1" << item1Enough;
	log << "server_enough2" << item2Enough;

	if (!item1Enough || !item2Enough || buycount <= 0)
	{
#ifdef IWORLD_SERVER_BUILD
		Rainbow::GetICloudProxyPtr()->InfoLog(getUin(), 0, "cheat_npc_shop_buy", log);
#endif
		return -1;
	}

	//remove player star
	int iStarNum = pSkuInfo->iStarNum;
	if (iStarNum > 0) {
		auto attr = getAttrib();
		auto playerAttr = attr == nullptr ? nullptr : dynamic_cast<PlayerAttrib*>(attr);
		if (playerAttr) {
			int curExp = playerAttr->getExp();
			int needExp = buycount * iStarNum * EXP_STAR_RATIO;
			if (needExp <= curExp) {
				playerAttr->setExp(curExp - needExp);
			}
		}
	}

	if (pSkuInfo->iCostItemInfo1 > 0) {
		removeBackpackItem(iCostItemId1, buycount * iCostCount1);
	}

	if (pSkuInfo->iCostItemInfo2 > 0) {
		removeBackpackItem(iCostItemId2, buycount * iCostCount2);
	}

	//add item to backpack
	pItemDef = GetDefManagerProxy()->getAutoUseForeignID(pSkuInfo->iItemID);
	int itemid = pItemDef ? pItemDef->ID : 101;
	int num = buycount * pSkuInfo->iOnceBuyNum;

	log << "server_get_itemid" << itemid;
	log << "server_get_num" << num;
#ifdef IWORLD_SERVER_BUILD
	Rainbow::GetICloudProxyPtr()->InfoLog(getUin(), 0, "npc_shop_buy_success", log);
#endif
	int addnum = getBackPack()->addItem(itemid, num);
	if (addnum < num)
	{
		int h = getThrowItemHeight();
		ClientItem* item = getActorMgr()->spawnItem(getPosition() + WCoord(0, h, 0), itemid, num - addnum);
		if (item == NULL) return -1;

		Rainbow::Vector3f& motion = item->getLocoMotion()->m_Motion;
		Rainbow::Vector3f dir;

		float r = 30.0f;
		PitchYaw2Direction(dir, getLocoMotion()->m_RotateYaw, getLocoMotion()->m_RotationPitch);
		motion.x = dir.x * r;
		motion.z = dir.z * r;
		motion.y = dir.y * r + 10.0f;

		r = 2.0f * GenRandomFloat();
		float angle = GenRandomFloat() * 360.0f;

		motion.x += r * Cos(angle);
		motion.z += r * Rainbow::Sin(angle);
		motion.y += (GenRandomFloat() - GenRandomFloat()) * 10.0f;
	}

	return 0;
}

int ClientPlayer::updatePackGiftItemChange(int packindex, int costitemid, int costitemnum, const std::map<int, int>& addMap)
{
	//???????ID
	int ItemID = getBackPack()->getGridItem(packindex);
	if (ItemID > 0)
	{
		ObserverEvent_PlayerItem obevent(getUin(), ItemID, 1);
		GetObserverEventManager().OnTriggerEvent("Player.UseGiftPack", &obevent);
	}
	else
	{
		return 0;
	}
	//删除该包裹道具
	getBackPack()->removeItem(packindex, 1);

	if (costitemid > 0) {//???????
		removeBackpackItem(costitemid, costitemnum);
	}

	//?????????
	int addnum, itemid, num;
	auto it = addMap.begin();
	for (; it != addMap.end(); it++) {
		itemid = it->first;
		num = it->second;
		addnum = getBackPack()->addItem(itemid, num);
		if (addnum < num)
		{
			int h = getThrowItemHeight();
			ClientItem* item = getActorMgr()->spawnItem(getPosition() + WCoord(0, h, 0), itemid, num - addnum);
			if (item == NULL) return -1;

			Rainbow::Vector3f& motion = item->getLocoMotion()->m_Motion;
			Rainbow::Vector3f dir;

			float r = 30.0f;
			PitchYaw2Direction(dir, getLocoMotion()->m_RotateYaw, getLocoMotion()->m_RotationPitch);
			motion.x = dir.x * r;
			motion.z = dir.z * r;
			motion.y = dir.y * r + 10.0f;

			r = 2.0f * GenRandomFloat();
			float angle = GenRandomFloat() * 360.0f;

			motion.x += r * Cos(angle);
			motion.z += r * Rainbow::Sin(angle);
			motion.y += (GenRandomFloat() - GenRandomFloat()) * 10.0f;
		}
	}

	return 0;
}


bool ClientPlayer::useMagicWand()
{
	Rainbow::Vector3f dir = getLocoMotion()->getLookDir();
	dir = MINIW::Normalize(dir);
	dir *= (BLOCK_SIZE * 2);
	WCoord pt = getEyePosition() + dir;

	WCoord blockpos = CoordDivBlock(pt);
	int blockid = getWorld()->getBlockID(blockpos);
	if (blockid != 0)
		return false;

	BlockMaterial* newmtl = g_BlockMtlMgr.getMaterial(BLOCK_PLANTSPACE_CLOUD);
	if (newmtl == NULL)
		return false;

	int blockdata = newmtl->getPlaceBlockData(getWorld(), blockpos, DIR_POS_Y, pt.x, pt.y, pt.z, 0);
	if (blockdata < 0)
		return false;

	getWorld()->setBlockAll(blockpos, BLOCK_PLANTSPACE_CLOUD, blockdata, 3);

	if (getWorld()->getBlockID(blockpos) == BLOCK_PLANTSPACE_CLOUD)
	{
		newmtl->DoOnBlockPlacedBy(getWorld(), blockpos, this, 0, Rainbow::Vector3f::zero, false, 0);//onBlockPlacedBy(getWorld(), blockpos, this);

		// 观察者事件接口
		ObserverEvent_ActorBlock obevent((long long)getObjId(), newmtl->getBlockResID(), blockpos.x, blockpos.y, blockpos.z);
		GetObserverEventManager().OnTriggerEvent("Block.PlaceBy", &obevent);
	}

	return true;
}



/*
bool ClientPlayer::getAccountWorldPoint(int mapid, WCoord& spawnpoint, WCoord& revivepoint)
{
	for (size_t i = 0; i < m_AccountWorldPoint.size(); i++)
	{
		AccountWorldPointInfo& src = m_AccountWorldPoint[i];
		if (src.mapid == mapid)
		{
			spawnpoint = src.spawnpoint;
			revivepoint = src.revivepoint;

			bool bValid = (spawnpoint != WCoord(0, -1, 0)) || (revivepoint != WCoord(0, -1, 0));
			return bValid;
		}
	}

	return false;
}
*/


bool ClientPlayer::needHandleWeaponMotionForView(int status, const char* motionName, bool stopMotion)
{
	if (status == 0 || motionName == "")
	{
		return false;
	}
	// ??????????????????Ч???л?????????????
	if (stopMotion)
	{
		this->stopMotion(30);
	}
	if (getCurToolID() == ITEM_GRAVITYGUN)
	{
		m_WeaponMotionStatus = status;
		m_WeaponMotionName = motionName;
		PlayerControl* control = dynamic_cast<PlayerControl*>(this);
		if (control != NULL) {
			if (control->getViewMode() == CameraControlMode::CAMERA_FPS && control->m_CameraModel)
			{
				if (status == 1)
				{
					control->m_CameraModel->playItemMotion("item_12293_FPSpickup", true, 30);
				}
				else if (status == 2)
				{
					control->m_CameraModel->playItemMotion("item_12293_FPScharge", true, 30);
				}
				else if (status == 3)
				{
					control->m_CameraModel->playItemMotion("item_12293_FPSfreed", true, 30);
				}
			}
			else
			{
				this->stopMotion(30);
				playMotion(motionName, 30, true);
			}
		}
		return true;
	}
	m_WeaponMotionStatus = 0;
	m_WeaponMotionName = "";
	return false;
}

bool ClientPlayer::checkCanOpenBackpack()
{
	//暂时屏蔽'禁用背包'开关
	return true;
	//return checkActionAttrState(ENABLE_OPENBACKPACK);
}

bool ClientPlayer::checkDeveloperHandleForActor(bool isLeft, ClientActor* target, bool openWnd)
{
	if (getCurToolID() == ITEM_DEVELOPER && target != NULL)
	{
		int objType = target->getObjType();
		int objId = (int)target->getObjId();
		int defID = target->getDefID();
		bool isRigidBody = false;
		PhysicsLocoMotion* loc = dynamic_cast<PhysicsLocoMotion*>(target->getLocoMotion());
		if (loc && loc->m_PhysActor)
		{
			isRigidBody = true;
		}
		if (objType != OBJ_TYPE_FIREWORK && !isRigidBody)
		{
			ClientMob* mob = dynamic_cast<ClientMob*>(target);
			// ???????ui
			if (mob)
			{
				if (openWnd)
				{
					int team = mob->getTeam();
#if defined(IWORLD_DEV_BUILD) && (OGRE_PLATFORM == OGRE_PLATFORM_WIN32)
					if (!mob->OpenBTreeForTest())
#endif
						MINIW::ScriptVM::game()->callFunction("OpenDeveloperWindowByRole", "biiii", isLeft, objType, objId, defID, team);
				}
				return true;
			}
			else
				return false;

		}
		else
		{
			return false;
		}
	}
	else
	{
		return false;
	}
}

bool ClientPlayer::checkDeveloperHandleForBlock(bool isLeft, int blockId, int posX, int posY, int posZ)
{
	// ????????ui
	if (getCurToolID() == ITEM_DEVELOPER && getWorld() != NULL)
	{
		const BlockDef* def = GetDefManagerProxy()->getBlockDef(blockId);
		if (def)
		{
			MINIW::ScriptVM::game()->callFunction("OpenDeveloperWindowByBlock", "u[BlockDef]iii", def, posX, posY, posZ);
			return true;
		}
		else
			return false;
	}
	else
	{
		return false;
	}
}

//----------- state  begin
bool ClientPlayer::syncMotionState(int state, bool b/* =true */)
{
	if (m_pWorld && m_pWorld->isRemoteMode())
	{
		PB_PlayerMotionStateChangeCH playerMotionStateChangeCH;
		playerMotionStateChangeCH.set_statetype(state);
		playerMotionStateChangeCH.set_stateswitch(b);

		GetGameNetManagerPtr()->sendToHost(PB_PLAYER_MOTIONSTATECHANGE_CH, playerMotionStateChangeCH);
	}
	return true;
}

void ClientPlayer::setMotionState(int state, bool b/* =true */)
{
	m_PlayerStateMgr.setStateReal((PLAYERST_TYPE)state, b);
}

void ClientPlayer::setActionAttrState(int actionattr, bool b)
{
	if (!g_WorldMgr)
		return;
	if (!g_WorldMgr->m_RuleMgr)
	{
		if (!g_WorldMgr->canOpenAttrPropSet())
		{
			return;
		}
	}
	auto ActionAttrStateComp = getActionAttrStateComponent();
	if (ActionAttrStateComp)
	{
		ActionAttrStateComp->setActionAttrState_Base(actionattr, b);
	}
	if (!hasUIControl() && (actionattr == ENABLE_MOVE || actionattr == ENABLE_VEHICLEAUTOFORWARD))  //某些属性需要同步给客机 由客机管理
	{
		PB_PlayerCanMoveHC playerCanMoveHC;
		playerCanMoveHC.set_canmove(b);

		GetGameNetManagerPtr()->sendToClient(getUin(), PB_PLAYER_CANMOVE_HC, playerCanMoveHC);
		return;
	}
	//主机同步
	if (m_pWorld && !m_pWorld->isRemoteMode())
	{
		if (actionattr == ENABLE_FORBIDFIRE)
		{
			PB_PlayerCanFireHC playerCanFireHC;
			playerCanFireHC.set_fire(!b);
			GetGameNetManagerPtr()->sendToClient(getUin(), PB_PLAYER_CANFIRE_HC, playerCanFireHC);
		}
	}
}

bool ClientPlayer::checkActionAttrState(int actionattr)
{
	auto ActionAttrStateComp = getActionAttrStateComponent();
	if (ActionAttrStateComp)
	{
		return ActionAttrStateComp->checkActionAttrState_Base(actionattr);
	}
	return false;
}

void ClientPlayer::setJumpState(int times)
{
	bool jumpStateActive = false;
	PLAYERST_TYPE jumpEnum = PLAYERSTTYPE_STOP;
	ConstAtLua* constAtLua = GetLuaInterfaceProxy().get_lua_const();
	//跳跃消耗体力加上buff消耗 code-by:曹泽港
	float consumedStrength = 0.f;
	float extraStrength = 0.f;
	float allStrength = 0.f;
	PlayerAttrib* attr = getPlayerAttrib();
	// 需要同步
	if (times == 0)
	{
		jumpEnum = PLAYERSTTYPE_JUMPFIRST;
		consumedStrength = m_PlayerStateMgr.getState(PLAYERSTTYPE_RUN) ? constAtLua->strength_consumption_of_running_jump : 0;
		if (attr)
		{
			extraStrength = attr->getActorAttValueWithStatus(BuffAttrType::BUFFATTRT_RUN_JUMP_SPEND_STRENGTH, consumedStrength);
		}
	}
	else if (times == 1)
	{
		jumpEnum = PLAYERSTTYPE_JUMPSECOND;
		consumedStrength = constAtLua->strength_consumption_of_double_jump;
		if (attr)
		{
			extraStrength = attr->getActorAttValueWithStatus(BuffAttrType::BUFFATTRT_JUMP_SECOND_SPEND_STRENGTH, consumedStrength);
		}
	}
	else
	{
		jumpEnum = PLAYERSTTYPE_JUMP;
		consumedStrength = m_PlayerStateMgr.getState(PLAYERSTTYPE_RUN) ? constAtLua->strength_consumption_of_running_jump : 0;
		if (attr)
		{
			extraStrength = attr->getActorAttValueWithStatus(BuffAttrType::BUFFATTRT_RUN_JUMP_SPEND_STRENGTH, consumedStrength);
		}
	}
	if (attr)
	{
		allStrength = attr->getActorAttValueWithStatus(BuffAttrType::BUFFATTRT_ALL_SPEND_STRENGTH, consumedStrength);
	}
	consumedStrength = consumedStrength + extraStrength + allStrength;

	const bool hasEnoughStrength = m_PlayerAttrib->isStrengthEnough(consumedStrength);
	jumpStateActive = hasEnoughStrength;

	m_PlayerStateMgr.setState(jumpEnum, jumpStateActive);
	if (jumpStateActive)
	{
		m_PlayerAttrib->addStrength(-consumedStrength);
	}

	//睡在床上的时候跳起来，需要重置床的占用状态
	WCoord blockpos = CoordDivBlock(getPosition());
	if (IsBedBlock(m_pWorld->getBlockID(blockpos)))
	{
		BedLogicHandle::setBedOccupied(m_pWorld, blockpos, false);
	}
}

void ClientPlayer::setStopState(bool b)
{
	m_PlayerStateMgr.setState(PLAYERSTTYPE_STOP, b);
}

void ClientPlayer::setFallGround(bool b)
{
	m_PlayerStateMgr.setState(PLAYERSTTYPE_FALLGROUND, b);
	if (b)
	{
		m_PlayerStateMgr.setState(PLAYERSTTYPE_JUMP, false);
		m_PlayerStateMgr.setState(PLAYERSTTYPE_JUMPFIRST, false);
		m_PlayerStateMgr.setState(PLAYERSTTYPE_JUMPSECOND, false);
	}
}

void ClientPlayer::setCurrentMoveState(bool b)
{
	m_PlayerStateMgr.setState(PLAYERSTTYPE_MOVE, b);
}

void ClientPlayer::setSneaking(bool b)
{
	ActorLiving::setSneaking(b);
	m_PlayerStateMgr.setState(PLAYERSTTYPE_SNEAK, b);
	static_cast<PlayerLocoMotion*>(getLocoMotion())->OnSneakChange(b);
}

void ClientPlayer::updateRunState()
{
	m_PlayerStateMgr.setState(PLAYERSTTYPE_RUN, getRun());
}

bool ClientPlayer::isPlayerDowned()
{
	PlayerAttrib* playerAttrib = dynamic_cast<PlayerAttrib*>(getAttrib());
	if (playerAttrib && playerAttrib->isPlayerDowned())
	{
		return true;
	}

	return false;
}

//--------------state  end

int ClientPlayer::getMaxLifeNum()
{
	if (m_MaxLifeNum > 0)
		return m_MaxLifeNum;

	if (GetWorldManagerPtr() != NULL && GetWorldManagerPtr()->isGameMakerRunMode() && GetWorldManagerPtr()->m_RuleMgr)
	{
		return (int)GetWorldManagerPtr()->m_RuleMgr->getRuleOptionVal(GMRULE_LIFE_NUM);
	}

	return 0;
}

void ClientPlayer::setLeftLiftNum(int num)
{
	m_MaxLifeNum = num + GetWorldManagerPtr()->m_RuleMgr->getTeamDieTimes(getTeam());
}

Rainbow::Vector3f ClientPlayer::getCameraLookDir()
{
	Rainbow::Vector3f dir = MINIW::Normalize(getLocoMotion()->getLookDir());
	if (this->getRidingVehicle())
	{
		ClientActor* riding = this->getWorld()->getActorMgr()->ToCastMgr()->findActorByWID(this->getRidingVehicle());
		if (riding)
			dir = MINIW::Normalize(this->getBodyDir());
	}

	return dir;
}


bool ClientPlayer::getAimPos(int& outx, int& outy, int& outz, int distance/*=20*/, bool calibration/*=false*/)
{
	MINIW::WorldRay ray;
	auto tmpOrigin = getEyePosition();
	/*ray.m_Dir  = MINIW::Normalize(getLocoMotion()->getLookDir());

	if (this->getRidingVehicle())
	{
		ClientActor * riding = this->getWorld()->getActorMgr()->findActorByWID(this->getRidingVehicle());
		if (riding)
		{
			ray.m_Dir = getBodyDir();
			ray.m_Dir  = MINIW::Normalize(ray.m_Dir);
		}
	}*/
	ray.m_Dir = getCameraLookDir();
	ray.m_Origin = tmpOrigin.toWorldPos();

	ray.m_Range = distance * BLOCK_FSIZE;
	const auto origin = tmpOrigin.toVector3();
	const auto& dir = ray.m_Dir;
	const auto& range = ray.m_Range;

	ActorExcludes excludes;
	excludes.addActorWithRiding(this);

	IntersectResult presult;
	if (!m_pWorld)
	{
		return false;
	}
	WorldPickResult intertype = m_pWorld->pickAll(ray, &presult, excludes, PICK_METHOD_CLICK);
	if (intertype == WorldPickResult::ACTOR)
	{
		outx = presult.collide_pos.x;
		outy = presult.collide_pos.y;
		outz = presult.collide_pos.z;
		return true;
	}
	else if (intertype == WorldPickResult::BLOCK)
	{
		// ???????????е?
		float blocksize = calibration ? 99.0f : 100.0f;
		Rainbow::Vector3f blockposBeg = presult.block.toVector3() * blocksize;
		Rainbow::Vector3f blockposEnd = blockposBeg + 100.0f;
		Rainbow::Vector3f planePos; // x, y, z

		// ?????
		planePos.x = (dir.x > 0) ? blockposBeg.x : blockposEnd.x;
		planePos.y = (dir.y > 0) ? blockposBeg.y : blockposEnd.y;
		planePos.z = (dir.z > 0) ? blockposBeg.z : blockposEnd.z;

		// ??????? : pos = origin + dir * k -> k = [0.0f, +infinity)
		Rainbow::Vector3f ret;
		do
		{
			float k = 0.0f;
			if (dir.x != 0 && (k = (planePos.x - origin.x) / dir.x) >= 0.0f)
			{
				ret.x = planePos.x;
				ret.y = origin.y + dir.y * k;
				ret.z = origin.z + dir.z * k;

				if (ret.y >= blockposBeg.y && ret.y <= blockposEnd.y
					&& ret.z >= blockposBeg.z && ret.z <= blockposEnd.z)
				{
					break;
				}
			}
			if (dir.y != 0 && (k = (planePos.y - origin.y) / dir.y) >= 0.0f)
			{
				ret.x = origin.x + dir.x * k;
				ret.y = planePos.y;
				ret.z = origin.z + dir.z * k;

				if (ret.x >= blockposBeg.x && ret.x <= blockposEnd.x
					&& ret.z >= blockposBeg.z && ret.z <= blockposEnd.z)
				{
					break;
				}
			}
			if (dir.z != 0 && (k = (planePos.z - origin.z) / dir.z) >= 0.0f)
			{
				ret.x = origin.x + dir.x * k;
				ret.y = origin.y + dir.y * k;
				ret.z = planePos.z;

				if (ret.x >= blockposBeg.x && ret.x <= blockposEnd.x
					&& ret.y >= blockposBeg.y && ret.y <= blockposEnd.y)
				{
					break;
				}
			}

			//assert(false && "aim point - calc block precision failed!");
			ret = (blockposBeg + blockposEnd) * 0.5f;
		} while (0);

		outx = (int)ret.x;
		outy = (int)ret.y;
		outz = (int)ret.z;
		return true;
	}
	else
	{
		// ??????
		Rainbow::Vector3f endpos = origin + dir * range;
		outx = (int)endpos.x;
		outy = (int)endpos.y;
		outz = (int)endpos.z;
		return true;
	}
}

Rainbow::Vector3f ClientPlayer::getBodyDir()
{
	Quaternionf quat;
	bool setbindrot = false;
	Rainbow::Vector3f pos(0, Rainbow::MAX_FLOAT, 0);
	ClientActor* riding = NULL;

	auto RidComp = getRiddenComponent();
	if (RidComp && RidComp->isRiding() && (riding = RidComp->getRidingActor()) != NULL)
	{
		auto ridingComp = riding->getRiddenComponent();
		if (ridingComp)
		{
			ridingComp->resetRiddenBindPos();
			pos = ridingComp->getRiddenBindPos(this);
			setbindrot = ridingComp->getRiddenBindRot(this, quat);
		}
	}
	else
	{
		pos = getLocoMotion()->getFramePosition().toVector3();
		pos.y -= getLocoMotion()->m_yOffset;
	}

	Quaternionf selfquat;
	if (getBody()->getControlRotation() && !getReverse())
	{
		if (pos.y != Rainbow::MAX_FLOAT && getBody()->getEntity())
			setPosition(WorldPos::fromVector3(pos));

		//selfquat.setEulerAngle(getBody()->getRenderYawOffset(), 0, 0);
		selfquat = AngleEulerToQuaternionf(Vector3f(0, getBody()->getRenderYawOffset(), 0));
	}
	else if (getReverse())
	{
		//selfquat.setEulerAngle(getBody()->getRenderYawOffset(), 0, 180);
		selfquat = AngleEulerToQuaternionf(Vector3f(0, getBody()->getRenderYawOffset(), 180));
		if (getBody()->getEntity())
			setPosition(WorldPos::fromVector3(pos + Rainbow::Vector3f(0.0f, (float)getLocoMotion()->m_BoundHeight, 0.0f)));
	}
	else
	{
		selfquat = Quaternionf::identity;
		if (pos.y != Rainbow::MAX_FLOAT && getBody()->getEntity())
			setPosition(WorldPos::fromVector3(pos));
	}

	if (riding && riding->getObjType() == OBJ_TYPE_ROCKET)
	{
		//selfquat.setEulerAngle(0, 0, 0);
		selfquat = AngleEulerToQuaternionf(Vector3f(0, 0, 0));
	}

	if (riding && riding->getObjType() == OBJ_TYPE_SHAPESHIFT_HORSE)
	{
		//selfquat.setEulerAngle(0, 0, 0);
		selfquat = AngleEulerToQuaternionf(Vector3f(0, 0, 0));
	}

	if (riding && riding->getObjType() == OBJ_TYPE_VEHICLE)
	{
		ActorVehicleAssemble* vehicle = dynamic_cast<ActorVehicleAssemble*>(riding);
		if (vehicle)
		{
			int dir = vehicle->getRiddenBindSeatDir(this);
			float angle = 0.0f;
			if (dir == DIR_NEG_X)
			{
				angle = 90.0f;
			}
			else if (dir == DIR_POS_X)
			{
				angle = -90.0f;
			}
			else if (dir == DIR_NEG_Z)
			{
				angle = 0.0f;
			}
			else if (dir == DIR_POS_Z)
			{
				angle = 180.0f;
			}

			//selfquat.setEulerAngle(vehicle->getLocoMotion()->m_RotateYaw + angle, 0, 0);
			//selfquat.setEulerAngle(angle, 0, 0);
			selfquat = AngleEulerToQuaternionf(Vector3f(0, angle, 0));
		}
	}

	if (setbindrot)
	{
		quat.Inverse();
		selfquat = selfquat * quat;
	}
	return selfquat.GetAxisZ();
}

bool ClientPlayer::isCloudRoomServerOwner()
{
	int hostuin = 0;
	if (GetGameNetManagerPtr())
		hostuin = GetGameNetManagerPtr()->getHostUin();

	// ???????????? ??????????????
	if (hostuin == getUin())
		return true;

	return false;
}

void ClientPlayer::setSpeedUpTimes(unsigned char speedUpTimes)
{
	m_SpeedUpTimes = speedUpTimes;
}

unsigned char ClientPlayer::getSpeedUpTimes()
{
	return m_SpeedUpTimes;
}

void ClientPlayer::onBuffAppend(int buffid, int bufflvl)
{
	if (!m_pWorld)
		return;

	//LOG_INFO("BUFF for callback.[player]: append! buffid=%d, bufflvl=%d", buffid, bufflvl);
	ObserverEvent_PlayerBuff obevent(this->getUin(), buffid, bufflvl);
	GetObserverEventManager().OnTriggerEvent("Player.AddBuff", &obevent);
}

void ClientPlayer::onBuffRemove(int buffid, int bufflvl)
{
	if (!m_pWorld)
		return;

	//LOG_INFO("BUFF for callback.[player]: remove! buffid=%d, bufflvl=%d", buffid, bufflvl);
	ObserverEvent_PlayerBuff obevent(this->getUin(), buffid, bufflvl);
	GetObserverEventManager().OnTriggerEvent("Player.RemoveBuff", &obevent);

	if (buffid == TOUGHNESSBREAK_PLAYER_BUFF)
	{
		stopAnim(SEQ_BROKEN_TOUGHNESS);
	}
}

Rainbow::Vector3f ClientPlayer::getCarryingBindPos()
{
	RoleDef* def = NULL;
	if (getBody())
	{
#ifndef IWORLD_SERVER_BUILD
		def = GetDefManagerProxy()->getRoleDef(getBody()->getModelID(), getBody()->getGeniusLv());
#else
		int playerindex = getBody()->getPlayerIndex();
		def = GetDefManagerProxy()->getRoleDef(PlayerIndex2Model(playerindex), PlayerIndex2Genius(playerindex)); ;
#endif 
	}

	if (def != NULL)
	{
		Rainbow::Vector3f pos = getLocoMotion()->getFramePosition().toVector3();
		pos.y += def->CarryingHeight;
		return pos;
	}
	else
	{
		auto CarryComp = getCarryComponent();
		if (CarryComp)
		{
			return CarryComp->getCarryingBindPos_Base();
		}
		return Rainbow::Vector3f(0, 0, 0);
	}


}


//?ж???????
bool ClientPlayer::isHomeLandGameMakerMode()
{
	if (GetWorldManagerPtr() && GetWorldManagerPtr()->getSpecialType() == HOME_GARDEN_WORLD)
	{
		//?淨??
		if (GetWorldManagerPtr()->getGameMode() == OWTYPE_GAMEMAKER_RUN)
		{
			return false;
		}
		//????(???????????SHORTCUTEX_START_INDEX??Container)
		else if (GetWorldManagerPtr()->getGameMode() == OWTYPE_GAMEMAKER)
		{
			return true;
		}
	}
	else
	{
		return false;
	}

	return false;
}


void ClientPlayer::updateToolModelTexture(int textureIndex /* = 0 */, bool sync /*= true*/)
{
	World* pWorld = getWorld();
	if (!pWorld)
		return;
	if (sync)
	{
		if (!pWorld->isRemoteMode())
		{
			PB_NotifyUpdateToolModelTextureHC notifyHC;
			notifyHC.set_uin(getUin());
			notifyHC.set_textureindex(textureIndex);

			GetGameNetManagerPtr()->sendBroadCast(PB_NOTIFY_UPDATE_TOOL_MODEL_TEXTURE_HC, notifyHC);
		}
		else
		{
			PB_NotifyUpdateToolModelTextureHC notifyHC;
			notifyHC.set_uin(getUin());
			notifyHC.set_textureindex(textureIndex);

			GetGameNetManagerPtr()->sendBroadCast(PB_NOTIFY_UPDATE_TOOL_MODEL_TEXTURE_HC, notifyHC);
		}
	}

	if (g_pPlayerCtrl && g_pPlayerCtrl == this && g_pPlayerCtrl->m_CameraModel)
		g_pPlayerCtrl->m_CameraModel->updateToolModelTexture(textureIndex);

	if (getBody())
		getBody()->updateToolModelTexture(textureIndex);
}

void ClientPlayer::doSomeChangeAfterSprinklerOnUse()
{
	int index = getCurShortcut() + getShortcutStartIndex();
	BackPackGrid* pgrid = getBackPack()->index2Grid(index);
	if (pgrid && pgrid->getItemID() == ITEM_SPRINKLER)
	{
		int leftCount = pgrid->getUserDataInt() - 1;
		pgrid->setUserDataInt(leftCount); //?????1

		if (leftCount < 1)
		{
			//??????? ?????????????????? ??????????????
			int oldDuration = pgrid->getDuration();
			if (getBackPack()->getGridNum(index) <= 1)
			{
				//getBackPack()->replaceItem(index, ITEM_SPRINKLER_EMPTY, 1, -1);
				GridCopyData data;
				data.resid = ITEM_SPRINKLER_EMPTY;
				data.num = 1;
				getBackPack()->replaceItem_byGridCopyData(data, index);
			}

			pgrid->setDuration(oldDuration);
		}
		else
		{
			//??????? ??????
			int texIndex = 5;
			MINIW::ScriptVM::game()->callFunction("GetSprinkleModelTextureIndex", "i>i", leftCount, &texIndex);
			updateToolModelTexture(texIndex);
		}

		getBackPack()->afterChangeGrid(index);
	}
}

void ClientPlayer::doSomeChangeAfterEmptySprinklerOnUse(int newItem /*= 0*/)
{
	if (newItem == 0)
		return;

	int index = getCurShortcut() + getShortcutStartIndex();
	BackPackGrid* pgrid = getBackPack()->index2Grid(index);
	if (pgrid && pgrid->getItemID() == ITEM_SPRINKLER_EMPTY)
	{
		if (getBackPack()->getGridNum(index) <= 1)
		{
			//???????????
			int leftCount = GetLuaInterfaceProxy().get_lua_const()->number_of_sprinklers;
			if (m_pWorld && !(GetWorldManagerPtr() && GetWorldManagerPtr()->isGodMode()))
			{
				//?淨?? ?????? ???-1
				int dur = pgrid->getDuration() - 1;
				//getBackPack()->replaceItem(index, newItem, 1, dur, 0, 0, (void*)leftCount);
				GridCopyData data;
				data.resid = newItem;
				data.num = 1;
				data.duration = dur;
				data.userdata = (void*)leftCount;
				getBackPack()->replaceItem_byGridCopyData(data, index);
			}
			else
			{
				//getBackPack()->replaceItem(index, newItem, 1, -1, 0, 0, (void*)leftCount);
				GridCopyData data;
				data.resid = newItem;
				data.num = 1;
				data.userdata = (void*)leftCount;
				getBackPack()->replaceItem_byGridCopyData(data, index);
			}
		}
	}
}


BackPackGrid* JsonToGridData(const jsonxx::Object& jsonObj)
{
	BackPackGrid* pGrid = ENG_NEW(BackPackGrid);

	// ???????
	int itemId = 0, itemNum = 0, durable = 0;
	if (jsonObj.has<jsonxx::Number>("Item_ID"))
	{
		itemId = jsonObj.get<jsonxx::Number>("Item_ID");
	}
	if (jsonObj.has<jsonxx::Number>("Item_Num"))
	{
		itemNum = jsonObj.get<jsonxx::Number>("Item_Num");
	}
	if (jsonObj.has<jsonxx::Number>("Item_Durable"))
	{
		durable = jsonObj.get<jsonxx::Number>("Item_Durable");
	}
	pGrid->setItem(itemId, itemNum, durable);

	// ????Index
	if (jsonObj.has<jsonxx::Number>("Item_Index"))
	{
		pGrid->setIndex(jsonObj.get<jsonxx::Number>("Item_Index"));
	}

	// ??????????
	if (jsonObj.has<jsonxx::Array>("Item_Enchant"))
	{
		jsonxx::Array jsonArr = jsonObj.get<jsonxx::Array>("Item_Enchant");
		for (unsigned int idx = 0; idx < jsonArr.size(); idx++)
		{
			pGrid->addEnchant(jsonArr.get<jsonxx::Number>(idx));
		}
	}

	return pGrid;
}

void ClientPlayer::initMobByEggData(long long objId, const jsonxx::Object& jsonObj)
{
	ClientActorMgr* pActorMgr = getActorMgr();
	if (pActorMgr == NULL) return;

	ClientMob* pCurrMob = pActorMgr->findMobByWID(objId);
	if (pCurrMob == NULL) return;

	// 1.?????????????
	if (jsonObj.has<jsonxx::Number>("TamedOwner"))
	{
		int ownerUin = jsonObj.get<jsonxx::Number>("TamedOwner");
		pCurrMob->setTamedOwnerUin(ownerUin);
	}

	// 2.???????: ????????????????
	if (jsonObj.has<jsonxx::String>("Base_Name"))
	{
		string displayName = jsonObj.get<jsonxx::String>("Base_Name");
		pCurrMob->setDisplayName(displayName);
	}

	MobAttrib* pAttrib = pCurrMob->getMobAttrib();
	if (pAttrib == NULL) return;

	if (jsonObj.has<jsonxx::Number>("Base_HP"))
	{
		float baseHp = jsonObj.get<jsonxx::Number>("Base_HP");
		pAttrib->setHP(baseHp, true);
	}
	if (jsonObj.has<jsonxx::Number>("Base_Food"))
	{
		float baseFood = jsonObj.get<jsonxx::Number>("Base_Food");
		pAttrib->setFood(baseFood);
	}

	// 3.??????
	if (jsonObj.has<jsonxx::Array>("Pack_Equips"))
	{
		const jsonxx::Array& mobEquips = jsonObj.get<jsonxx::Array>("Pack_Equips");
		for (unsigned int index = 0; index < mobEquips.size(); index++)
		{
			const jsonxx::Object& itemJson = mobEquips.get<jsonxx::Object>(index);
			int slotType = itemJson.get<jsonxx::Number>("Slot_Type");

			BackPackGrid* pTempGrid = JsonToGridData(itemJson);
			pAttrib->equip((EQUIP_SLOT_TYPE)slotType, pTempGrid);
			OGRE_DELETE(pTempGrid);
		}
	}

	// 4.???????
	PackContainer* backpack = pAttrib->getBags();
	if (jsonObj.has<jsonxx::Array>("Pack_Begs") && backpack != NULL)
	{
		const jsonxx::Array& mobbpack = jsonObj.get<jsonxx::Array>("Pack_Begs");
		for (unsigned int index = 0; index < mobbpack.size(); index++)
		{
			const jsonxx::Object& itemJson = mobbpack.get<jsonxx::Object>(index);
			BackPackGrid* pTempGrid = JsonToGridData(itemJson);
			BackPackGrid* pPackGrid = backpack->index2Grid(pTempGrid->getIndex());

			if (pPackGrid != NULL)
			{
				int itemId = pTempGrid->getItemID();
				int itemNum = pTempGrid->getNum();
				int durable = pTempGrid->getDuration();
				pPackGrid->setItem(itemId, itemNum, durable);

				// ???????
				pPackGrid->loadRunesAndEnchants(pTempGrid);
			}
			OGRE_DELETE(pTempGrid);
		}
	}

	// 5.???????顢???????
	ActorVillager* pVillager = dynamic_cast<ActorVillager*>(pCurrMob);
	if (pVillager != NULL)
	{
		if (jsonObj.has<jsonxx::Number>("Subs_FaceId"))
		{
			int faceId = jsonObj.get<jsonxx::Number>("Subs_FaceId");
			pVillager->setFaceId(faceId);
		}

		if (jsonObj.has<jsonxx::Number>("Subs_HairId"))
		{
			int hairId = jsonObj.get<jsonxx::Number>("Subs_HairId");
			int hairColor = jsonObj.get<jsonxx::Number>("Subs_HairColor");
			pVillager->setHairColor(hairColor, hairId);
		}

		if (jsonObj.has<jsonxx::Number>("Subs_Profession"))
		{
			int profession = jsonObj.get<jsonxx::Number>("Subs_Profession");
			pVillager->setProfession(profession);
			pVillager->interact(this); //???y???
		}
	}

	ActorHorse* pHorse = dynamic_cast<ActorHorse*>(pCurrMob);
	if (pHorse != NULL)
	{
		if (jsonObj.has<jsonxx::Number>("Subs_H_MaxHP"))
		{
			float maxHp = jsonObj.get<jsonxx::Number>("Subs_H_MaxHP");
			pAttrib->setMaxHP(maxHp);
		}

		if (jsonObj.has<jsonxx::Number>("Subs_H_Speed"))
		{
			int speedValue = jsonObj.get<jsonxx::Number>("Subs_H_Speed");
			pHorse->setRiddenLandSpeed(speedValue);
		}
		if (jsonObj.has<jsonxx::Number>("Subs_H_Jump"))
		{
			float jumpValue = jsonObj.get<jsonxx::Number>("Subs_H_Jump");
			pHorse->setMaxJumpHeight(jumpValue);
		}
	}
}

bool ClientPlayer::isStarStationTeleporting()
{
	return m_isStarStationTeleporting;
}

void ClientPlayer::setIsStarStationTeleporting(bool isTeleporting)
{
	m_isStarStationTeleporting = isTeleporting;
}

bool ClientPlayer::isExploiting()
{
	return m_isExploiting;
}
void ClientPlayer::setIsExploiting(bool exploiting)
{
	m_isExploiting = exploiting;
}


bool ClientPlayer::isCurrentActionState(const std::string& szState)
{
	if (nullptr != m_StateController)
	{
		if (m_StateController->getActionState() == szState)
		{
			return true;
		}
	}
	return false;
}

PlayerState* ClientPlayer::getCurrentActionStatePtr()
{
	return m_StateController ? m_StateController->getCurrentActionStatePtr() : nullptr;
}

PlayerState* ClientPlayer::getCurrentActionBodyStatePtr()
{
	return m_StateController ? m_StateController->getCurrentActionBodyStatePtr() : nullptr;
}

PlayerState* ClientPlayer::getCurrentMoveStatePtr()
{
	return m_StateController ? m_StateController->getCurrentMovementStatePtr() : nullptr;
}

PlayerState* ClientPlayer::getActionStatePtr(const std::string& szStateId)
{
#ifdef IWORLD_SERVER_BUILD
	//??????????????Σ???????????????????á?
	return nullptr != m_StateController ? m_StateController->findActionStatePtr(szStateId, (PlayerControl*)this) : nullptr;
#else
	return nullptr != m_StateController ? m_StateController->findActionStatePtr(szStateId) : nullptr;
#endif
}

PlayerState* ClientPlayer::getActionStatePtr(const char* szStateId)
{
#ifdef IWORLD_SERVER_BUILD

	return nullptr != m_StateController ? m_StateController->findActionStatePtr(szStateId, (PlayerControl*)this) : nullptr;
#else
	return nullptr != m_StateController ? m_StateController->findActionStatePtr(szStateId) : nullptr;
#endif
}

PlayerState* ClientPlayer::getActionBodyStatePtr(const std::string& szStateId)
{
#ifdef IWORLD_SERVER_BUILD
	//??????????????Σ???????????????????á?
	return nullptr != m_StateController ? m_StateController->findActionBodyStatePtr(szStateId, (PlayerControl*)this) : nullptr;
#else
	return nullptr != m_StateController ? m_StateController->findActionBodyStatePtr(szStateId) : nullptr;
#endif
}

PlayerState* ClientPlayer::getMoveStatePtr(const std::string& szStateId)
{
	return nullptr != m_StateController ? m_StateController->findMoveStatePtr(szStateId) : nullptr;
}

PlayerState* ClientPlayer::getLocoCurActionStatePtr(const std::string& szStateId)
{
	if (g_pPlayerCtrl == this)
	{
		return getCurrentActionStatePtr();
	}
	else
	{
		return getActionStatePtr(szStateId);
	}
}

PlayerState* ClientPlayer::getLocoCurActionStatePtr(const char* szStateId)
{
	if (g_pPlayerCtrl == this)
	{
		return getCurrentActionStatePtr();
	}
	else
	{
		return getActionStatePtr(szStateId);
	}
}

PlayerState* ClientPlayer::getLocoCurActionBodyStatePtr(const std::string& szStateId)
{
	if (g_pPlayerCtrl == this)
	{
		return getCurrentActionBodyStatePtr();
	}
	else
	{
		return getActionBodyStatePtr(szStateId);
	}
}

bool ClientPlayer::setActionState(const std::string& szStateId)
{
	if (nullptr != m_StateController)
	{
		return m_StateController->performActionTransition(szStateId);
	}
	else
	{
		return false;
	}
}

bool ClientPlayer::setMoveState(const std::string& szStateId)
{
	if (nullptr != m_StateController)
	{
		m_StateController->performMoveTransition(szStateId);
		return true;
	}
	else
	{
		return false;
	}
}

void ClientPlayer::toActionState(const std::string& szStateId)
{
	if ("" != szStateId && nullptr != m_StateController)
	{
		m_StateController->performActionTransition(szStateId);
	}
}

void ClientPlayer::toActionBodyState(const std::string& szStateId)
{
	if ("" != szStateId && nullptr != m_StateController)
	{
		m_StateController->performActionBodyTransition(szStateId);
	}
}

//void ClientPlayer::checFollowSituation()
//{
	//ActorFollow* actorfllow = (ActorFollow*) FindComp("ActorFollow");
	//actorfllow->checFollowSituation();
//}

//ActorUpdateFrequency* ClientPlayer::getUpdateFrequencyCom()
//{
//	if (m_pActorUpdateFrequency)
//		return m_pActorUpdateFrequency;
//	m_pActorUpdateFrequency = new EmptyUpdateFrequency(this);
//	return m_pActorUpdateFrequency;
//}





// 20210910?????????????????  codeby?? keguanqiang
void ClientPlayer::breakHorseInvisible()
{
	if (!m_pWorld || m_pWorld->isRemoteMode())
		return;

	auto RidComp = getRiddenComponent();
	if (RidComp)
	{
		auto* ride = RidComp->getRidingActor();
		ActorLiving* living = dynamic_cast<ActorLiving*>(ride);
		if (living)
			living->breakInvisible();
	}
}

// 20210910?????????  codeby?? keguanqiang
bool ClientPlayer::isInvisible()
{
	auto RidComp = getRiddenComponent();
	if (RidComp)
	{
		auto* ride = RidComp->getRidingActor();
		ActorLiving* living = dynamic_cast<ActorLiving*>(ride);
		if (living)
			return living->isInvisible();
	}


	return false;
}


void ClientPlayer::onClientUploadCheckInfo(int infoType, const std::string& detail)
{
	if (m_CheatData)
		m_CheatData->SetCheckData(infoType, detail);
}

void ClientPlayer::tryGetAdShopExtraAward(int awardId, int itemId, int count)
{
	bool success = true;
	if (m_CheatData)
		success = m_CheatData->CheckAdShopExtraAward(awardId, itemId, count);

	if (success)
	{
		gainItems(itemId, count);
	}
}

void ClientPlayer::tryExtractStoreItem(int storeIndex, int itemId, int count)
{
	bool success = true;
	if (m_CheatData)
		success = m_CheatData->CheckExtractStoreItem(storeIndex, itemId, count);

	if (success)
	{
		gainItems(itemId, count);
	}
}

//2021-09-14 codeby:chenwe ??????绥??????
bool ClientPlayer::playSkinAct(int act, const int inviteUin, const int acceptUin)
{
	//MpPlayerControl???????????
	return true;
}

//20210915 codeby: chenwei ??????????????
int ClientPlayer::getSkinActPlayerNum() {
	return 0;
}


void ClientPlayer::GetAdShopExtraItemReward(int awardId, int itemId, int count)
{
	if (m_pWorld && !m_pWorld->isRemoteMode())
	{
		gainItems(itemId, count);
	}
}

void ClientPlayer::ExtraStoreItem(int storeIndex, int itemId, int count)
{
	if (m_pWorld && !m_pWorld->isRemoteMode())
	{
		gainItems(itemId, count);
	}
}


//20210915 codeby: chenwei ???index???????
int ClientPlayer::getSkinActPlayerUinByIndex(int index)
{
	return 0;
}
//20210915 codeby: chenwei ??????????
void ClientPlayer::scanSkinActActorList(int range)
{
}

//******** codeby:chenwei ??????????
bool ClientPlayer::checkHasEnoughSpace2SkinAct(int act)
{
	//******** codeby:chenwei ??????????
	if (!m_pWorld)
		return false;

	WCoord myPos = getPosition();
	WCoord targetPos = getSkinActTargetPos(act, myPos);
	targetPos.y += getEyeHeight(); // ???????????????λ???м??????????????????P1???->P2???

	if (!checkIsDivByBlock(myPos, targetPos))
	{
		return true;
	}

	return false;
}


//******** codeby:chenwei ?????????
bool ClientPlayer::checkNearEnough2SkinAct(int targetUin, int act)
{
	if (NULL == m_pWorld)
		return false;

	ClientPlayer* targetPlayer = m_pWorld->getActorMgr()->ToCastMgr()->findPlayerByUin(targetUin);

	// ????
	WCoord originPos = this->getPosition();
	WCoord targetPos = targetPlayer->getPosition();
	float dis = originPos.distanceTo(targetPos);

	float distance = 10;
	if (act != 0 && act != 23 && act != 24)
	{
		int dir = 0;
		char posLen[16];
		SandboxCoreDriver::GetInstance().GetLua().CallFunctionM("RoleSkin_Helper", "GetActionPosById", "i>is", act, &dir, &posLen);
		distance = atof(strcat(posLen, "/0"));
		distance += 1;	//误差范围
	}

	//5????????????????绥??????
	if (dis <= distance * BLOCK_SIZE) {
		return true;
	}

	return false;
}

//20210914?????????????????e cody-by: wangyu
void ClientPlayer::sendActorInvite(int inviteType, int targetuin, int actId /* = 0 */, int inviterPosX, int inviterPosZ)
{
	//ge GetGameEventQue().postActorInviteEvent(inviteType, targetuin, actId);
	MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
		SetData_Number("inviteType", inviteType).
		SetData_Number("targetuin", targetuin).
		SetData_Number("actId", actId).
		SetData_Number("inviterPosX", inviterPosX).
		SetData_Number("inviterPosZ", inviterPosZ);
	if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
		MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GIE_UPDATE_ACTORINVITE", sandboxContext);
}

//2021-09-17 codeby:chenwe ???2a???????????????????????????o?????????????
bool ClientPlayer::checkCanPlaySkinAct()
{
	// 2021-09-24 codeby:wangyu ????????????????? / ??? ???2???????????????
	//******** codeby chenwei ?????????????
	auto RidComp = getRiddenComponent();
	if ((RidComp && RidComp->isRiding()) ||
		isCurrentActionState("Sleep") ||
		isRestInBed() ||
		isSittingInStarStationCabin() ||
		isRocketTeleport())
	{
		return false;
	}

	return true;
}

// ********????????????????????  codeby?? huangxin
void ClientPlayer::setInMusicClubArea(bool inArea)
{
	m_isInMusicClubArea = inArea;
}


// 20211014????????????????  codeby?? huangxin
bool ClientPlayer::getInMusicClubArea()
{
	return m_isInMusicClubArea;
}


//2021-09-25 codeby: luoshuai ???????????????
//2021-12-20 codeby: wangyang ???????????
void ClientPlayer::tickNewChat(const char* text, int bubble)
{
	std::string m_Str = text;
	int idx = m_Str.find("&N&A&M&E$1");
	if (idx != -1)
	{
		if (!m_pWorld->isRemoteMode())//主机  需要发送消息通知客机播放
		{
			PB_TopBrandHC topBrandHC;
			topBrandHC.set_targetuin(getUin());
			topBrandHC.set_brandname(m_Str);
			GetGameNetManagerPtr()->sendBroadCast(PB_TOP_BRAND_HC, topBrandHC);
		}
		else
		{
			// 因本协议消息不会经过敏感词筛选, 已被外挂利用, 不再由客客机发起, 业务需要时建议由主机触发  2024.07.17 by huanglin
			/*
			PB_TopBrandCH topBrandCH;
			topBrandCH.set_targetuin(getUin());
			topBrandCH.set_brandname(m_Str);
			GetGameNetManagerPtr()->sendToHost(PB_TOP_BRAND_CH, topBrandCH);
			*/
		}
	}
	else
	{
		bool isOpen = false;
		MINIW::ScriptVM::game()->callFunction("if_can_show_chat_bubble", ">b", &isOpen);
		if (!isOpen)
		{
			return;
		}

		if (!GetClientInfoProxy()->getAccountInfo())
			return;

		bool isChatOpen = GET_SUB_SYSTEM(PermitsSubSystem)->getPlayerPermits(GetClientInfoProxy()->getAccountInfo()->Uin, CS_PERMIT_ChatBubble) == 1;
		if (ROOM_SERVER_RENT == GetGameInfoProxy()->GetRoomHostType())
		{
			isChatOpen = GET_SUB_SYSTEM(PermitsSubSystem)->getCSPlayerPermits(GetClientInfoProxy()->getAccountInfo()->Uin, CS_PERMIT_ChatBubble) == 1;
		}

		//工具暂时打开
#if defined(BUILD_MINI_EDITOR_APP) || (OGRE_PLATFORM == OGRE_PLATFORM_WIN32 && defined(STUDIO_SERVER))
		isChatOpen = true;
#endif

		if (!m_isInMusicClubArea && !isChatOpen)
		{
			return;
		}
	}
	m_chatContent = text;
	//2021-12-20 codeby: wangyang ???????????
	m_chatBubble = bubble;
	m_haveShowTime = 0;
	m_needReport = true;
}

void ClientPlayer::setTopBrand(const char* text)
{
	m_chatContent = text;
	m_chatBubble = 0;
	m_haveShowTime = 0;
	m_needReport = false;
}

std::string ClientPlayer::getUserData(const std::string& key)
{
	auto it = m_mapUserDatas.find(key);
	if (it != m_mapUserDatas.end())
	{
		return it->second;
	}

	return "";
}

bool ClientPlayer::hasUserData(const std::string& key)
{
	return m_mapUserDatas.find(key) != m_mapUserDatas.end();
}

bool ClientPlayer::removeUserData(const std::string& key)
{
	auto it = m_mapUserDatas.find(key);
	if (it != m_mapUserDatas.end())
	{
		m_mapUserDatas.erase(it);
		m_needSaveUserData = true;
		SLOG(INFO) << "removeUserData uin=" << getUin() << ", key=" << key;
		return true;
	}
	return false;
}

void ClientPlayer::setUserData(const std::string& key, const std::string& value)
{
	auto it = m_mapUserDatas.find(key);
	if (it != m_mapUserDatas.end() && it->second == value)
	{
		return;
	}

	m_mapUserDatas[key] = value;
	m_needSaveUserData = true;
	SLOG(INFO) << "setUserData uin=" << getUin() << ", key=" << key << ", val=" << value;
}

void ClientPlayer::removeAllUserData()
{
	if (!m_mapUserDatas.empty())
	{
		m_mapUserDatas.clear();
		m_needSaveUserData = true;
		SLOG(INFO) << "removeAllUserData uin=" << getUin();
	}
}

std::map<std::string, std::string> ClientPlayer::getAllUserData()
{
	return m_mapUserDatas;
}

//2021-09-25 codeby: luoshuai 检查限制时间是否到了
void ClientPlayer::checkShowMusicClubChatBubble(float dtime) {
	if (m_chatContent == "")
	{
		return;
	}
	if (m_haveShowTime < 5.0f)
	{
		m_haveShowTime += dtime;
		if (m_haveShowTime >= m_showLimitTime)
		{
			m_chatContent = "";
		}
	}
	//2021-12-20 codeby: wangyang ???????????
	showMusicClubChatBubble(m_chatContent.c_str(), m_chatBubble, dtime);
}
//2021-09-25 codeby: luoshuai ?ж???????????????????
//2021-12-20 codeby: wangyang ???????????
void ClientPlayer::showMusicClubChatBubble(const char* text, int bubble, float tickTime)
{
	auto body = getBody();
	if (!body)
	{
		return;
	}
	if (!GetClientInfoProxy()->getAccountInfo())
		return;

	std::string m_Str = text;
	int idx = m_Str.find("&N&A&M&E$1");
	if (idx != -1)
	{
		body->setDispayMusicClubChatBubble();
		body->setMusicClubChatBubbleText(text, true, bubble);
	}
	else
	{
		//判断是否在音乐厅区域
		bool isInMusicClub = m_isInMusicClubArea;
		//判断房主是否开启房间内聊天窗口开关
		bool isChatOpen = false;
		PermitsSubSystem* pPermitSystem = GET_SUB_SYSTEM(PermitsSubSystem);
		if (pPermitSystem != nullptr)
		{
			auto accInfo = GetClientInfoProxy()->getAccountInfo();
			int accUin = accInfo == nullptr ? 0 : accInfo->Uin;
			isChatOpen = pPermitSystem->getPlayerPermits(accUin, CS_PERMIT_ChatBubble) == 1;
			if (ROOM_SERVER_RENT == GetGameInfoProxy()->GetRoomHostType())
			{
				isChatOpen = pPermitSystem->getCSPlayerPermits(accUin, CS_PERMIT_ChatBubble) == 1;
			}
		}

		//若在音乐厅区域则必须显示，不在则需要看开关
		bool isGo = isInMusicClub ? true : isChatOpen;

		//工具暂时打开
#if defined(BUILD_MINI_EDITOR_APP) || (OGRE_PLATFORM == OGRE_PLATFORM_WIN32 && defined(STUDIO_SERVER))
		isGo = true;
#endif

		//工具制作的地图暂时打开
		if (GetWorldManagerPtr()->isNewSandboxNodeGame())
		{
			isGo = true;
		}

		body->setDispayMusicClubChatBubble();
		bool isShow = (strcmp(text, "") == 0 || !isInShowBubbleDistane() || !isGo) ? false : true;
		//2021-12-20 codeby: wangyang 会员聊天气泡

		body->setMusicClubChatBubbleText(text, isShow, bubble, tickTime);
		if (isChatOpen && m_needReport && isShow && strcmp(text, "") != 0)
		{
			//埋点数据上报
			if (m_pWorld->isRemoteMode())
			{
				GetMiniReportMgrProxy()->standReportEvent("1001", "MINI_CHAT_BUBBLE", "-", "view");
			}
			else
			{
				GetMiniReportMgrProxy()->standReportEvent("1003", "MINI_CHAT_BUBBLE", "-", "view");
			}
		}
	}
	m_needReport = false;
}

//2021-09-25 codeby: luoshuai ?ж???????????????
bool ClientPlayer::isInShowBubbleDistane()
{
	//WCoord playe = g_pPlayerCtrl->getPosition();
	ACCONTINFO* info = GetClientInfoProxy()->getAccountInfo();
	if (info == NULL)
		return false;
	ClientPlayer* player = getActorMgr()->findPlayerByUin(info->Uin);
	if (player) {
		WCoord playerblockpos = player->getPosition();
		WCoord myPos = getPosition();
		return (playerblockpos - myPos).length() <= 8 * BLOCK_SIZE;
	}
	return false;
}



//******** codeby:chenwei ???????????λ????? ******** codeby:chenwei ????????????????????
WCoord ClientPlayer::getSkinActTargetPos(int act, const WCoord& selfPos, bool isReverse)
{
	WCoord defaultPos(0, 0, 0);
	int posDir = 0;
	float dis = 0;
	char posLen[16];
	SandboxCoreDriver::GetInstance().GetLua().CallFunctionM("RoleSkin_Helper", "GetActionPosById", "i>is", act, &posDir, &posLen);
	dis = atof(strcat(posLen, "/0"));
	if (posDir == 0 || dis == 0)
	{
		return defaultPos;
	}
	//????????????
	Rainbow::Vector3f vecLookDir = getLocoMotion()->getLookDir();
	vecLookDir = MINIW::Normalize(vecLookDir);
	//??????????????????
	Rainbow::Vector3f dir = CrossProduct(Rainbow::Vector3f(0, 1.0f, 0), vecLookDir);
	dir = MINIW::Normalize(dir);

	int dirSymbol = isReverse ? -1 : 1;
	if (posDir == 3)// ?
	{
		vecLookDir *= dis * BLOCK_SIZE * dirSymbol;
		WCoord offsetPos(vecLookDir);
		return selfPos + offsetPos;
	}
	else if (posDir == 4)// ??
	{
		vecLookDir *= -dis * BLOCK_SIZE * dirSymbol;
		WCoord offsetPos(vecLookDir);
		return selfPos + offsetPos;
	}
	else if (posDir == 5)// ??
	{
		dir *= -dis * BLOCK_SIZE * dirSymbol;
		WCoord offsetPos(dir);
		return selfPos + offsetPos;
	}
	else if (posDir == 6)// ??
	{
		dir *= dis * BLOCK_SIZE * dirSymbol;
		WCoord offsetPos(dir);
		return selfPos + offsetPos;
	}

	return defaultPos;
}

bool ClientPlayer::checkIsDivByBlock(const WCoord& pos1, const WCoord& pos2)
{
	if (!m_pWorld)
		return false;

	// ???????????????λ???м??????????????????P1???->P2???
	MINIW::WorldRay ray;
	ray.m_Origin = pos1.toWorldPos();
	ray.m_Dir = pos2.toVector3() - pos1.toVector3();
	ray.m_Range = ray.m_Dir.Length();
	ray.m_Dir /= ray.m_Range;

	// ??Χ????????????????赲 //2021-10-20 codeby:wangyu ?????赲?????????
	IntersectResult inter_result;
	if (m_pWorld->pickGround(ray, &inter_result, PICK_METHOD_SOLID))
	{
		return true;
	}
	return false;
}
//******** codeby:chenwei ?????綯????????????б????????
bool ClientPlayer::checkIsDivideByBlock(int targetUin)
{
	if (!getActorMgr())
		return false;

	ClientPlayer* player = getActorMgr()->findPlayerByUin(targetUin);
	if (!player)
		return false;

	WCoord myPos = getPosition();
	WCoord targetPos = player->getPosition();
	targetPos.y += player->getEyeHeight();

	// 射线检测，防止到跳舞位置中间有其他障碍物，出发点：P1脚底->P2眼睛
	return checkIsDivByBlock(myPos, targetPos);
}

//20210929 codeby:chenwei 联机装扮互动的动作中断处理
void ClientPlayer::onAnimInterrupt()
{
	//******** codeby:chenwei 添加添加健壮性判断和完善逻辑
	if (!getBody() || !getWorld())
	{
		return;
	}

	//ֹͣ�Լ��Ķ���
	int oldAct = -1;
	getBody()->setAct(-1);
	getBody()->setCurAnim(-1, 0);
	stopMotion(30000);

	//20210927 codeby:chenwei ����װ�绥���Ķ����жϴ����޸�
	int partnerUin = getSkinPartnerUin();
	if (partnerUin > 0)
	{
		setSkinPartnerUin(NULL);

		ClientPlayer* partnerPlayer = getActorMgr()->findPlayerByUin(partnerUin);
		//ֹͣ���Ķ���
		if (partnerPlayer && partnerPlayer->getBody())
		{
			ActorBody* parnerBody = partnerPlayer->getBody();
			oldAct = parnerBody->getActID();
			parnerBody->setAct(-1);
			parnerBody->setCurAnim(-1, 0);
			partnerPlayer->stopMotion(30000);
			partnerPlayer->setSkinPartnerUin(NULL);
		}

		const PlayActDef* def = GetDefManagerProxy()->getPlayActDef(oldAct);
		if (def)
		{
			if (partnerPlayer) {
				auto soundComp1 = partnerPlayer->getSoundComponent();
				if (soundComp1)
				{
					soundComp1->stopSoundByTrigger(def->Sound.c_str(), true);
				}
			}
			auto soundComp = getSoundComponent();
			if (soundComp)
			{
				soundComp->stopSoundByTrigger(def->Sound.c_str(), true);
			}
		}

		if (getWorld()->getMpActorMgr())
		{
			if (m_pWorld->isRemoteMode())
			{
				PB_ActorStopSkinActCH actorStopSkinActCH;
				actorStopSkinActCH.set_actorid1(getUin());
				actorStopSkinActCH.set_actorid2(partnerUin);
				GetGameNetManagerPtr()->sendToHost(PB_ACTOR_STOP_SKIN_ACT_CH, actorStopSkinActCH);
			}
			else
			{
				PB_ActorStopSkinActHC actorStopSkinActHC;
				actorStopSkinActHC.set_actorid1(getUin());
				actorStopSkinActHC.set_actorid2(partnerUin);
				getWorld()->getMpActorMgr()->sendMsgToTrackingPlayers(PB_ACTOR_STOP_SKIN_ACT_HC, actorStopSkinActHC, this);
			}

		}
	}
}

bool ClientPlayer::loadFromDataServer(long long owid, int uin, void* buf, int buflen, bool needload/* =true */, int specialType/* = NORMAL_WORLD*/)
{
	bool hasrole = loadFromDataServerReal(owid, uin, buf, buflen, needload, specialType);
	m_NewPlayerFlag = !hasrole; // ???????????????
	return hasrole;
}

/*
	本函数为云服存储数据方式专用, 加载buf中的角色数据
	因精细化存储/加载需求, 将loadActorCommon中的加载项全部拆分到此处用配置项来控制加载
	两个主要选项为 1.重置地图/不重置地图 2.保存角色数据(可勾选保存项)/不保存角色数据 策划要求如下:
	未勾选保存玩家数据: 不保存玩家数据
	否则
		重置地图: 保存并加载列表【勾选的数据】
		不重置地图: 保存加载【勾选的数据】和【不在列表的数据】（如位置，技能cd等）
	实现中, 当保存数据被勾选, 角色数据全量保存但部分加载, 未勾选则不保存角色数据 2021.12.24 by 黄林
*/

bool ClientPlayer::loadFromDataServerReal(long long owid, int uin, void* buf, int buflen, bool needload, int specialType/* = NORMAL_WORLD*/)
{
	if (buf == NULL || buflen <= 0) {
		return false;
	}
	bool isNeedSyncArchive = false;
	SandboxResult sandboxResult = SandboxEventDispatcherManager::GetGlobalInstance().Emit("ArchiveManager_getNeedSyncArchive");
	if (sandboxResult.IsExecSuccessed())
	{
		isNeedSyncArchive = sandboxResult.GetData_Bool();
	}
	if (!isNeedSyncArchive) {
		m_CurMapID = 0;
		return false;
	}
	flatbuffers::Verifier verifier((const uint8_t*)buf, buflen);
	if (!FBSave::VerifyActorPlayerBuffer(verifier)) {
		// SLOG(WARNING) << "loadFromDataServerReal, verify buffer failed." << buflen << ", uin = " << uin;
#ifdef IWORLD_SERVER_BUILD
		init_failed = true;
		if (GameNetManager::getInstance()->getConnection())
			GameNetManager::getInstance()->getConnection()->kickoffMember(uin, ClientErrCode::ERR_CODE_KICK);
		jsonxx::Object error;
		error << "len" << buflen;
		error << "msg" << "flatbuffers::Verifier failed";
		Rainbow::GetICloudProxyPtr()->InfoLog(uin, owid, "player_enter", error);
#endif		
		return false;
	}
#ifdef IWORLD_SERVER_BUILD
	init_failed = false;
#endif
	const FBSave::ActorPlayer* wrole = FBSave::GetActorPlayer(buf);
	if (wrole == NULL) {
		return false;
	}

	if (m_ObjId > GUEST_UIN && m_ObjId != uin)
	{
		LOG_SEVERE("Wrong uin: objid=%d, uin=%d", int(m_ObjId), uin);
		return false;
	}

	if (!needload)
	{
		//getAccountHorseComponent()->setCurAccountHorse(wrole->curhorse());
		if (wrole->curhorse() != 0)
		{
			auto comp = sureAccountHorseComponent();//by__Logo
			if (comp)
			{
				comp->setCurAccountHorse(wrole->curhorse());
			}
		}
		onSetCurShortcutWithFB(wrole);
		return false;
	}

	auto srcdata = wrole->basedata();
	if (srcdata == NULL)
		return false;

	auto loco_montion = getLocoMotion();
	if (!loco_montion || !srcdata->motion())
		return true;
	WCoord pos = Coord3ToWCoord(srcdata->pos());
	loco_montion->gotoPosition(pos, srcdata->yaw(), srcdata->pitch());
	//m_FallDistance = srcdata->falldist();
	loco_montion->m_Motion = Rainbow::Vector3f(srcdata->motion()->x(), srcdata->motion()->y(), srcdata->motion()->z());
	m_Flags = srcdata->flags();
	m_LiveTicks = srcdata->liveticks();
	m_llMasterObjId = srcdata->masterobjid();

	PlayerAttrib* attr = getPlayerAttrib();
	ConstAtLua* lua_const = GetLuaInterfaceProxy().get_lua_const();
	attr->setBasicMaxHP(lua_const->hpmax);
	attr->setBasicOverflowHP(lua_const->hpoverflow);

	attr->setBasicMaxStrength(lua_const->strengthmax);
	attr->setBasicOverflowStrength(lua_const->strengthoverflow);
	bool ret = false;
	auto attinfo = srcdata->attinfo();
	if (attinfo) {
		ret = GetClientInfoProxy()->getNeedSyncPlayerAttr((int)PLAYER_ATTR_MOVESPEED);
		if (ret)
		{
			if (!(attinfo->walkspeed() < 0.0f))
				attr->setSpeedAtt(Actor_Walk_Speed, attinfo->walkspeed());
		}
		ret = GetClientInfoProxy()->getNeedSyncPlayerAttr((int)PLAYER_ATTR_ATKANDDEF);
		if (ret)
		{
			if (!(attinfo->punchattack() < 0.0f))
				attr->setAttackBaseLua(ATTACK_TYPE::ATTACK_PUNCH, attinfo->punchattack());

			if (!(attinfo->rangeattack() < 0.0f))
				attr->setAttackBaseLua(ATTACK_TYPE::ATTACK_RANGE, attinfo->rangeattack());

			if (!(attinfo->punchdefense() < 0.0f))
				attr->setArmorBaseLua(ATTACK_TYPE::ATTACK_PUNCH, attinfo->punchdefense());

			if (!(attinfo->rangedefense() < 0.0f))
				attr->setArmorBaseLua(ATTACK_TYPE::ATTACK_RANGE, attinfo->rangedefense());
		}
	}
	ret = GetClientInfoProxy()->getNeedSyncPlayerAttr((int)PLAYER_ATTR_HUNGER);
	if (ret) {
		attr->m_FoodLevel = wrole->food();
		attr->m_FoodSatLevel = 0;
	}
	ret = GetClientInfoProxy()->getNeedSyncPlayerAttr((int)PLAYER_ATTR_HP);
	if (ret) {
		if (attinfo && attinfo->maxhp() > 0.0f)
			attr->setBasicMaxHP(attinfo->maxhp());  //��ǰ���õ���setMaxHP, ���ᱻbasicMaxHP����,����ô浵��ͨ������setBasicMaxHP

		attr->setHP(wrole->hp(), true);
	}
	ret = GetClientInfoProxy()->getNeedSyncPlayerAttr((int)PLAYER_ATTR_STRENGTH);
	if (ret) {
		attr->setStrength(wrole->strength());
	}
	std::vector<int> _pack_to_record;
	ret = GetClientInfoProxy()->getNeedSyncPlayerAttr((int)PLAYER_ATTR_EQUIPCUT);
	if (ret) {
		LoadPackContainer(getBackPack(), EQUIP_START_INDEX, wrole->equips());
		_pack_to_record.push_back(EQUIP_START_INDEX);
	}
	ret = GetClientInfoProxy()->getNeedSyncPlayerAttr((int)PLAYER_ATTR_BAGCONF);
	if (ret) {
		LoadPackContainer(getBackPack(), BACKPACK_START_INDEX, wrole->backpack());
		_pack_to_record.push_back(BACKPACK_START_INDEX);
	}
	ret = GetClientInfoProxy()->getNeedSyncPlayerAttr((int)PLAYER_ATTR_SHORTCUT);
	if (ret)
	{
		LoadPackContainer(getBackPack(), SHORTCUT_START_INDEX, wrole->shortcut());
		LoadPackContainer(getBackPack(), SHORTCUTEX_START_INDEX, wrole->shortcutEx());
		LoadPackContainer(getBackPack(), SHORTCUT_START_INDEX_EDIT, wrole->shortcutEdit());
		_pack_to_record.push_back(SHORTCUT_START_INDEX);
		_pack_to_record.push_back(SHORTCUTEX_START_INDEX);
	}
	ret = GetClientInfoProxy()->getNeedSyncPlayerAttr((int)PLAYER_ATTR_EXTBAGCONF);
	if (ret) {
		LoadPackContainer(getBackPack(), EXT_BACKPACK_START_INDEX, wrole->extbackpack());
		_pack_to_record.push_back(EXT_BACKPACK_START_INDEX);
	}

	BackPackStringRecorder _recorder(7);
	for (auto iter = _pack_to_record.begin(); iter != _pack_to_record.end(); ++iter)
	{
		PackContainer* pack = dynamic_cast<PackContainer*>(getBackPack()->getContainer(*iter));
		if (pack)
			pack->visitPack(&_recorder);
	}
	_recorder.end();
	Rainbow::GetICloudProxyPtr()->packLog(getUin(), "pack_load", _recorder.m_Index2ItemRecord.str(), true);
	ret = GetClientInfoProxy()->getNeedSyncPlayerAttr((int)PLAYER_ATTR_STARNUM);
	if (ret)
	{
		attr->setExp(wrole->exp());
	}
	ret = GetClientInfoProxy()->getNeedSyncPlayerAttr((int)PLAYER_ATTR_EFFECT);
	if (ret)
	{
		// TODO liusijia 临时代码，删除现网多余的装备属性
		std::map<int, const FBSave::ActorBuff*> addedBuffs;
		if (wrole->buffs()->size() > 2000)
		{
			for (size_t i = 0; i < wrole->buffs()->size(); i++)
			{
				auto b = wrole->buffs()->Get(i);
				addedBuffs[b->buffid()] = b;
			}
			jsonxx::Object log;
			log << "buffcount" << wrole->buffs()->size();
			log << "addedcount" << addedBuffs.size();
			Rainbow::GetICloudProxyPtr()->InfoLog(uin, 0, "fix_buff_length", log);
		}
		if (addedBuffs.size() > 0)
		{
			for (auto& pair : addedBuffs)
			{
				auto b = pair.second;
				attr->addBuffOnLoad(b->buffid(), b->bufflv(), b->ticks());
			}
		}
		else
		{
			for (size_t i = 0; i < wrole->buffs()->size(); i++)
			{
				auto b = wrole->buffs()->Get(i);
				attr->addBuffOnLoad(b->buffid(), b->bufflv(), b->ticks());
			}
		}
	}
	ret = GetClientInfoProxy()->getNeedSyncPlayerAttr((int)PLAYER_ATTR_LEVELEXP);
	if (ret)
	{
		SetLevelMode(wrole->sumlevelexp(), wrole->curlevelexp(), wrole->curlevel());
	}
	m_ServerPosCmp = loco_montion->getPosition();
	m_ServerYawCmp = loco_montion->m_RotateYaw;
	m_ServerPitchCmp = loco_montion->m_RotationPitch;
	ActorLocoMotion::CheckMotionValid(loco_montion->m_Motion);

	LockCtrlComponent* lockctrl = GetComponent<LockCtrlComponent>();
	lockctrl->SetLastPasswd(wrole->lastpasswd());

	/* 未勾选数据根据[是否重置地图]选项来决定是否加载
	  当地图配置为[重置地图]则不加载下面的数据
	*/
	if (GetWorldManagerPtr() && GetWorldManagerPtr()->isSurviveMode()) {
		// 云服下冒险模式保存所有 2022.04.20 by huanglin
	}
	else if (!GetWorldManagerPtr() || !GetWorldManagerPtr()->m_RuleMgr || GetWorldManagerPtr()->m_RuleMgr->getRuleOptionVal(GMRULE_SAVEMODE) == 1)
	{
		m_CurMapID = 0;
		return true;
	}

	if (attinfo)
	{
		if (!(attinfo->jumppower() < 0.0f))
			attr->setSpeedAtt(Actor_Jump_Speed, attinfo->jumppower());

		if (!(attinfo->swimspeed() < 0.0f))
			attr->setSpeedAtt(Actor_Swim_Speed, attinfo->swimspeed());

		if (attinfo->attacktype() >= 0)
			attr->setAttackType(attinfo->attacktype());

		if (attinfo->immunetype() > 0) {
			attr->resetImmuneType();
			attr->setImmuneType(attinfo->immunetype(), true);
		}

		if (attinfo->dodge() >= 0)
			setAiInvulnerableProb(attinfo->dodge());

		if (attinfo->settingatt() != ENABLE_INITVALUE)
			setActionAttrState(attinfo->settingatt(), true);

		if (!(attinfo->hprecover() < 0.0f))
			attr->setHPRecover(attinfo->hprecover());
	}

	int version = wrole->version();
	if (!loadActorCommon(wrole->basedata())) return false;

	if (m_ObjId > GetClientInfoProxy()->getWdescGuest_Uin() && m_ObjId != uin)
	{
		LOG_SEVERE("Wrong uin: objid=%d, uin=%d", int(m_ObjId), uin);
		// free(buf);
		return false;
	}
	if (version > 4611 || wrole->uinencoded()) //0.18.3,   
	{
		unsigned char curenc[16];
		int nchar = CalUinEncoded(curenc, (unsigned int)m_ObjId);
		for (int i = 0; i < nchar; i++)
		{
			if (curenc[i] != wrole->uinencoded()->Get(i))
			{
				LOG_SEVERE("Wrong encoded");
				return true;
			}
		}
	}
	SetObjId(uin);
	//m_ObjId = uin;
	//m_RidingActor = wrole->riding();

	if (wrole->riding() != 0)
	{
		auto RidComp = sureRiddenComponent();
		if (RidComp)
		{
			RidComp->setRidingActorObjId(wrole->riding());
		}
	}

	m_LoginNum = wrole->loginnum();
	m_CurMapID = wrole->mapid();

	attr->m_FoodLevel = wrole->food();
	attr->m_FoodSatLevel = 0;

	attr->setBasicMaxHP(lua_const->hpmax);
	attr->setBasicOverflowHP(lua_const->hpoverflow);
	attr->setHP(wrole->hp(), true);

	attr->setBasicMaxStrength(lua_const->strengthmax);
	attr->setBasicOverflowStrength(lua_const->strengthoverflow);
	attr->setStrength(wrole->strength());
	attr->setOxygen(wrole->oxygen());
	attr->m_UsedStamina = wrole->usedstamina();
	attr->setExp(wrole->exp());

	attr->toggleUseCompatibleStrength(wrole->useStrength());
	attr->setStrengthFoodShowState(wrole->strengthFoodShowState());
	attr->setStarDebuffTime(wrole->stardebufftime());
	attr->setStarDebuffStage(wrole->stardebuffstage());

	if (wrole->computerOrderUsed())
	{
		for (size_t i = 0; i < wrole->computerOrderUsed()->size(); i++)//保存电脑指令使用次数
		{
			attr->addComputerOrderIsUsed(wrole->computerOrderUsed()->Get(i));
		}
	}
	m_DieTimes = wrole->deathTimes();

	if (version == 0)
	{
		attr->m_FoodLevel *= 5.0f;
		attr->m_FoodSatLevel *= 5.0f;
		//attr->m_Life *= 5.0f;
		attr->initHP(attr->getHP() * 5.0f);
	}
	if (!attr->m_Attribs.empty()) memset(&attr->m_Attribs[0], 0, attr->m_Attribs.size() * sizeof(AttribModified));
	// for (size_t i = 0; i < wrole->buffs()->size(); i++)
	// {
	// 	auto b = wrole->buffs()->Get(i);
	// 	attr->addBuffOnLoad(b->buffid(), b->bufflv(), b->ticks());
	// }

	LoadPackContainer(getBackPack(), EQUIP_START_INDEX, wrole->equips());
	LoadPackContainer(getBackPack(), BACKPACK_START_INDEX, wrole->backpack());
	LoadPackContainer(getBackPack(), SHORTCUT_START_INDEX, wrole->shortcut());
	LoadPackContainer(getBackPack(), SHORTCUTEX_START_INDEX, wrole->shortcutEx());
	LoadPackContainer(getBackPack(), WITHHOLD_BACKPACK_START_INDEX, wrole->withhold_backpack());

	if (wrole->craft_tasks())
	{
		for (size_t i = 0; i < wrole->craft_tasks()->size(); i++)
		{
			auto t = wrole->craft_tasks()->Get(i);
			getCraftingQueue()->loadAddTask(t->craftingId(), t->count(), t->ticksPerItem(), t->remainingTicks());
		}
	}

	onSetCurShortcutWithFB(wrole);
	// 拷贝高级创造老地图的快捷栏数据到新快捷栏, 71680 = 1.24.0
	// code by 2023.3.3 chenshaobin
	int gameVersionInt = GetClientInfoProxy()->GetGameVersionIntForClientInfo();
	if (g_WorldMgr && (g_WorldMgr->isUGCMode()) && gameVersionInt >= 71680 && version < 71680)
	{
		CopyShortcutForUGC(getBackPack());
	}

	//getAccountHorseComponent()->load(wrole);	
	if (wrole->horses() || wrole->curhorse() != 0)
	{
		auto comp = sureAccountHorseComponent();//by__Logo
		if (comp)
			comp->load(wrole);
	}

	SandboxContext context;
	context.SetData_Userdata("userdata", "wrole", const_cast<FBSave::ActorPlayer*> (wrole));
	Event().Emit("loadFromFile", context);

	//m_SkillCD.clear();
	//if (wrole->skillcds())
	//{
	//	for (size_t i = 0; i < wrole->skillcds()->size(); i++)
	//	{
	//		auto src = wrole->skillcds()->Get(i);
	//		m_SkillCD[src->itemid()] = src->cd();
	//	}
	//}
	//m_pSkillCDComp->load(wrole->skillcds());

	auto skillCDComp = m_SkillCDComponent;
	if (skillCDComp) skillCDComp->load(wrole->skillcds());

	m_UnlockItems.clear();
	if (wrole->unlockitems())
	{
		if (wrole->unlockitems()->size() <= 2048)
		{
			m_UnlockItems.resize(wrole->unlockitems()->size());
			for (size_t i = 0; i < wrole->unlockitems()->size(); i++)
			{
				int itemid = wrole->unlockitems()->Get(i);
				m_UnlockItems[i] = itemid;
			}
		}
	}

	if (wrole->worldtimes())
	{
		for (int i = 0; i < (int)wrole->worldtimes()->size(); i++)
		{
			m_WorldTimes[i] = wrole->worldtimes()->Get(i);
		}
	}

	auto landingpoints = wrole->landingpoints();
	if (landingpoints)
	{
		for (size_t i = 0; i < landingpoints->size(); i++)
		{
			m_LandingPoints[i] = Coord3ToWCoord(landingpoints->Get(i));
		}
	}

	if (wrole->containerpasswords())
	{
		int realsize = 0;
		for (int i = 0; i < (int)wrole->containerpasswords()->size(); i++)
		{
			auto* containerpassword = wrole->containerpasswords()->Get(i);
			if (containerpassword)
			{
				setContainersPassword(Coord3ToWCoord(containerpassword->pos()), containerpassword->password());
				realsize++;
				if (realsize >= 10)
				{
					break;
				}
			}
		}
	}
	//getActorBindVehicle()->Bind(wrole->vehicleobj(), Coord3ToWCoord(wrole->vehiclecoord()), m_pWorld && !m_pWorld->isRemoteMode());	
	auto actorBindVehicle = getActorBindVehicle();//by__Logo
	if (actorBindVehicle)
		actorBindVehicle->Bind(wrole->vehicleobj(), Coord3ToWCoord(wrole->vehiclecoord()), m_pWorld && !m_pWorld->isRemoteMode());

	if (m_pTaskMgr)
		m_pTaskMgr->load(wrole);

	/*m_NpcShopInfo.clear();
	if (wrole->npcshopinfo()) {
		char filepath[256];
		sprintf(filepath, "%s/w%lld/mods/", rootpath.c_str(), owid);
		std::map<int, std::vector<int> > mNpcShopChangeInfo;
		GetDefManagerProxy()->getNpcShopChangeConfigStatus(filepath, mNpcShopChangeInfo);
		for (size_t i = 0; i < wrole->npcshopinfo()->size(); i++)
		{
			auto data = wrole->npcshopinfo()->Get(i);
			auto itShop = mNpcShopChangeInfo.find(data->shopid());
			if (itShop != mNpcShopChangeInfo.end()) {//reset this shop's sku
				if (itShop->second.size() == 0)
					continue;

				int j = 0;
				for (; j < itShop->second.size(); j++) {
					if (itShop->second[j] == data->skuid()) {
						break;
					}
				}

				if (j != itShop->second.size()) { continue; }
			}
			std::map<int, NpcShopInfo> mNpcShopInfo;
			mNpcShopInfo.clear();

			if (m_NpcShopInfo.find(data->shopid()) != m_NpcShopInfo.end()) {
				mNpcShopInfo = m_NpcShopInfo[data->shopid()];
			}

			NpcShopInfo skuinfo;
			skuinfo.iLeftCount = data->leftcount();
			skuinfo.iEndTime = data->endtime();

			mNpcShopInfo[data->skuid()] = skuinfo;
			m_NpcShopInfo[data->shopid()] = mNpcShopInfo;
		}

		if (mNpcShopChangeInfo.size() > 0) {
			mNpcShopChangeInfo.clear();
			GetDefManagerProxy()->setNpcShopChangeConfigStatus(filepath, mNpcShopChangeInfo);
		}
	} */

	//applyEquips();
	applyEquips(EQUIP_WEAPON);
	updateEquips();

	if (wrole->attr())
	{
		bool isModelvolume = attr->hasStatusEffect(STATUS_EFFECT_MODELVOLUME);
		if (wrole->attr()->modelscale() > 0 && !isModelvolume)
			setScale(wrole->attr()->modelscale());

		if (GetWorldManagerPtr() && GetWorldManagerPtr()->m_RuleMgr)
			GetWorldManagerPtr()->m_RuleMgr->setLockViewMode(wrole->attr()->lockviemode());

		if (wrole->attr()->customscale() > 0 && !isModelvolume)
			setCustomScale(wrole->attr()->customscale());
	}

	if (GetWorldManagerPtr() && GetWorldManagerPtr()->m_RuleMgr && GetWorldManagerPtr()->m_RuleMgr->getRuleOptionVal(GMRULE_SAVEMODE) != 1) {
		auto itemattlist = wrole->itemattaction();
		if (itemattlist && attr) {
			for (int i = 0; i < (int)itemattlist->size(); i++) {
				attr->addItemAttAction(itemattlist->Get(i));
			}
		}
	}

	SetLevelMode(wrole->sumlevelexp(), wrole->curlevelexp(), wrole->curlevel());
	//m_pPetFollowListComp->load(wrole->tamedFollows());
	if (wrole->tamedFollows() && wrole->tamedFollows()->size() > 0)
	{
		auto petFollowListComp = surePetFollowListComponent();
		if (petFollowListComp) petFollowListComp->load(wrole->tamedFollows());
	}

	// ???????????|????????????????????????????????? codeby chenzihang
	attr->setHP(wrole->hp(), true);
	attr->setStrength(wrole->strength());

	LoadObjectChildAndComponent(wrole->scriptcomponent(), wrole->ugccomponents(), wrole->children());

	//m_CarryingActor = wrole->carrying();  ???????????????????????????????
	// free(buf);
	return true;
}

bool ClientPlayer::onLoadUserData(const void* data, int len)
{
#ifdef IWORLD_SERVER_BUILD
	miniw::user_data pb;
	if (!pb.ParseFromArray(data, len))
	{
		LOG_WARNING("onLoadUserData pb ParseFromArray failed");
		return false;
	}

	m_mapUserDatas.clear();
	for (size_t i = 0; i < pb.kvs_size(); i++)
	{
		auto kv = pb.kvs(i);
		m_mapUserDatas[kv.key()] = kv.val();
		SLOG(INFO) << "onLoadUserData uin=" << getUin() << ", key=" << kv.key() << ", val=" << kv.val();
	}
#endif
	return true;
}

bool ClientPlayer::saveUserData(long long owid)
{
#ifdef IWORLD_SERVER_BUILD
	if (!m_needSaveUserData)
	{
		return true;
	}

	miniw::user_data pb;
	for (auto it = m_mapUserDatas.begin(); it != m_mapUserDatas.end(); ++it)
	{
		auto kv = pb.add_kvs();
		kv->set_key(it->first);
		kv->set_val(it->second);
		SLOG(INFO) << "saveUserData uin=" << getUin() << ", key=" << it->first << ", val=" << it->second;
	}

	std::string reqData;
	pb.SerializeToString(&reqData);

	g_zmqMgr->SaveDataToDataServer(miniw::RTMySQL::SaveCustomData, reqData.c_str(),
		reqData.length(), owid, getUin());
	m_needSaveUserData = false;
#endif

	return true;
}

int ClientPlayer::tryBuyAdNpcGood(int tabId, int goodId)
{
	if (MINIW::ScriptVM::game())
	{
		int isWareHouse = 0, priceType = 0, price = 0, itemId = 0, itemNum = 0;
		MINIW::ScriptVM::game()->callFunction("GetAdShopGoodInfo", "ii>iiiii", tabId, goodId,
			&isWareHouse, &priceType, &price, &itemId, &itemNum);
		if (itemId == 0)
			return 0;

		bool success = false;
		if (priceType == 3)
		{
			int exp = -price * EXP_STAR_RATIO;
			PlayerAttrib* playerAttr = getPlayerAttrib();
			if (playerAttr)
			{
				int expBefore = playerAttr->getExp();
				if (exp > 0 || (exp < 0 && expBefore >= abs(exp)))
				{
					playerAttr->addExp(exp);
					success = true;
				}
			}
		}
		else if (priceType > 40000000) // 即类型4消耗道具的
		{
			priceType -= 40000000;
			if (getBackPack() && getBackPack()->getItemCountInNormalPack(priceType) >= price)
			{
				getBackPack()->removeItemInNormalPack(priceType, price);
				success = true;
			}
		}

		if (success && isWareHouse == 0)
		{
			ClientPlayer::gainItems(itemId, itemNum, 1);
		}

		return success;
	}
	return 0;
}

// 迷你豆等消耗流程已通过 使用tabid即goodid可避免玩家恶意修改目标道具
void ClientPlayer::onBuyAdNpcGood(int tabId, int goodId)
{
	if (MINIW::ScriptVM::game())
	{
		int isWareHouse = 0, priceType = 0, price = 0, itemId = 0, itemNum = 0;
		MINIW::ScriptVM::game()->callFunction("GetAdShopGoodInfo", "ii>iiiii", tabId, goodId,
			&isWareHouse, &priceType, &price, &itemId, &itemNum);
		if (itemId == 0)
			return;

		if (isWareHouse == 0)
		{
			ClientPlayer::gainItems(itemId, itemNum, 1);
		}
	}
}

bool ClientPlayer::onLoadTechTree(const void* data, int len)
{
#ifdef IWORLD_SERVER_BUILD
	miniw::tech_workbench pb;
	if (!pb.ParseFromArray(data, len))
	{
		LOG_WARNING("onLoadUserData pb ParseFromArray failed");
		return false;
	}

	m_mapTechWorkbench.clear();
	for (size_t i = 0; i < pb.tech_trees_size(); i++)
	{
		auto t = pb.tech_trees(i);
		miniw::tech_tree* tree = new miniw::tech_tree();
		tree->CopyFrom(t);
		m_mapTechWorkbench[tree->level()] = tree;
		SLOG(INFO) << "onLoadTechTree uin=" << getUin() << ", tree=" << tree->level();
	}
#endif
	return true;
}
bool ClientPlayer::saveTechTree(long long owid)
{
#ifdef IWORLD_SERVER_BUILD

	if (!m_needSaveTechTree)
	{
		return true;
	}

	miniw::tech_workbench pb;
	for (auto it = m_mapTechWorkbench.begin(); it != m_mapTechWorkbench.end(); ++it)
	{
		auto tree = pb.add_tech_trees();
		tree->CopyFrom(*it->second);
		 
		SLOG(INFO) << "saveTechTree uin=" << getUin() << ", tree=" << tree->level();
	}

	std::string reqData;
	pb.SerializeToString(&reqData);

	g_zmqMgr->SaveDataToDataServer(miniw::RTMySQL::SaveTechTree, reqData.c_str(),reqData.length(), owid, getUin());
	m_needSaveTechTree = false;
#endif
	return true;
}
void ClientPlayer::unlockTechNode(int page_level, int tree_id, int node_id) {
#ifdef IWORLD_SERVER_BUILD

	miniw::tech_tree* tree_ptr = nullptr;
	auto it = m_mapTechWorkbench.find(page_level);
	if (it != m_mapTechWorkbench.end())
	{
		tree_ptr = it->second;
	}
	else  
	{
		// 向map中添加新的tech_tree
		tree_ptr = new miniw::tech_tree();
		tree_ptr->set_level(page_level); 
		tree_ptr->set_root_tree_id(tree_id);
		tree_ptr->set_tree_name("level_"+std::to_string(page_level));
		m_mapTechWorkbench[page_level] = tree_ptr;
	}

	miniw::tech_tree_node* node = nullptr;
	for (int i = 0; i < tree_ptr->tree_nodes_size(); i++)
	{
		auto* n = tree_ptr->mutable_tree_nodes(i);
		if (n->id() == node_id)
		{
			node = n;
			node->set_is_unlocked(true);
			break;
		}
	}

	if (!node)
	{
		node = tree_ptr->add_tree_nodes();
		node->set_id(node_id);
		node->set_level(page_level);
		node->set_is_unlocked(true);
	}

	m_needSaveTechTree = true;
#endif

}

miniw::tech_tree_node* ClientPlayer::getTechTreeNode(int level, int node_id)
{
	miniw::tech_tree_node* node = nullptr;
#ifdef IWORLD_SERVER_BUILD
	auto* tree = getTechTree(level);
	if (tree) {
		for (int i=0; i < tree->tree_nodes().size(); i++) {
			miniw::tech_tree_node* p = tree->mutable_tree_nodes(i);
			if (p->id() == node_id) {
				node = p;
				break;
			}
		}
	}
#endif
	return node;
}


miniw::tech_tree* ClientPlayer::getTechTree(int level) {
	miniw::tech_tree* tree = nullptr;
#ifdef IWORLD_SERVER_BUILD
	auto it = m_mapTechWorkbench.find(level);
	if (it != m_mapTechWorkbench.end())
	{
		tree = it->second; 
	}

#endif
	return tree;
}

vector<miniw::tech_tree*> ClientPlayer::getTechTrees( int page_level, int tree_id)
{
	vector<miniw::tech_tree*> list;
#ifdef IWORLD_SERVER_BUILD
	auto it = m_mapTechWorkbench.find(page_level);
	if (it != m_mapTechWorkbench.end())
	{
		auto* tree = it->second;

		if (tree->root_tree_id() == tree_id || tree_id == 0)
		{
			list.push_back(tree);
		}
	}
	
#endif
	return list;
}

void ClientPlayer::getAchievementAward(int taskId)
{
	if (!m_pWorld->isRemoteMode())
	{
		if (!GetClientInfoProxy()->IsAchievementManagerValid()) {
			jsonxx::Object log;
			log << "task" << taskId;
			log << "mode" << g_WorldMgr->getGameMode();
			Rainbow::GetICloudProxyPtr()->InfoLog(getUin(), 0, "cheat_task_reward", log);
			return;
		}
#ifdef IWORLD_SERVER_BUILD
		// 0未解锁 1解锁未激活 2激活未完成 3激活且完成
		// 降低玩家刷任务奖励的范围 2022.10.01 by huanglin
		if (GetClientInfoProxy()->getAchievementState(getUin(), taskId) < 2)
		{
			jsonxx::Object log;
			log << "task" << taskId;
			log << "state" << GetClientInfoProxy()->getAchievementState(getUin(), taskId);
			Rainbow::GetICloudProxyPtr()->InfoLog(getUin(), 0, "cheat_task_reward", log);
			return;
		}
#endif
		if (GetClientInfoProxy()->getAchievementRewardState(getUin(), taskId) == 2) // 已领取
		{
			jsonxx::Object log;
			log << "task" << taskId;
			log << "rewarded" << GetClientInfoProxy()->getAchievementRewardState(getUin(), taskId);
			Rainbow::GetICloudProxyPtr()->InfoLog(getUin(), 0, "cheat_task_reward", log);
			return;
		}
		AchievementDef* def = nullptr;
		MNSandbox::GetGlobalEvent().Emit<AchievementDef**, int>("AchievementManager_getAchievementDef", &def, taskId);
		if (def == nullptr)
			return;

		// 判断不了state 客户端维护的，暂不处理
		//int state = g_AchievementMgr->getAchievementRewardState(getObjId(), taskId);
		//if (state != REWARD_CAN_RECEIVE)
		//	return;

		for (int i = 0; i < 2; ++i)
		{
			int type = def->RewardType[i];
			int itemId = def->RewardID[i];
			int num = def->RewardNum[i];
			// 道具
			if (type == 0)
			{
				if (itemId > 0)
				{
					gainItems(itemId, num);
				}
			}
			else if (type == 1)
			{
				AddStar(num);
			}
		}
		GetClientInfoProxy()->setAchievementRewardState(getUin(), taskId, 2);
	}
	else
	{
		// 通知服务器
		PB_GetAchievementAwardCH getAward;
		getAward.set_taskid(taskId);
		GetGameNetManagerPtr()->sendToHost(PB_ACHIEVEMENT_AWARD_CH, getAward);
	}
}

void ClientPlayer::callPierceCommand()
{
	setFlying(true);
	m_isInPierceMode = true;
	m_checkboxscale = -1;

	m_BoundHeight = -100;
	m_BoundWidth = -100;

	if (getLocoMotion())
	{
		getLocoMotion()->setBound(-100, -100);
		// ????????????????????
		if (m_pWorld)
		{
			static_cast<PlayerLocoMotion*>(getLocoMotion())->detachPhysActor();
			static_cast<PlayerLocoMotion*>(getLocoMotion())->attachPhysActor();
		}
	}
}

void ClientPlayer::callPierceCloseCommand()
{
	setFlying(false);
	m_isInPierceMode = false;
	m_checkboxscale = 1;

	m_BoundHeight = 180;
	m_BoundWidth = 60;

	if (getLocoMotion())
	{
		float scale = 1.0f * getCustomScale();
		if (getLocoMotion()->m_BoundHeight != (m_BoundHeight * scale) || getLocoMotion()->m_BoundSize != (m_BoundWidth * scale))
		{
			getLocoMotion()->setBound((int)(m_BoundHeight * scale), (int)(m_BoundWidth * scale));
			// ????????????????????
			if (m_pWorld)
			{
				static_cast<PlayerLocoMotion*>(getLocoMotion())->detachPhysActor();
				static_cast<PlayerLocoMotion*>(getLocoMotion())->attachPhysActor();
			}
		}
	}
}

void ClientPlayer::tryStopPianoSoundAndPaticle()
{
	if (isMoveControlActive())
	{
		if (getSitting())
			changeMoveFlag(IFC_Sit, false);
	}
	else
		setSitting(false);
	EffectManager* m_EffectMgr = m_pWorld->getEffectMgr();
	if (m_EffectMgr != NULL)
	{
		if (this->pianoPaticleName != "")
		{
			WCoord center = BlockCenterCoord(this->pParticlePos);
			const char* pPath = this->pianoPaticleName.c_str();
			if (m_EffectMgr->getParticleOnPos(center, pPath) != NULL)
			{
				m_EffectMgr->stopParticleEffect(pPath, center);
			}
			this->setPianoPaticleName("");
		}
		if (this->pianoSoundName != "")
		{
#ifdef CHINA_SEQ_USED
			this->stopAnim(SEQ_PIANO_PLAY);

			PB_StopActCH stopActCH;
			stopActCH.set_playeruin(g_pPlayerCtrl->getUin());
			stopActCH.set_actid(SEQ_PIANO_PLAY);
			GameNetManager::getInstance()->sendToHost(PB_StopAct_CH, stopActCH);
			WCoord pSoundPos = this->pSoundPos;
			const char* fname = this->pianoSoundName.c_str();
			m_EffectMgr->stopPosSound(pSoundPos, fname);
			this->setPianoSoundName("");
#endif
		}
	}
}

#pragma region ActorBodyer

void ClientPlayer::showSaddle(int id)
{
	if (id < 0)
	{
		return;
	}

	ActorBody* actorbody = getBody();
	if (actorbody)
	{
		actorbody->setShowSaddle(id);

		if (actorbody->getEntity() && actorbody->getEntity()->GetMainModel())
		{
			for (int i = 1; i <= MAX_SADDLE; i++)
			{
				actorbody->getEntity()->GetMainModel()->ShowSkin(s_SaddleNames[i - 1], i == id);
			}
		}
	}
}

void ClientPlayer::showNecklace(int id)
{
	ActorBody* actorbody = getBody();
	if (actorbody)
	{
		actorbody->setShowNecklace(id);
		if (actorbody->getEntity() && actorbody->getEntity()->GetMainModel())
		{
			for (int i = 1; i <= MAX_NECKLACE; i++)
			{
				actorbody->getEntity()->GetMainModel()->ShowSkin(s_NecklaceNames[i - 1], i == id);
			}
		}
	}

}

void ClientPlayer::showRake(int id)
{
	ActorBody* actorbody = getBody();
	if (actorbody)
	{
		actorbody->setShowRake(id);
		if (actorbody->getEntity() && actorbody->getEntity()->GetMainModel())
		{
			for (int i = 1; i <= MAX_RAKE; i++)
			{
				actorbody->getEntity()->GetMainModel()->ShowSkin(s_RakeNames[i - 1], i == id);
			}
		}
	}
}

void ClientPlayer::setFaceExpression(int i)
{
	if (m_CustomSkins.size() > 0)
	{
		//exchangePartFace(i, 2, true);
		if (getBody())
		{
			getBody()->exchangePartFace(i, 2, true);
		}
		return;
	}

	if (getBody())
	{
		getBody()->setFaceExpression(i);
	}
}

void ClientPlayer::setFaceExpression(std::string face)
{
	int index = -1;
	const char* facestr[] = { "face_100108", "face_100130", "face_100100", "face_100107", "face_100155", "face_100159", "face_100158" };
	for (int i = 0; i < sizeof(facestr) / sizeof(char*); i++)
	{
		if (face == facestr[i])
		{
			index = i;
		}
	}
	if (index >= 0)
		setFaceExpression(index);
}

bool ClientPlayer::playActForTriggerForActorbody(int act)
{
	ActorBody* actorbody = getBody();
	auto def = GetDefManagerProxy()->getTriggerActDef(act);
	if (def)
	{

		if (actorbody->getCurAnim(0) == SEQ_PLAY_ACT)
			actorbody->setCurAnim(-1, 0);
		if (actorbody->getEntity())
			actorbody->getEntity()->StopMotion(30000);

		actorbody->setActID(-1);
		actorbody->setActTriggerID(act);

		if (!actorbody->isAvatarModel() && actorbody->getInitTex() && actorbody->getFaceMesh())
		{
			actorbody->getFaceMesh()->SetTexture("g_DiffuseTex", actorbody->getInitTex());
		}

		//添加玩家第一人称播放动画
		if (m_CameraModel && def->FpsActID > 0)
		{
			m_CameraModel->playHandAnim(def->FpsActID);
		}

		// ???????
		if (g_WorldMgr && !g_WorldMgr->isRemote())
			playActionOnTrigger(0, actorbody->getActTriggerID());
		return true;
	}
	return false;
}

//只做主机同步
bool ClientPlayer::skillPlayAnim(int tpsanimid, int fpsanimid, int isLoop, int playLayer)
{
	ActorLiving::skillPlayAnim(tpsanimid, fpsanimid, isLoop, playLayer);
	ActorBody* actorbody = getBody();
	if (actorbody)
	{
		//添加玩家第一人称播放动画
		if (m_CameraModel && fpsanimid > 0)
		{
			if (!m_CameraModel->hasAnimPlaying(fpsanimid))
			{
				m_CameraModel->playHandAnim(fpsanimid, isLoop, 1.0, playLayer);
			}
		}
		return true;
	}
	return false;
}

bool ClientPlayer::skillStopAnim(int tpsanimid, int fpsanimid, bool isReset)
{
	ActorLiving::skillStopAnim(tpsanimid, fpsanimid, isReset);
	ActorBody* actorbody = getBody();
	if (actorbody)
	{
		//添加玩家第一人称停止播放动画
		if (m_CameraModel && fpsanimid > 0)
		{
			m_CameraModel->stopHandAnim(fpsanimid, isReset);
		}
		return true;
	}
	return false;
}

bool ClientPlayer::skillPlayToolAnim(int animid)
{
	ActorLiving::skillPlayToolAnim(animid);
	if (m_CameraModel && animid > 0)
	{
		m_CameraModel->playItemAnim(animid);
		return true;
	}
	return false;
}
bool ClientPlayer::skillStopToolAnim(int animid)
{
	ActorLiving::skillStopToolAnim(animid);
	if (m_CameraModel && animid > 0)
	{
		m_CameraModel->stopItemAnim(animid);
		return true;
	}
	return false;
}
void ClientPlayer::skillSetChargeMove(float chargemove)
{
	PlayerLocoMotion* locomotion = dynamic_cast<PlayerLocoMotion*>(getLocoMotion());
	if (locomotion)
	{
		locomotion->setSkillChargeMove(chargemove);
	}
	if (m_pWorld && !m_pWorld->isRemoteMode())
	{
		jsonxx::Object context;
		context << "uin" << getUin();
		context << "chargemove" << chargemove;
		SandBoxManager::getSingleton().sendToClient(getUin(), "PB_SKILLSETCHARGEMOVE_HC", context.bin(), context.binLen());
	}
}


void ClientPlayer::playWeaponAnim(int aniID)
{
	ClientActor::PlayWeaponAnim(aniID);
}

void ClientPlayer::stopWeaponAnim(int aniID)
{
	ClientActor::StopWeaponAnim(aniID);
}

bool ClientPlayer::hasWeaponAnimPlaying(int aniID)
{
	return ClientActor::HasWeaponPlaying(aniID);
}

#pragma endregion
void ClientPlayer::exploreChunkView(int range, int blockType)
{
	if (m_pWorld && m_pWorld->getCurMapID() == 0)
	{
		WCoord pos = getPosition();
		m_pWorld->getChunkProvider()->findTerrain(CoordDivBlock(pos.x), CoordDivBlock(pos.z), blockType, range, getUin());
	}
}

float ClientPlayer::getArmor()
{
	if (!getAttrib())
	{
		return 0;
	}
	return getAttrib()->getArmor();
}

float ClientPlayer::getPerseverance()
{
	if (!m_PlayerAttrib)
	{
		return 0;
	}
	return m_PlayerAttrib->getPerseverance();
}

void ClientPlayer::OnKillMonster(int monster_id)
{
#ifdef IWORLD_SERVER_BUILD
	if (m_pvpActivityConfig.mobNeedRecord.count(monster_id)) {
		++m_pvpRecordMobKill[monster_id];
	}
#endif
}

void ClientPlayer::SetOnPlatform(ClientActor* pActor, bool bForceInRemote)
{
	bool bSet = false;
	if (!hasUIControl())
	{
		if (bForceInRemote)
		{
			// 客机不能自己设置， 除非是主机通知过来的（bFromNetMsg 为 true）
			bSet = true;
		}
	}
	else
	{
		// 主机可以直接设置
		bSet = true;
	}
	if (bSet)
	{
		m_pBindPlatform = pActor;
	}
}

Rainbow::Transform* ClientPlayer::GetTransform()
{
	PlayerLocoMotion* pLoco = static_cast<PlayerLocoMotion*>(getLocoMotion());
	if (pLoco)
		return pLoco->GetTransform();

	return ClientActor::GetTransform();
}

Rainbow::GameObject* ClientPlayer::GetGameObject()
{
	PlayerLocoMotion* pLoco = static_cast<PlayerLocoMotion*>(getLocoMotion());
	if (pLoco)
		return pLoco->GetGameObject();

	return ClientActor::GetGameObject();
}

bool ClientPlayer::CanExposePosToOther()
{
	return m_exposePos;
}

void ClientPlayer::SetExposePosToOther(bool b)
{
	if (m_exposePos != b)
	{
		m_exposePos = b;
		if (m_pWorld->isRemoteMode())
		{
			PB_ExposePosChangeCH data;
			data.set_isexpose(m_exposePos);
			if (GameNetManager::getInstance())
			{
				GameNetManager::getInstance()->sendToHost(PB_CHANGEEXPOSEPOS_CH, data);
			}
		}

		if (GameNetManager::getInstance() && GameNetManager::getInstance()->isHost())
		{
			if (g_WorldMgr)
				g_WorldMgr->signChangedToSync(getUin(), BIS_EXPOSE_POS);
		}
	}
}

void ClientPlayer::discardEquip(int index, int num)
{
	if (!m_PlayerAttrib)
	{
		return;
	}
	if (index < 0 || index >= MAX_EQUIP_SLOTS)
	{
		return;
	}
	int gridIndex = m_PlayerAttrib->equipSlot2Index((EQUIP_SLOT_TYPE)index);
	discardItem(gridIndex, num);
}

void ClientPlayer::SetHideEquipAvatar(bool bShow)
{
	ActorBody* actorbody = getBody();
	if (actorbody)
	{
		actorbody->SetHideEquipAvatar(bShow);
	}

	if (m_UIViewBody && !GetActorBodySafeHandle()->IsVaild(m_UIViewBody))
	{
		m_UIViewBody = nullptr;
	}

#if MODELVIEW_DECOUPLE_FROM_ACTORBODY
	if (m_UIViewBody && m_UIViewBody->getIsAttachModelView())
#else
	if (m_UIViewBody && m_UIViewBody->getAttachedModelView())
#endif
	{
		m_UIViewBody->SetHideEquipAvatar(bShow);
	}
}

bool ClientPlayer::GetHideEquipAvatarState()
{
	ActorBody* actorbody = getBody();
	if (!actorbody)
		return false;

	return actorbody->GetHideEquipAvatarState();
}

void ClientPlayer::doThornBall(ClientActor* actor)
{
	if (m_pWorld->isRemoteMode())
	{
		return;
	}
	if (actor == NULL || getLocoMotion() == NULL)
	{
		return;
	}

	ClientMob* mob = dynamic_cast<ClientMob*>(actor);
	ClientPlayer* player = dynamic_cast<ClientPlayer*>(actor);
	if (!mob && !player)
	{
		return;
	}

	SandboxCoreSubsystem* module = PluginManager::GetInstancePtr()->FindSubsystem<SandboxCoreSubsystem>();
	if (module == nullptr)
	{
		return;
	}

	auto myComponent = getThornBallComponent();
	auto thornComponent = actor->getThornBallComponent();
	int ownerCount = myComponent ? myComponent->getThornAnchorNum() : 0;
	int targetCount = thornComponent ? thornComponent->getThornAnchorNum() : 0;
	if (ownerCount <= 0 && targetCount <= 0)
	{
		return;
	}

	myComponent = sureThornBallComponent();
	thornComponent = actor->sureThornBallComponent();
	if (myComponent == nullptr || thornComponent == nullptr)
	{
		return;
	}

	//不相互同时间计算反弹
	if (module->IsRebounds(getObjId()))
	{
		//反弹
		myComponent->reboundsAttackedRound(3, myComponent->checkCrashDir());
		if (mob)
		{
			//反弹
			thornComponent->reboundsAttackedRound(3, thornComponent->checkCrashDir(), true);
		}
		module->PushReboundsID(getObjId());
	}

	//不同时间计算粘黏
	bool isSticky = module->IsSticky(getObjId());
	if (isSticky)
	{
		//我不存在
		if (ownerCount <= 0)
		{
			return;
		}
		LivingLocoMotion* living1 = dynamic_cast<LivingLocoMotion*>(getLocoMotion());
		LivingLocoMotion* living2 = dynamic_cast<LivingLocoMotion*>(actor->getLocoMotion());
		if (living1 == nullptr || living2 == nullptr)
		{
			return;
		}
		//都没跑
		if (!living1->m_InRun && !living2->m_InRun)
		{
			return;
		}
		//对方一定在跑
		if (!living1->m_InRun)
		{
			//对方有
			if (targetCount > 0)
			{
				return;
			}
		}
		//粘附到对方身上
		myComponent->removeThornBallModel(ownerCount);
		int ballNum = thornComponent->getThronBallNum();
		if (targetCount < ballNum)
		{
			module->SetStickyID(actor->getObjId());
			//实际少数量
			int actualCount = ballNum - targetCount;
			for (int i = 0; i < actualCount; i++)
			{
				if (ownerCount <= 0)
				{
					break;
				}
				ownerCount--;
				ClientActorThornBall::createThornEntity(actor);
			}
		}
		//剩下的
		myComponent->dropThornBall(ownerCount);
	}

}

int ClientPlayer::openPackGift(int nouse_id, int iShortCutIdx, int iPackID, std::string& addlist)
{
	// 由主机随机生成物品并将获得物品的结果通知给玩家 2022.09.26 by huanglin
	int iRet = 0;
	int iItemID = getBackPack()->getGridItem(iShortCutIdx);
	ItemDef* itemDef = GetDefManagerProxy()->getAutoUseForeignID(iItemID);

	if (!itemDef) { return -1; }
	PackGiftDef* def = GetDefManagerProxy()->getPackGiftDef(itemDef->ID);
	if (!def) { return -1; }
	BackPack* backpack = getBackPack();
	if (!backpack) { return -1; }

	int iCostItemId = def->iCostItemInfo / 1000;
	int iCostItemNum = def->iCostItemInfo % 1000;

	if (!m_pWorld->isRemoteMode()) {
		ItemDef* costItemDef = GetDefManagerProxy()->getAutoUseForeignID(iCostItemId);
		iCostItemId = costItemDef ? costItemDef->ID : 101;
		if (def->iNeedCostItem != 0) {//需要消耗该物品才能开启礼包
			int neednum = iItemID == iCostItemId ? (iCostItemNum + 1) : iCostItemNum;
			if (def->iCostItemInfo <= 0) {
				return -1;
			}
			else if (backpack->getItemCountInNormalPack(iCostItemId) < neednum) {
				return 1;//物品不足
			}
		}

		//检查背包空间是否充足
		int needgridnum = (def->iPackType == 1 ? def->iMaxOpenNum : def->vPackItemList.size());
		PackContainer* backpackContainer = (PackContainer*)backpack->getContainer(BACKPACK_START_INDEX);
		PackContainer* shortcutContainer = (PackContainer*)backpack->getContainer(backpack->getShortcutStartIndex());

		int iOwnEmptyGrid = 0;
		bool bEnough = false;
		for (size_t i = 0; i < backpackContainer->m_Grids.size(); i++) {
			BackPackGrid& grid = backpackContainer->m_Grids[i];
			if (!grid.def || grid.def->ID == 0) {
				iOwnEmptyGrid++;
				bEnough = (iOwnEmptyGrid >= needgridnum);
				if (bEnough) { break; }
			}
		}

		if (needgridnum > iOwnEmptyGrid) {
			for (size_t i = 0; i < shortcutContainer->m_Grids.size(); i++) {
				BackPackGrid& grid = shortcutContainer->m_Grids[i];
				if (!grid.def || grid.def->ID == 0) {
					iOwnEmptyGrid++;
					bEnough = (iOwnEmptyGrid >= needgridnum);
					if (bEnough) { break; }
				}
			}
		}

		//背包空间不足
		if (!bEnough) { return 3; }

		ItemDef* addItemDef;
		std::map<int, int> addMap;
		std::stringstream ss("");
		int itemid, itemnum, iteminfo;
		if (def->iPackType == 1) {//随机类型，可随机def->iMaxOpenNum次，每次概率获得其中一种物品
			int OpenNum = def->iMaxOpenNum > 0 ? def->iMaxOpenNum : 1;
			if (OpenNum >= def->vPackItemList.size())
			{
				for (int i = 0; i < (int)def->vPackItemList.size(); i++) {
					iteminfo = def->vPackItemList[i].iItemInfo;
					itemid = iteminfo / 1000;
					itemnum = iteminfo % 1000;
					addItemDef = GetDefManagerProxy()->getAutoUseForeignID(itemid);
					itemid = addItemDef ? addItemDef->ID : 101;
					iteminfo = itemid * 1000 + itemnum;
					addMap[itemid] += itemnum;
				}
			}
			else
			{
				int Total = 10000; //随机总概率
				std::map<int, int> vecitem;// 不重复获取的道具 已随机到的道具 不再获得
				for (int icount = 0; icount < OpenNum; icount++)
				{
					int iRand = (rand() % Total) + 1;
					int iRatio = 0;
					for (int i = 0; i < (int)def->vPackItemList.size(); i++)
					{
						if (def->iRepeat == 0 && vecitem.find(i) != vecitem.end()) { continue; } //已发的道具
						iRatio += def->vPackItemList[i].iRatio;
						if (iRatio > Total) { return -1; } //概率设置出错
						if (iRand <= iRatio) //在概率范围
						{
							iteminfo = def->vPackItemList[i].iItemInfo;
							itemid = iteminfo / 1000;
							itemnum = iteminfo % 1000;
							addItemDef = GetDefManagerProxy()->getAutoUseForeignID(itemid);
							itemid = addItemDef ? addItemDef->ID : 101;
							iteminfo = itemid * 1000 + itemnum;
							if (addMap.find(itemid) == addMap.end()) //插件内可以设置相同的道具
							{
								addMap[itemid] = itemnum;
							}
							else
							{
								addMap[itemid] += itemnum;
							}
							if (def->iRepeat == 0) //不可重复
							{
								Total -= def->vPackItemList[i].iRatio;
								vecitem[i] = 1;
							}
							break;
						}
					}
				}
			}
		}
		else {//固定类型，获得里面全部物品
			for (int i = 0; i < (int)def->vPackItemList.size(); i++) {
				iteminfo = def->vPackItemList[i].iItemInfo;
				itemid = iteminfo / 1000;
				itemnum = iteminfo % 1000;
				addItemDef = GetDefManagerProxy()->getAutoUseForeignID(itemid);
				itemid = addItemDef ? addItemDef->ID : 101;
				iteminfo = itemid * 1000 + itemnum;

				addMap[itemid] += itemnum;
			}
		}
		iRet = updatePackGiftItemChange(iShortCutIdx, def->iNeedCostItem != 0 ? iCostItemId : 0, iCostItemNum, addMap);
		jsonxx::Array items;
		if (iRet == 0 && !addMap.empty())
		{
			for (auto iter = addMap.begin(); iter != addMap.end(); ++iter) {
				jsonxx::Object item;
				item << "itemid" << iter->first;
				item << "num" << iter->second;
				items << item;
			}
			notifyGameInfo2Self(PLAYER_NOTIFYINFO_GETMULTIITEM, 0, 0, items.json().c_str());
		}
	}
	else {
		PB_PackGiftNotifyItemChgCH reqDataCH;
		reqDataCH.set_shortcutidx(iShortCutIdx);
		reqDataCH.set_costiteminfo(iCostItemId * 1000 + iCostItemNum);
		GameNetManager::getInstance()->sendToHost(PB_PACKGIFT_NOTIFYITEMCHANGE_HC, reqDataCH);
	}

	return iRet;
}

#ifdef IWORLD_SERVER_BUILD	
void _loadPack(PackContainer* pack, const flatbuffers::Vector<flatbuffers::Offset<FBSave::ItemIndexGrid>>* items, GridVisitor* recorder)
{
	if (!pack || !items)
		return;
	pack->clear();
	for (size_t i = 0; i < items->size(); i++)
	{
		auto item = items->Get(i);
		BackPackGrid* grid = pack->index2Grid(item->index());
		if (grid) {
			grid->load(item);
		}
	}
	pack->visitPack(recorder);
}

/**
 * @brief 覆盖恢复玩家的背包数据
 *
 * @param buf 角色数据
 * @param buf_len 数据长度
 * @param recorder 遍历获取玩家被覆盖后的背包
 * @return int 错误码 0-成功 其它-失败
 */
int ClientPlayer::RecoverBagPack(const void* buf, int buf_len, GridVisitor* recorder) {
	flatbuffers::Verifier verifier((const uint8_t*)buf, buf_len);
	if (!FBSave::VerifyActorPlayerBuffer(verifier)) {
		SLOG(WARNING) << "RecoverBagPack, verify buffer failed." << buf_len << ", uin = " << getUin();
		return 1;
	}

	const FBSave::ActorPlayer* wrole = FBSave::GetActorPlayer(buf);
	if (wrole == NULL) {
		return 2;
	}
	auto srcdata = wrole->basedata();
	if (srcdata == NULL)
		return 3;
	if (!getBackPack())
		return 5;
	bool syncEquip = false;
	MNSandbox::GetGlobalEvent().Emit<bool&, int>("ArchiveManager_getNeedSyncPlayerAttr", syncEquip, PLAYER_ATTR_EQUIPCUT);
	bool syncBagConf = false;
	MNSandbox::GetGlobalEvent().Emit<bool&, int>("ArchiveManager_getNeedSyncPlayerAttr", syncBagConf, PLAYER_ATTR_BAGCONF);
	bool syncShortcut = false;
	MNSandbox::GetGlobalEvent().Emit<bool&, int>("ArchiveManager_getNeedSyncPlayerAttr", syncShortcut, PLAYER_ATTR_SHORTCUT);
	bool syncExtBagConf = false;
	MNSandbox::GetGlobalEvent().Emit<bool&, int>("ArchiveManager_getNeedSyncPlayerAttr", syncExtBagConf, PLAYER_ATTR_EXTBAGCONF);

	if (syncEquip) {
		_loadPack(dynamic_cast<PackContainer*>(getBackPack()->getContainer(EQUIP_START_INDEX)), wrole->equips(), recorder);
	}
	if (syncBagConf) {
		_loadPack(dynamic_cast<PackContainer*>(getBackPack()->getContainer(BACKPACK_START_INDEX)), wrole->backpack(), recorder);
	}
	if (syncShortcut) {
		_loadPack(dynamic_cast<PackContainer*>(getBackPack()->getContainer(SHORTCUT_START_INDEX)), wrole->shortcut(), recorder);
	}
	if (syncExtBagConf) {
		_loadPack(dynamic_cast<PackContainer*>(getBackPack()->getContainer(EXT_BACKPACK_START_INDEX)), wrole->extbackpack(), recorder);
	}
	return 0;
}
#endif

void ClientPlayer::suddenIllnessBuffAttack() {

	if (m_pWorld == NULL) return;
	LivingAttrib* attrib = getLivingAttrib();
	if (attrib == NULL) return;
	auto targetComponent = getToAttackTargetComponent();
	if (attrib->hasBuff(SUDDEN_ILLNESS_BUFF))
	{
		PlayerControl* pPlayerCtrl = dynamic_cast<PlayerControl*>(this);
		ClientActor* target = NULL;
		if (targetComponent)
		{
			target = targetComponent->getTarget();
		}
		if (NULL == target || target->isDead()) {

			PlayerControl* pPlayerCtrl = dynamic_cast<PlayerControl*>(this);
			if (pPlayerCtrl)
			{

				if (!m_isBuffChangeViewMode)
				{
					pPlayerCtrl->tryStandup();//坐着的时候中buff则起来
					pPlayerCtrl->tryWakeup();//睡觉的时候中buff则起来
					int viewmode = pPlayerCtrl->getViewMode();
					m_oldBuffViewMode = viewmode;
					m_isBuffChangeViewMode = true;
					m_nSpectatortype = SPECTATOR_TYPE_OUTCONTROL;
					if (getGunComponent() && getGunComponent()->getZoom())
					{
						getGunComponent()->setZoom(false);//修复狙击枪开镜bug
					}
					//无法移动要关闭弹古筝页面
					MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("close_main_player_freeAutoGen", MNSandbox::SandboxContext(nullptr));
				}
				pPlayerCtrl->setViewMode(CAMERA_TPS_BACK_2);
			}

			int mindist = 99999;
			int num = selectAllCacheMobsNum(0, 0, 2000);
			WCoord pos = getPosition();
			ClientMob* nearMob = nullptr;
			for (size_t index = 0; index < num; index++)
			{
				ClientMob* mob = getCacheMob(index);
				int dist = pos.squareDistanceTo(mob->getPosition());
				if (dist < mindist)
				{
					mindist = dist;
					nearMob = mob;
				}
			}


			if (nearMob && nearMob->getObjType() != OBJ_TYPE_BOSS)
			{
				if (targetComponent)
				{
					targetComponent->setTarget(nearMob);
				}
			}
			else
			{
				ClientPlayer* player = getActorMgr()->selectNearPlayer(getPosition(), 2000, [](ClientActor* actor, void* userdata)->bool {
					if (actor == userdata) return false;
				return !actor->isDead();
				}, this);
				if (player)
				{
					if (targetComponent)
					{
						targetComponent->setTarget(player);
					}
				}
				else
				{
					m_suddenIllnessBuffTraceTick--;
					if (m_suddenIllnessBuffTraceTick <= 0)
					{
						m_suddenIllnessBuffTraceTick = 30;
						WCoord targetPos;
						getLocoMotion()->findRandTargetBlock(targetPos, 8, 0, NULL);
						tryMoveToPos(targetPos.x, targetPos.y, targetPos.z, 1.5);
					}
				}
			}
		}
		else {

			CollideAABB box;
			getCollideBox(box);
			double dist = BLOCK_SIZE;
			dist = dist * dist;
			WCoord targetpos = target->getLocoMotion()->getPosition();
			if (getSquareDistToPos(targetpos.x, targetpos.y, targetpos.z) <= dist)
			{
				m_suddenIllnessBuffTraceTick--;
				if (m_suddenIllnessBuffTraceTick <= 0)
				{
					m_suddenIllnessBuffTraceTick = 15;
					attackActor(target, SEQ_ATTACK);
				}
			}
			else {
				//setLookAt(target, 30.0, 30.0);
				m_suddenIllnessBuffTraceTick--;
				if (attrib->getMoveSpeed() != 0 && m_suddenIllnessBuffTraceTick <= 0)
				{
					m_suddenIllnessBuffTraceTick = 4 + GenRandomInt(0, 6);
					tryMoveToPos(targetpos.x, targetpos.y, targetpos.z, 1.5);
				}
			}

		}
	}
	else if (m_isBuffChangeViewMode)
	{
		m_nSpectatortype = SPECTATOR_TYPE_FREE;
		m_MotionCtrl.clear();
		if (targetComponent)
		{
			targetComponent->setTarget(NULL);
		}
		PlayerControl* pPlayerCtrl = dynamic_cast<PlayerControl*>(this);
		if (pPlayerCtrl)
		{
			m_isBuffChangeViewMode = false;
			pPlayerCtrl->setViewMode(m_oldBuffViewMode);
		}
	}
	else {
		if (targetComponent)
		{
			if (targetComponent->getTarget())
			{
				targetComponent->setTarget(NULL);
			}
		}
	}
}


#pragma endregion
#ifdef IWORLD_SERVER_BUILD	
void ClientPlayer::SetPvpActivityConfig(const game::ch::PB_PvpActivityConfigCH& msg)
{
	if (m_pvpActivityConfig.activityId > 0)
		return;
	// auto gp = GetClientInfoProxy();
	// const char* aid = gp->getEnterParam("toloadmapid");
	// long long owid = strtoll(aid, NULL, 10);
	// if (msg.mapid() != owid)
	// {
	// 	LOG_INFO("SetPvpActivityConfig, owid wrong %lld <-> %lld", msg.mapid(), owid);
	// 	return;
	// }

	// int year_s, month_s, day_s, hour_s, minute_s, second_s;
	// int ret = sscanf(msg.start_time().c_str(), "%d-%d-%d-%d-%d-%d", &year_s, &month_s, &day_s, &hour_s, &minute_s, &second_s);
	// if (ret != 6)
	// {
	// 	LOG_INFO("SetPvpActivityConfig, start err: %s", msg.start_time().c_str());
	// 	return;
	// }
	// tm tm_s;
	// tm_s.tm_year  = year_s - 1900;
	// tm_s.tm_mon   = month_s - 1;
	// tm_s.tm_mday  = day_s;
	// tm_s.tm_hour  = hour_s;
	// tm_s.tm_min   = minute_s;
	// tm_s.tm_sec   = second_s;
	// tm_s.tm_isdst = 0;
	// time_t t_s    = mktime(&tm_s);
	// tm     tm_e;
	// int    year_e, month_e, day_e, hour_e, minute_e, second_e;
	// ret = sscanf(msg.end_time().c_str(), "%d-%d-%d-%d-%d-%d", &year_e, &month_e, &day_e, &hour_e, &minute_e, &second_e);
	// if (ret != 6)
	// {
	// 	LOG_INFO("SetPvpActivityConfig, end err: %s", msg.end_time().c_str());
	// 	return;
	// }

	// tm_e.tm_year  = year_e - 1900;
	// tm_e.tm_mon   = month_e - 1;
	// tm_e.tm_mday  = day_e;
	// tm_e.tm_hour  = hour_e;
	// tm_e.tm_min   = minute_e;
	// tm_e.tm_sec   = second_e;
	// tm_e.tm_isdst = 0;
	// time_t t_e    = mktime(&tm_e);

	m_pvpActivityConfig.activityId = msg.activityid();
	m_pvpActivityConfig.start_time = msg.start_time();
	m_pvpActivityConfig.end_time = msg.end_time();
	m_pvpActivityConfig.timeZone = msg.timezone();
	m_pvpActivityConfig.commParam = msg.commparam();

	SLOG(INFO) << "pvp config uin=" << getUin() << " extrarule=" << (msg.has_extrarule() ? msg.extrarule() : "");
	jsonxx::Object json_ob;
	if (json_ob.parse(msg.extrarule())) {
		if (json_ob.has<jsonxx::Array>("item_list")) {
			const auto& values = json_ob.get<jsonxx::Array>("item_list").values();
			for (auto iter = values.begin(); iter != values.end(); ++iter) {
				if ((*iter)->is<jsonxx::Number>())
					setRecordItem((*iter)->get<jsonxx::Number>());
			}
		}
		if (json_ob.has<jsonxx::Array>("mob_list")) {
			const auto& values = json_ob.get<jsonxx::Array>("mob_list").values();
			for (auto iter = values.begin(); iter != values.end(); ++iter) {
				if ((*iter)->is<jsonxx::Number>())
					setRecordMonsterKill((*iter)->get<jsonxx::Number>());
			}
		}
	}
}

bool ClientPlayer::IsInPvpActivity()
{
	// auto gp = GetClientInfoProxy();
	// const char* t = gp->getEnterParam("debugpvp");
	const char* t = GetClientInfoProxy()->getEnterParam("debugpvp");
	if (t[0] != '\0')
		return true;

	if (m_pvpActivityConfig.activityId <= 0)
		return false;
	const time_t real_time = time(NULL);
	if (m_pvpActivityConfig.start_time <= real_time && m_pvpActivityConfig.end_time >= real_time)
		return true;
	return false;
}

uint32_t ClientPlayer::GetPvpActivityId()
{
	return m_pvpActivityConfig.activityId;
}
int32_t ClientPlayer::GetPvpTimezone()
{
	return m_pvpActivityConfig.timeZone;
}
const std::string& ClientPlayer::GetPvpCommonParam()
{
	return m_pvpActivityConfig.commParam;
}

void ClientPlayer::setRecordItem(int item_id)
{
	m_pvpActivityConfig.itemNeedRecord.insert(item_id);
}

void ClientPlayer::setRecordMonsterKill(int mid)
{
	m_pvpActivityConfig.mobNeedRecord.insert(mid);
}
#endif

#ifdef BUILD_MINI_EDITOR_APP
void ClientPlayer::SetBindChunk(bool toggle)
{
	Super::SetBindChunk(toggle);
	SetSerializableInChunk(true);
}

void ClientPlayer::setCurToolID(int nToolId)
{
	m_nCurToolId = nToolId;
}
#endif //BUILD_MINI_EDITOR_APP

#ifdef IWORLD_SERVER_BUILD
/**
 * @brief 遍历玩家背包计算某些物品的数量
 */
class ItemsCounter : public GridVisitor
{
public:
	ItemsCounter(const std::set<int>& item_set, std::map<int, unsigned>& count_map) :
		m_ItemIDNeedCount(item_set), m_ItemCount(count_map)
	{}
	void visit(const BackPackGrid* grid) {
		if (!grid)
			return;
		int item_id = grid->getItemID();
		if (item_id && m_ItemIDNeedCount.count(item_id))
			m_ItemCount[item_id] += grid->getNum();
	}
private:
	const std::set<int>& m_ItemIDNeedCount;  // 需要统计数量的物品id
	std::map<int, unsigned>& m_ItemCount;  // 记录统计后的物品数量 key:itemid, value: count
};

void ClientPlayer::PackPvpActivityData(miniw::pb_pvp_activity_player* pb_player)
{
	if (!pb_player)
		return;
	pb_player->set_uin(getUin());
	pb_player->set_result(getGameResults());
	pb_player->set_score(getGameScore());
	pb_player->set_kills(getTotalKill());
	pb_player->set_nickname(getNickname());
	pb_player->set_lang(getLang());
	pb_player->set_apiid(getApiid());

	int team_score = 0;
	GameMode* gmaker = static_cast<GameMode*>(g_WorldMgr->m_RuleMgr);
	if (gmaker)
		team_score = gmaker->getTeamScore(getTeam());
	pb_player->set_teamscore(team_score);

	int           xingxing_num = 0;
	PlayerAttrib* attr = getPlayerAttrib();
	if (attr)
		xingxing_num = attr->getExp() / EXP_STAR_RATIO;
	pb_player->set_xingxing(xingxing_num);
	pb_player->set_timezone(GetPvpTimezone());
	pb_player->set_commparam(GetPvpCommonParam());
	std::ostringstream log_pairs;
	{// 记录获取物品
		BackPack* back = getBackPack();
		if (back)
		{
			std::map<int, unsigned> items_count;
			ItemsCounter counter(m_pvpActivityConfig.itemNeedRecord, items_count);
			back->searchNormalPack(&counter);
			for (auto iter = items_count.begin(); iter != items_count.end(); ++iter) {
				if (!iter->second)
					continue;
				auto ii_pair = pb_player->add_itemgain();
				ii_pair->set_key(iter->first);
				ii_pair->set_count(iter->second);
				log_pairs << " i_" << iter->first << "_" << iter->second;
			}
		}
	}
	{// 记录杀死怪物
		const auto& map_ = getKillMonsterRecords();
		for (auto iter = map_.begin(); iter != map_.end(); ++iter) {
			auto ii_pair = pb_player->add_monsterkill();
			ii_pair->set_key(iter->first);
			ii_pair->set_count(iter->second);
			log_pairs << " m_" << iter->first << "_" << iter->second;
		}
	}
	SLOG(INFO) << "pvp uin:" << getUin()
		<< " result:" << getGameResults()
		<< " kill:" << getTotalKill()
		<< " score:" << getGameScore()
		<< " ts:" << team_score
		<< " star:" << xingxing_num
		<< log_pairs.str();

}

#endif // IWORLD_SERVER_BUILD


bool ClientPlayer::checkCanInteractPoseidonStatue()
{
	return m_InteractPoseidonStatue;
}

void ClientPlayer::setPoseidonStatueStates(bool value)
{
	m_InteractPoseidonStatue = value;
}

bool ClientPlayer::isAttrShapeShift()
{
	if (m_PlayerAttrib)
	{
		return m_PlayerAttrib->getAttrShapeShift();
	}
	return false;
}
//椰子跳过夜晚
bool ClientPlayer::isCoconutSkipNight()
{
	return m_IsSkipNight;
}
void ClientPlayer::setCoconutSkipNight(bool is)
{
	m_IsSkipNight = is;
	m_TickInDay = 0;
	if (is)
	{
		m_TickInDay = g_WorldMgr->getDayNightTime();
	}
	//广播给客机
	if (m_pWorld && !m_pWorld->isRemoteMode())
	{
		jsonxx::Object context;
		char objid_str[128];
		sprintf(objid_str, "%lld", getObjId());
		context << "objid" << objid_str;
		context << "night" << is;
		SandBoxManager::getSingletonPtr()->sendBroadCast("PB_COCONUT_SKIP_NIGHT", context.bin(), context.binLen());
	}
}
void ClientPlayer::setCoconutHit(bool is)
{
	m_TickInHit = 0;
	m_IsCoconutHit = is;
	//中断
	m_CurOperate = PLAYEROP_NULL;
	m_OperateTicks = 0;
	//广播给客机
	if (m_pWorld && !m_pWorld->isRemoteMode())
	{
		jsonxx::Object context;
		char objid_str[128];
		sprintf(objid_str, "%lld", getObjId());
		context << "objid" << objid_str;
		context << "hit" << is;
		SandBoxManager::getSingletonPtr()->sendBroadCast("PB_COCONUT_HIT", context.bin(), context.binLen());
	}
}
bool ClientPlayer::isCoconutHit()
{
	return m_IsCoconutHit;
}
void ClientPlayer::updateCoconutSkipNight()
{
	setCoconutHit(false);
	setCoconutSkipNight(false);
	if (m_pWorld == nullptr || g_WorldMgr == nullptr)
	{
		return;
	}
	if (!m_pWorld->isRemoteMode())
	{
		if ((m_TickInDay > 0) && (g_WorldMgr->getDayNightTime() == TICKS_ONEDAY))
		{
			//流逝的tick
			int nOffsetWorldTime = TICKS_ONEDAY - m_TickInDay;
			m_WorldTimes[m_pWorld->getCurMapID()] += nOffsetWorldTime;
			g_WorldMgr->setWorldTime(g_WorldMgr->getWorldTime() + nOffsetWorldTime);
		}
	}
}
int	ClientPlayer::getCoconutTimer()
{
	return m_SkipNightTime;
}

//野人伙伴战斗号角
void ClientPlayer::battleHornVillager()
{
	auto battle = [this](int village_uid)
	{
		std::vector<WORLD_ID> allVillagers = GetWorldManagerPtr()->getWorldInfoManager()->getAllVillagers(village_uid);
		for (size_t i = 0; i < allVillagers.size(); i++)
		{
			auto actor = getActorMgr()->findActorByWID(allVillagers[i]);
			if (actor)
			{
				if (auto Villager = dynamic_cast<ActorVillager*>(actor))
				{
					if (Villager->getTamedOwnerID() == getObjId())//必须是主人
					{
						bool succ = actor->Event2().Emit<bool, ClientPlayer*>("beGuardForPlayerOnTrigger", m_battleHornVillager, this);
					}
				}
			}
		}
	};
	m_battleHornVillager = !m_battleHornVillager;
	battle(getObjId());
	battle(0);//野生村庄
	//注意，这里传的是玩家的ID

	//if (m_battleHornVillager)
	//{
	//	this->getBody()->setHeadDisplayIcon(12621, 20);
	//}
}

void ClientPlayer::SetTreeItemIndex(int itemid, int index)
{
	m_treeItemIndex = index;
}

int ClientPlayer::GetTreeItemIndex()
{
	return m_treeItemIndex;
}

void ClientPlayer::UseColorBrush()
{
	Rainbow::Vector3f direction = getCameraLookDir();
	Rainbow::Vector3f eyePos = getEyePosition().toVector3();

	float yaw, pitch;
	Direction2PitchYaw(&yaw, &pitch, direction);


	PlayerControl* player_control = dynamic_cast<PlayerControl*>(this);
	if ((player_control != g_pPlayerCtrl) || (player_control == NULL))
	{
		// 如果是客机，从GunLogic获取客机同步过来的精确位置和朝向
		Rainbow::Vector3f tmp;
		if (this->getGunLogical()) this->getGunLogical()->getDir(yaw, pitch, tmp);
		eyePos = tmp;
	}
	else if (g_pPlayerCtrl)
	{
		eyePos = player_control->m_pCamera->getEyePos().toVector3();
		pitch = player_control->m_pCamera->m_RotatePitch;
		yaw = player_control->m_pCamera->m_RotateYaw + 180.0f;
	}

	Vector3f dir;
	PitchYaw2Direction(dir, yaw, pitch);

	MINIW::WorldRay ray;
	ray.m_Origin = WorldPos::initVector3(eyePos.x, eyePos.y, eyePos.z);
	ray.m_Dir = dir;
	ray.m_Range = 64 * BLOCK_FSIZE; // 最远32格

	ActorExcludes excludes;
	IntersectResult presult;
	excludes.addActorWithRiding(this);
	//ClientPlayer *player = checkPlayerByMsg2Host(uin);
	//if (player == NULL) return;
	//excludes.addActorWithRiding(m_PlayerCtrl);
	int intertype = (int)this->getWorld()->pickAll(ray, &presult, excludes, PICK_METHOD_CLICK);
	if (intertype == 1)
	{
		//UGCEcosysParams params;
		//params.type = index;
		////MKTODO:替换方块颜色
		WCoord blockpos = presult.block;
		DirectionType face = presult.face;
		int blockid = this->getWorld()->getBlockID(blockpos);
		int color = this->getSelectedColor();
		//player->getWorld()->setBlockAll(blockPos.x, blockPos.y, blockPos.z, blockid, blockdata, 2);
		if (IsDyeableBlock(blockid))
		{
			MINIW::ScriptVM::game()->callFunction("OnColorBrushItemUse", "iiiu[World]iii", blockpos.x, blockpos.y, blockpos.z, this->getWorld(), blockid, color, face);
		}
	}
	return;
}
bool ClientPlayer::HaveExtBackPack()
{
	BackPackGrid* pCurTool = getPlayerAttrib()->getEquipGridWithType(EQUIP_PIFENG);
	if (pCurTool && !pCurTool->isEmpty())
	{
		int itemid = pCurTool->getItemID();
		const ToolDef* tooldef = GetDefManagerProxy()->getToolDef(itemid);
		if (tooldef && tooldef->Type == 30)
		{
			return true;
		}

	}
	return false;
}

int ClientPlayer::GetExtBackPackGridCount()
{
	BackPackGrid* pCurTool = getPlayerAttrib()->getEquipGridWithType(EQUIP_PIFENG);
	if (pCurTool && !pCurTool->isEmpty())
	{
		int itemid = pCurTool->getItemID();
		const ToolDef* tooldef = GetDefManagerProxy()->getToolDef(itemid);
		if (tooldef->Type == 30)
		{
			switch (tooldef->Level)
			{
			case 1:
				return GetLuaInterfaceProxy().get_lua_const()->level1ExtBackPack;
				break;
			case 2:
				return GetLuaInterfaceProxy().get_lua_const()->level2ExtBackPack;
				break;
			case 3:
				return GetLuaInterfaceProxy().get_lua_const()->level3ExtBackPack;
				break;
			default:
				break;
			}
		}

	}
	return 0;
}

void ClientPlayer::attrShapeShiftTick()
{
	if (m_AttrRightClickTick < 9999)
		m_AttrRightClickTick++;

	if (m_AttrShapeShiftTick == 9999)
		m_AttrShapeShiftTick = 0;

	m_AttrShapeShiftTick++;
	if (m_AttrStopRecoverTick > 0)
		m_AttrStopRecoverTick--;

	if (!m_pWorld && !m_PlayerAttrib) return;
	std::vector<StatusAttInfo> infoList;
	m_PlayerAttrib->getStatusAddAttInfo(BUFFATTRT_ATTR_SHAPESHIFT, infoList);
	if (infoList.size() > 0)
	{
		for (unsigned int i = 0; i < infoList.size(); i++)
		{
			const StatusAttInfo& info = infoList[i];
			if (info.vValue.size() >= 3)
			{
				if (info.vValue[0].value == 3224) // 海灵守卫
				{
					if (m_Body && m_Body->getModel())// 海灵守卫需要一直保持最亮模型
					{
						m_Body->getModel()->SetInstanceData(Rainbow::Vector4f(0.8f, 0.8f, 0.8f, 0));
					}
					if (m_AttrShapeShiftTick == 20)
					{
						auto effectComponent = getEffectComponent();
						if (effectComponent)
						{
							effectComponent->playBodyEffect("sea_08_penshui", true);
						}
					}
					bool ret = getLocoMotion()->m_InWater;
					if (!ret)
					{
						auto coord = WCoord(0, 0, 0);
						auto findComponent = getFindComponent();
						if (findComponent)
						{
							ret = findComponent->findNearestBlock(coord, 3, 1);
						}
						if (!ret)
						{
							auto coord_ = WCoord(0, 0, 0);
							if (findComponent)
							{
								ret = findComponent->findNearestBlock(coord_, 4, 1);
							}
						}
					}
					if (m_AttrShapeShiftTick % 20 == 0)
					{
						int hp = info.vValue[1].value;
						if (ret)
						{
							if (m_AttrStopRecoverTick == 0)
								m_PlayerAttrib->addHP(hp);
						}
						else
						{
							auto atkComponent = getAttackedComponent();
							if (atkComponent)
							{
								atkComponent->attackedFromType(ATTACK_SUFFOCATE, 2.0f * GetLuaInterfaceProxy().get_lua_const()->default_shanghai_beilv);
							}
							if (m_PlayerAttrib->m_AttrShapeShiftDef)
							{
								char path[256];
								sprintf(path, "entity/%s/male3.png", m_PlayerAttrib->m_AttrShapeShiftDef->Model.c_str());
								if (getBody())
									getBody()->changeBodyTex(path, "rtexbody");
								if (!m_pWorld->isRemoteMode())
								{
									jsonxx::Object context;
									context << "male" << 3;
									SandBoxManager::getSingleton().sendToClient(getUin(), "PB_ATTR_SHAPE_SHIFT_SYNC", context.bin(), context.binLen());
									return;
								}
							}
						}
					}
					int time = info.vValue[2].value;
					if (ret && m_AttrStopRecoverTick == 0 && m_PlayerAttrib->m_AttrShapeShiftDef)
					{
						char path[256];
						sprintf(path, "entity/%s/male.png", m_PlayerAttrib->m_AttrShapeShiftDef->Model.c_str());
						if (getBody())
							getBody()->changeBodyTex(path, "rtexbody");
						if (!m_pWorld->isRemoteMode())
						{
							jsonxx::Object context;
							context << "male" << 0;
							SandBoxManager::getSingleton().sendToClient(getUin(), "PB_ATTR_SHAPE_SHIFT_SYNC", context.bin(), context.binLen());
							return;
						}
					}
				}
			}
		}
	}
}


bool ClientPlayer::checkIsSnowMan(ClientActor* actor)
{
	if (!actor)
	{
		return false;
	}
	int actorId = actor->getDefID();
	return actorId == 3916;
}

void ClientPlayer::setFlying(bool b)
{
	ActorLiving::setFlying(b);
	if (GetCheatHandler())
		GetCheatHandler()->onFlyModeChange(b);
}

void ClientPlayer::playWeaponMotion(std::string motion, bool resetPlay /*= true*/, int motionClass /*= 0*/, float motionScale /*= 1.0f*/, bool isSync /*= true*/, int playType, bool tpsPlay)
{
	if (m_CameraModel && m_CameraModel->isShow())
	{
		m_CameraModel->playItemMotion(motion.c_str(), resetPlay, motionClass, motionScale);
	}
	// 其他人称视角
	else if (getBody() && tpsPlay)
	{
		getBody()->playWeaponMotion(motion.c_str(), resetPlay, motionClass, motionScale, playType);
	}

	if (isSync)
	{
		if (!m_pWorld)
			return;

		if (m_pWorld->isRemoteMode())
		{
			PB_PlayWeaponEffectCH playWeaponEffectCH;
			playWeaponEffectCH.set_effectname(motion.c_str());
			playWeaponEffectCH.set_effectid(motionClass);
			playWeaponEffectCH.set_effectscale(int(motionScale * 100));
			playWeaponEffectCH.set_effectstatus(0);
			playWeaponEffectCH.set_objid(getObjId());
			GetGameNetManagerPtr()->sendToHost(PB_PLAYWEAPONEFFECT_CH, playWeaponEffectCH);
		}
		else
		{
			PB_PlayWeaponEffectHC playWeaponEffectHC;
			playWeaponEffectHC.set_effectname(motion.c_str());
			playWeaponEffectHC.set_effectid(motionClass);
			playWeaponEffectHC.set_effectscale(int(motionScale * 100));
			playWeaponEffectHC.set_effectstatus(0);
			playWeaponEffectHC.set_objid(getObjId());
			m_pWorld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_PLAYWEAPONEFFECT_HC, playWeaponEffectHC, this);
		}
	}
}

void ClientPlayer::SetPlayerAttrViewRange(int range)
{
	m_attrViewRange = range;
}

#ifdef SURVIVAL_ACTOR_SUPPORT_SANDBOX_NODEEX
bool ClientPlayer::NeedSupportExSandboxsInfo() const
{
#ifdef SANDBOX_DEV // 带测验，暂不开放
	if (MNSandbox::Config::GetSingleton().IsSandboxMode()) // studio 制作的地图，不需要player 的拓展
		return false;
#endif
	return true;
}
#endif// SURVIVAL_ACTOR_SUPPORT_SANDBOX_NODEEX

void ClientPlayer::addMotion(float x, float y, float z)
{
	if (getLocoMotion())
	{
		getLocoMotion()->addMotion(x, y, z);
	}
}

bool ClientPlayer::ComboAttackStateRunning()
{
	if (!dataAttackDef)
		return false;

	return (m_StateController && m_StateController->getActionState() == "ComboAttack") || ComboAttackCalculate;
}

void ClientPlayer::processBuildBlock(int processType, const WCoord& blockpos, int upgradenum)
{
	if (!m_pWorld->isRemoteMode())
	{
		auto pmtl = m_pWorld->getBlockMaterial(blockpos);
		if (pmtl)
		{
			if (processType == 1)
			{
				// 升级
				pmtl->onBlockUpGrade(m_pWorld, blockpos, upgradenum, this);
			}
			else if (processType == 2)
			{
				// 拆除
				pmtl->onBlockDestroyedBy(m_pWorld, blockpos, 0, BLOCK_DESTROY_PLAYER, this);
			}
		}
	}
	else
	{
		PB_ProcessBuildBlockCH builddata;
		builddata.set_x(blockpos.x);
		builddata.set_y(blockpos.y);
		builddata.set_z(blockpos.z);
		builddata.set_type(processType);
		if (processType == 1) builddata.set_level(upgradenum);

		GetGameNetManagerPtr()->sendToHost(PB_PROCESS_BUILD_CH, builddata);
		
		// 获取方块ID
		int blockId = m_pWorld->getBlockID(blockpos.x, blockpos.y, blockpos.z);
		
		// 获取当前工具ID
		int toolId = getCurToolID();
		
		// 构建位置信息的JSON字符串
		std::string locationJson = "{\"x\":" + std::to_string(blockpos.x) + ",\"y\":" + std::to_string(blockpos.y) + ",\"z\":" + std::to_string(blockpos.z) + "}";
		
		// 获取消耗资源信息
		std::string costJson = "[]";
		
		// 尝试获取建筑容器以获取实际的资源消耗
		auto container = m_pWorld->getContainerMgr()->getContainer(blockpos);
		auto archContainer = dynamic_cast<containerArchitecture*>(container);
		
		if (archContainer)
		{
			// 参考 containerArchitecture::checkArchitectureResEnough 获取资源消耗
			if (processType == 1) // 升级
			{
				// 获取建筑蓝图数据
				int blueprintTypeId = archContainer->getBluePrintTypeId();
				const ArchitecturalBlueprintCsvDef* blueprintDef = GetDefManagerProxy()->getArchitecturalBlueprintCsvDef(blueprintTypeId);
				if (blueprintDef)
				{
					std::map<int, int> requiredMaterials;
					
					// 根据升级等级获取所需材料
					if (upgradenum == 1) // WoodStyle
					{
						if (blueprintDef->Wood.ConsumeItemID > 0 && blueprintDef->Wood.Count > 0) {
							requiredMaterials[blueprintDef->Wood.ConsumeItemID] = blueprintDef->Wood.Count;
						}
					}
					else if (upgradenum == 2) // StoneStyle
					{
						if (blueprintDef->Stone.ConsumeItemID > 0 && blueprintDef->Stone.Count > 0) {
							requiredMaterials[blueprintDef->Stone.ConsumeItemID] = blueprintDef->Stone.Count;
						}
					}
					else if (upgradenum == 3) // IronStyle
					{
						if (blueprintDef->Iron.ConsumeItemID > 0 && blueprintDef->Iron.Count > 0) {
							requiredMaterials[blueprintDef->Iron.ConsumeItemID] = blueprintDef->Iron.Count;
						}
					}
					else if (upgradenum == 4) // SteelStyle
					{
						if (blueprintDef->Steel.ConsumeItemID > 0 && blueprintDef->Steel.Count > 0) {
							requiredMaterials[blueprintDef->Steel.ConsumeItemID] = blueprintDef->Steel.Count;
						}
					}
					
					// 构建消耗资源的JSON字符串
					costJson = "[";
					bool first = true;
					for (const auto& material : requiredMaterials) {
						if (!first) {
							costJson += ",";
						}
						costJson += "{\"item_id\":" + std::to_string(material.first) + ",\"count\":" + std::to_string(material.second) + "}";
						first = false;
					}
					costJson += "]";
				}
			}
			else if (processType == 2) // 拆除
			{
				// 拆除时获取返还的材料（按0.1比例返还）
				int blueprintTypeId = archContainer->getBluePrintTypeId();
				int blueprintLevel = archContainer->getBluePrintLevel();
				const ArchitecturalBlueprintCsvDef* blueprintDef = GetDefManagerProxy()->getArchitecturalBlueprintCsvDef(blueprintTypeId);
				if (blueprintDef)
				{
					std::map<int, int> returnMaterials;
					float returnProportion = 0.1f; // 参考代码中的拆除返还比例
					
					// 根据当前建筑等级获取返还材料
					if (blueprintLevel == 1) // WoodStyle
					{
						if (blueprintDef->Wood.ConsumeItemID > 0 && blueprintDef->Wood.Count > 0) {
							int returnCount = std::ceil(blueprintDef->Wood.Count * returnProportion);
							if (returnCount > 0) {
								returnMaterials[blueprintDef->Wood.ConsumeItemID] = returnCount;
							}
						}
					}
					else if (blueprintLevel == 2) // StoneStyle
					{
						if (blueprintDef->Stone.ConsumeItemID > 0 && blueprintDef->Stone.Count > 0) {
							int returnCount = std::ceil(blueprintDef->Stone.Count * returnProportion);
							if (returnCount > 0) {
								returnMaterials[blueprintDef->Stone.ConsumeItemID] = returnCount;
							}
						}
					}
					else if (blueprintLevel == 3) // IronStyle
					{
						if (blueprintDef->Iron.ConsumeItemID > 0 && blueprintDef->Iron.Count > 0) {
							int returnCount = std::ceil(blueprintDef->Iron.Count * returnProportion);
							if (returnCount > 0) {
								returnMaterials[blueprintDef->Iron.ConsumeItemID] = returnCount;
							}
						}
					}
					else if (blueprintLevel == 4) // SteelStyle
					{
						if (blueprintDef->Steel.ConsumeItemID > 0 && blueprintDef->Steel.Count > 0) {
							int returnCount = std::ceil(blueprintDef->Steel.Count * returnProportion);
							if (returnCount > 0) {
								returnMaterials[blueprintDef->Steel.ConsumeItemID] = returnCount;
							}
						}
					}
					
					// 构建返还资源的JSON字符串
					costJson = "[";
					bool first = true;
					for (const auto& material : returnMaterials) {
						if (!first) {
							costJson += ",";
						}
						costJson += "{\"item_id\":" + std::to_string(material.first) + ",\"count\":" + std::to_string(material.second) + "}";
						first = false;
					}
					costJson += "]";
				}
			}
		}
		else
		{
			// 如果不是建筑容器，记录基本的方块信息
			if (processType == 2) // 拆除
			{
				costJson = "[{\"item_id\":" + std::to_string(blockId) + ",\"count\":1}]";
			}
		}
		
		// 构建升级信息的JSON字符串
		std::string upgradeInfoJson = "{\"upgrade_num\":" + std::to_string(upgradenum) + ",\"block_id\":" + std::to_string(blockId) + "}";
		
		// 建筑行为数据统计埋点
		GameAnalytics::TrackEvent("building_action", {
			{"action_type", GameAnalytics::Value(processType)},              // int 1是升级，2是拆除
			{"bp_type", GameAnalytics::Value(blockId)},                      // 方块类型
			{"tool_id", GameAnalytics::Value(toolId)},                       // 使用的工具ID
			{"location", GameAnalytics::Value(locationJson.c_str())},        // 方块坐标
			{"cost", GameAnalytics::Value(costJson.c_str())},                // 消耗的资源列表
			{"upgrade_info", GameAnalytics::Value(upgradeInfoJson.c_str())}, // 升级信息
			{"uin", GameAnalytics::Value(getUin())}                          // 玩家UIN
		});
	}
}

bool ClientPlayer::openEmitterContainer()
{
	if (!getUsingEmitter())
	{
		return false;
	}
	auto container = getEmitterWorldContainer();
	if (!container)
	{
		return false;
	}
	if (openContainer(container)) return true;
	return false;
}

bool ClientPlayer::_mountAndOpenEmitterContainer(const WCoord& blockpos)
{
	auto pworld = getWorld();
	if (!pworld)
	{
		return false;
	}
	//if (getMountType() != 0)
	//{
	//	return false;
	//}
	ContainerManualEmitter* container = dynamic_cast<ContainerManualEmitter*>(pworld->getContainerMgr()->getContainer(blockpos));
	if (container)
	{
		if (container->attachPlayer(this))
		{
			if (!pworld->isRemoteMode())
			{
				return openContainer(container);
			}
			else return true;
		}
	}
	return false;
}

ContainerManualEmitter* ClientPlayer::getEmitterContainer()
{
	if (getUsingEmitter())
	{
		if (getWorld() && getWorld()->getContainerMgr())
		{
			return dynamic_cast<ContainerManualEmitter*>(getWorld()->getContainerMgr()->getContainer(m_emitterPos));
		}
	}
	return nullptr;
}

WorldContainer* ClientPlayer::getEmitterWorldContainer()
{
	if (getUsingEmitter())
	{
		if (getWorld() && getWorld()->getContainerMgr())
		{
			return getWorld()->getContainerMgr()->getContainer(m_emitterPos);
		}
	}
	return nullptr;
}

bool ClientPlayer::disMountEmitter(const WCoord& blockpos)
{
	if (!m_pWorld)
	{
		return false;
	}
	if (!getUsingEmitter())
	{
		return false;
	}
	if (m_pWorld->isRemoteMode())
	{
		//发给主机
		jsonxx::Object object;
		object << "uin" << getUin();
		object << "type" << 2;
		object << "x" << blockpos.x;
		object << "y" << blockpos.y;
		object << "z" << blockpos.z;
		int size = 0;
		unsigned char* p = NULL;
		object.saveBinary(p, size);
		SandBoxManager::getSingletonPtr()->sendToHost((char*)("MANUAL_EMITTER"), p, size);
		free(p);
		return true;
	}
	else
	{
		if (_disMountEmitter(blockpos))
		{
			//发给客机
			jsonxx::Object object;
			object << "uin" << getUin();
			object << "type" << 2;
			object << "x" << blockpos.x;
			object << "y" << blockpos.y;
			object << "z" << blockpos.z;
			int size = 0;
			unsigned char* p = NULL;
			object.saveBinary(p, size);
			SandBoxManager::getSingletonPtr()->sendBroadCast((char*)("MANUAL_EMITTER"), p, size);
			free(p);
			return true;
		}
	}
	return false;
}

bool ClientPlayer::mountEmitter(const WCoord& blockpos)
{
	if (!m_pWorld)
	{
		return false;
	}
	//if (getMountType() != 0)
	//{
	//	return false;
	//}
	if (getUsingEmitter())
	{
		return false;
	}
	if (!m_pWorld->isRemoteMode())
	{
		if (_mountAndOpenEmitterContainer(blockpos))
		{
			//发给客机
			jsonxx::Object object;
			object << "uin" << getUin();
			object << "type" << 1;
			object << "x" << blockpos.x;
			object << "y" << blockpos.y;
			object << "z" << blockpos.z;
			int size = 0;
			unsigned char* p = NULL;
			object.saveBinary(p, size);
			SandBoxManager::getSingletonPtr()->sendBroadCast((char*)("MANUAL_EMITTER"), p, size);
			free(p);
			return true;
		}
		return false;
	}
	return _mountEmitter(blockpos);
}

bool ClientPlayer::operateManualEmitter(const WCoord& blockpos)
{
	if (!m_pWorld)
	{
		return false;
	}
	if (!getUsingEmitter())
	{
		return false;
	}
	auto emitter = getEmitterContainer();
	if (!emitter)
	{
		return false;
	}
	if (m_pWorld->isRemoteMode())
	{
		//发给主机
		jsonxx::Object object;
		object << "uin" << getUin();
		object << "type" << 3;
		object << "x" << blockpos.x;
		object << "y" << blockpos.y;
		object << "z" << blockpos.z;
		int size = 0;
		unsigned char* p = NULL;
		object.saveBinary(p, size);
		SandBoxManager::getSingletonPtr()->sendToHost((char*)("MANUAL_EMITTER"), p, size);
		free(p);
		return true;
	}
	else
	{
		if (!emitter->checkPermit(this))
		{
			return false;
		}
		if (!_operateManualEmitter(blockpos))
		{
			return false;
		}
		//发给客机
		jsonxx::Object object;
		object << "uin" << getUin();
		object << "type" << 3;
		object << "x" << blockpos.x;
		object << "y" << blockpos.y;
		object << "z" << blockpos.z;
		int size = 0;
		unsigned char* p = NULL;
		object.saveBinary(p, size);
		SandBoxManager::getSingletonPtr()->sendBroadCast((char*)("MANUAL_EMITTER"), p, size);
		free(p);
		return true;
	}
	return false;
}


bool ClientPlayer::disMountEmitterClient(const WCoord& blockpos)
{
	return _disMountEmitter(blockpos);
}

bool ClientPlayer::operateManualEmitterClient(const WCoord& blockpos)
{
	return _operateManualEmitter(blockpos);
}

bool ClientPlayer::_disMountEmitter(const WCoord& blockpos)
{
	if (!getUsingEmitter())
	{
		return false;
	}
	if (getUsingEmitterBlockPos() != blockpos)
	{
		assert(0);
		return false;
	}
	closeContainer();
	setSitting(false);
	auto container = getEmitterContainer();
	if (container)
	{
		container->dettachPlayer();
		setPosition(container->getCurEmitterGetDownPos());
	}
	else
	{
		setUsingEmitter(false);
		setEmitterBlockPos(WCoord(0, 0, 0));
		//恢复玩家的包围盒
		updateBound(180, 60);
		if (hasUIControl())
		{
			PlayerControl* pcontrol = dynamic_cast<PlayerControl*>(this);
			if (pcontrol)
			{
				pcontrol->setCurShortcut(getCurShortcut());
			}
		}
		else
		{
			applyEquips(EQUIP_WEAPON);
		}
	}
	if (hasUIControl())
	{
		//GetGameEventQue().postRidingChange(0);
		MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
			SetData_Number("ridetype", 0);
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GIE_RIDING_CHANGE", sandboxContext);
		}
	}
	return true;
}

bool ClientPlayer::_mountEmitter(const WCoord& blockpos)
{
	auto pworld = getWorld();
	if (!pworld)
	{
		return false;
	}
	//if (getMountType() != 0)
	//{
	//	return false;
	//}
	ContainerManualEmitter* container = dynamic_cast<ContainerManualEmitter*>(pworld->getContainerMgr()->getContainer(blockpos));
	if (container)
	{
		return container->attachPlayer(this);
	}
	return false;
}

bool ClientPlayer::_operateManualEmitter(const WCoord& blockpos)
{
	if (!getUsingEmitter())
	{
		return false;
	}
	if (getUsingEmitterBlockPos() != blockpos)
	{
		assert(0);
		return false;
	}
	auto container = getEmitterContainer();
	if (container)
	{
		return container->doEmit() >= 0;
	}
	return false;
}

int ClientPlayer::doSimulativePick(const Rainbow::WorldPos& originPos, const Rainbow::Vector3f& dir, int range, IntersectResult& result, bool ignorecarried)
{
	if (!m_pWorld)
	{
		return 0;
	}
	if (range == 0)
		return 0;
	MINIW::WorldRay ray;
	ray.m_Origin = originPos;
	ray.m_Dir = dir;
	//Rainbow::Ray ray;
	ray.m_Range = range;

	result.liquids.resize(0);
	result.intersect_actor = result.intersect_block = false;

	ActorExcludes excludes;
	excludes.addActorWithRiding(this);
	if (ignorecarried)
	{
		auto CarryComp = getCarryComponent();
		if (CarryComp)
		{
			auto* carrying = CarryComp->getCarringActor();
			if (carrying)
			{
				excludes.addActor(carrying);
			}
		}
	}
	int type = (int)(m_pWorld->pickAll(ray, &result, excludes, PICK_METHOD_CLICK, 0, WCoord()));
	return type;
}

void ClientPlayer::updatePhysCollisionBetweenPlayers()
{
	if (getLocoMotion())
		static_cast<PlayerLocoMotion*>(getLocoMotion())->updatePhysCollisionBetweenPlayers();
}

void ClientPlayer::BindItemComponent(MNSandbox::SceneComponent* pComponent)
{
	m_pItemComponent = dynamic_cast<ItemUseComponent*>(pComponent);
}

void ClientPlayer::BindGunComponent(MNSandbox::SceneComponent* pComponent)
{
	m_pGunComponent = dynamic_cast<GunUseComponent*>(pComponent);
}

void ClientPlayer::BindCustomGunComponent(MNSandbox::SceneComponent* pComponent)
{
	m_pCustomGunComponent = dynamic_cast<CustomGunUseComponent*>(pComponent);
}

void ClientPlayer::BindFishLineComponent(MNSandbox::SceneComponent* pComponent)
{
	//m_pFishLineComponent = dynamic_cast<FishLineComponent*>(pComponent);
}

FishLineComponent* ClientPlayer::GetFishLineComponent()
{
	return m_pFishLineComponent;
}

void ClientPlayer::OnChangeTool(bool bHold)
{
	if (m_pFishLineComponent)
		m_pFishLineComponent->OnChangeTool(bHold);
}
bool ClientPlayer::ShowFishLine(bool bShow)
{
	if (!m_pFishLineComponent)
	{
		m_pFishLineComponent = CreateComponent<FishLineComponent>("FishLineComponent");
	}

	m_pFishLineComponent->ShowLine(bShow);
	
	return true;	
}

void ClientPlayer::SetFishLineEndPoint(int x, int y, int z)
{
	if (!m_pFishLineComponent)
		return;

	m_pFishLineComponent->SetEndPos(x, y, z);
}

void ClientPlayer::gotoTeleportPos()
{
	if (m_TeleportPosition.isZero())
		return;
	ActorLocoMotion* locmove = getLocoMotion();
	if (locmove)
	{
		locmove->setPosition(m_TeleportPosition.x, m_TeleportPosition.y, m_TeleportPosition.z);
	}
	auto RidComp = getRiddenComponent();
	if (RidComp && RidComp->isRiding()) //玩家坐骑需要设置 坐骑的位置
	{
		auto ridingActor = RidComp->getRidingActor();
		if (ridingActor && ridingActor->getObjType() != OBJ_TYPE_MINECART)
		{
			ridingActor->getLocoMotion()->gotoPosition(m_TeleportPosition);
		}
	}
	m_TeleportPosition.x = 0;
	m_TeleportPosition.y = 0;
	m_TeleportPosition.z = 0;
}

void ClientPlayer::setTeleportPos(const WCoord& pos)
{
	m_TeleportPosition = pos;
	if (m_MoveControl && m_MoveControl->isActive())
	{
		m_MoveControl->onSyncToPos(pos, getLocoMotion()->getMotion());
	}
}

void ClientPlayer::ToggleDisplayNameVisible(bool visible)
{
	if (visible)
	{
		applyDisplayName();
	}
	else
	{
		getBody()->setDispayName(NULL, 0);
	}
}

void ClientPlayer::writePolaroidAlbumInfo(int gridIndex, std::string jsonStr)
{
	BackPack* backpack = getBackPack();
	if (!backpack)
		return;
	BackPackGrid* grid = backpack->index2Grid(gridIndex);
	if (!grid)
		return;
	if (grid->getItemID() != ITEM_POLAROID_ALBUM && grid->getItemID() != ITEM_POLAROID_RARE_ALBUM)
		return;
	jsonxx::Object albumInfoObj;
	//如果不是完整的json数据
	if (!albumInfoObj.parse(jsonStr))
		return;
	grid->userdata_str = jsonStr;
}

void ClientPlayer::tryTakeoutAllItem(int baseIndex, int blockId, int blockX, int blockY, int blockZ)
{
	if (!m_pWorld)
		return;

	WorldContainerMgr* pContainerMgr = m_pWorld->getContainerMgr();
	if (!pContainerMgr) 
		return;
	WorldStorageBox* pStorageBox = pContainerMgr->getStorageBox(blockX, blockY, blockZ);
	if (!pStorageBox)
		return;

	if (m_pWorld->isRemoteMode())
	{
		//客机得话就通知主机去拿取
		jsonxx::Object objInfo;
		objInfo << "baseindex" << baseIndex;
		objInfo << "blockid" << blockId;
		objInfo << "blockx" << blockX;
		objInfo << "blocky" << blockY;
		objInfo << "blockz" << blockZ;
		objInfo << "operator" << m_ObjId;
		GetSandBoxManagerPtr()->sendToHost("PB_STORAGE_BOX_TAKE_OUT_ALL", objInfo.bin(), objInfo.binLen());
		return;
	}

	BackPack* pBackpack = getBackPack();
	if (!pBackpack)
		return;
	
	std::map<int, vector<int>> shortCutIdToIndexMap;
	//先判断是否有相同的item，有的话判断堆叠数是否满了，如果满了就使用空的，没满就叠到满
	//先判断快捷栏，然后再去看背包
	int shortcutStartIndex = getShortcutStartIndex();
	PackContainer* pShortPackContainer = pBackpack->getPack(shortcutStartIndex);
	if (pShortPackContainer)
	{
		int SHORTCUT_SLOTS = pShortPackContainer->getGridCount();
		for (int i = 0; i < SHORTCUT_SLOTS; ++i)
		{
			BackPackGrid& pBackPackGrid = pShortPackContainer->m_Grids[i];
			if (pBackPackGrid.isEmpty())
				continue;
			int itemID = pBackPackGrid.getItemID();
			int shortcutIndex = shortcutStartIndex + i;
			auto iter = shortCutIdToIndexMap.find(itemID);
			if (iter == shortCutIdToIndexMap.cend())
			{
				vector<int> shortcutIndexVec;
				shortcutIndexVec.push_back(shortcutIndex);
				shortCutIdToIndexMap.insert(std::make_pair(itemID, shortcutIndexVec));
			}
			else
			{
				iter->second.push_back(shortcutIndex);
			}
		}
	}

	std::map<int, vector<int>> backPackIdToIndexMap;
	PackContainer* pBackPackContainer = pBackpack->getPack(BACKPACK_START_INDEX);
	if (pBackPackContainer)
	{
		for (int i = 0; i < 30; ++i)
		{
			BackPackGrid& pBackPackGrid = pBackPackContainer->m_Grids[i];
			if (pBackPackGrid.isEmpty())
				continue;
			int itemID = pBackPackGrid.getItemID();
			int backpackIndex = BACKPACK_START_INDEX + i;
			auto iter = backPackIdToIndexMap.find(itemID);
			if (iter == backPackIdToIndexMap.cend())
			{
				vector<int> shortcutIndexVec;
				shortcutIndexVec.push_back(backpackIndex);
				backPackIdToIndexMap.insert(std::make_pair(itemID, shortcutIndexVec));
			}
			else
			{
				iter->second.push_back(backpackIndex);
			}
		}
	}
	
	int gridCount = pStorageBox->getGridCount();
	for (int i = 0; i < gridCount; ++i)
	{
		int storageBoxBaseIndex = baseIndex + i;
		BackPackGrid* pGrid = pStorageBox->index2Grid(storageBoxBaseIndex);
		if (pGrid->isEmpty())
			continue;

		int itemid = pGrid->getItemID();
		auto shortcutIter = shortCutIdToIndexMap.find(itemid);
		if (shortcutIter != shortCutIdToIndexMap.cend())
		{
			//先把能堆叠的都堆叠到最大，然后再把剩下的往空位上放
			for (int j = 0; j < shortcutIter->second.size(); ++j)
			{
				int shortcutIndex = shortcutIter->second[j];
				BackPackGrid* shortcutGrid = pBackpack->index2Grid(shortcutIndex);
				if (!shortcutGrid)
					continue;
				if (shortcutGrid->getNum() >= shortcutGrid->getMaxStack())
					continue;
				int enableStackNum = shortcutGrid->getMaxStack() - shortcutGrid->getNum();
				//小于可堆叠的数量就直接堆叠
				if (pGrid->getNum() <= enableStackNum)
				{
					pBackpack->moveItem(storageBoxBaseIndex, shortcutIndex,pGrid->getNum());
					pGrid->clear();
					break;
				}
				else
				{
					pBackpack->moveItem(storageBoxBaseIndex, shortcutIndex, enableStackNum);
				}
			}
		}

		if (pGrid->isEmpty())
			continue;

		auto backpackIter = backPackIdToIndexMap.find(itemid);
		if (backpackIter != backPackIdToIndexMap.cend())
		{
			//先把能堆叠的都堆叠到最大，然后再把剩下的往空位上放
			for (int j = 0; j < backpackIter->second.size(); ++j)
			{
				int backpackIndex = backpackIter->second[j];
				BackPackGrid* backpackGrid = pBackpack->index2Grid(backpackIndex);
				if (!backpackGrid)
					continue;
				if (backpackGrid->getNum() >= backpackGrid->getMaxStack())
					continue;
				int enableStackNum = backpackGrid->getMaxStack() - backpackGrid->getNum();
				//小于可堆叠的数量就直接堆叠
				if (pGrid->getNum() <= enableStackNum)
				{
					pBackpack->moveItem(storageBoxBaseIndex, backpackIndex, pGrid->getNum());
					pGrid->clear();
					break;
				}
				else
				{
					pBackpack->moveItem(storageBoxBaseIndex, backpackIndex, enableStackNum);
				}
			}
		}

		if (pGrid->isEmpty())
			continue;

		int emptyShortcutIndex = pBackpack->getEmptyShortcutIndex();
		//先判断快捷栏有没有位置，然后判断背包有没有位置，都没有就产生掉落物
		if (emptyShortcutIndex == -1)
			emptyShortcutIndex = pBackpack->getEmptyBagIndex();
		if (emptyShortcutIndex == -1)
			continue;
		pBackpack->moveItem(storageBoxBaseIndex, emptyShortcutIndex, pGrid->getNum());
		//放到空位后将原位置清除
		pGrid->clear();
	}
}
Rainbow::IActorBody* ClientPlayer::iGetBody()
{
	return getBody();
}

IActorLocoMotion* ClientPlayer::GetPlayerLocoMotion()
{
	return getLocoMotion();
}

bool ClientPlayer::BlockBookCabinetVehicleDir(Rainbow::Vector3f& viewDir, Rainbow::Vector3f& centerDir, World* pworld, const WCoord& blockpos)
{
	if (pworld->isVehicleWorld())
	{
		VehicleWorld* pvworld = dynamic_cast<VehicleWorld*>(pworld);
		PlayerControl* pplayer = dynamic_cast<PlayerControl*>(this);
		if (pplayer && pvworld)
		{
			ActorVehicleAssemble* vehicle = pvworld->getActorVehicleAssemble();
			if (vehicle)
			{
				viewDir = pplayer->getLookDir();
				VehicleAssembleLocoMotion* vehicleLoco = dynamic_cast<VehicleAssembleLocoMotion*>(vehicle->getLocoMotion());
				WCoord realPos = vehicle->getRealWorldPosWithPos(blockpos);
				realPos += WCoord(0, BLOCK_HALFSIZE, 0);
				WCoord eyepos = pplayer->getEyePosition();
				centerDir = realPos.toVector3() - eyepos.toVector3();
				centerDir = MINIW::Normalize(centerDir);

				return true;
			}
		}
	}
	return false;
}

bool ClientPlayer::GetGunZoom()
{
	return getGunLogical() != NULL ? getGunLogical()->getZoom() : false;
}
void ClientPlayer::doWaterCanoonSkill()
{
	getGunLogical()->doWaterCanoonSkill();
}
IClientActor* ClientPlayer::GetCurAccountHorse()
{
	return getCurAccountHorse();
}
int ClientPlayer::getEquipItem(EQUIP_SLOT_TYPE t)
{
	if (getPlayerAttrib())
	{
		return getPlayerAttrib()->getEquipItemWithType(t);
	}
	return -1;
}

BackPackGrid* ClientPlayer::getEquipGrid(EQUIP_SLOT_TYPE t)
{
	if (getLivingAttrib())
	{
		return getLivingAttrib()->getEquipGrid(t);
	}
	return nullptr;
}

BackPackGrid* ClientPlayer::getEquipGridWithType(EQUIP_SLOT_TYPE t)
{
	if (getLivingAttrib())
	{
		return getLivingAttrib()->getEquipGridWithType(t);
	}
	return nullptr;
}

int ClientPlayer::damageEquipItem(EQUIP_SLOT_TYPE t, int damage)
{
	if (getPlayerAttrib())
	{
		return getPlayerAttrib()->damageEquipItemWithType(t, damage);
	}
	return -1;
}

IClientActor* ClientPlayer::GetCatchBall()
{
	return getCatchBall();
}
IClientActor* ClientPlayer::GetPlatformIClientActor()
{
	return GetPlatform();
}


void ClientPlayer::resetChunkViewer()
{
	m_ChunkViewer.enterWorld(m_pWorld, getPosition(), ClientPlayer::GetCurViewRange(this)/*getCurViewRange()*/);
}

void ClientPlayer::SetOffline(bool offline)
{
	if (getPlayerAttrib())
	{
		getPlayerAttrib()->setOffLine(offline);
	}
	setNewPlayer(false);  // 首次登陆的玩家标记需要修改一下
}

bool ClientPlayer::IsOffline() 
{
	if (getPlayerAttrib())
	{
		return getPlayerAttrib()->getOffLine();
	}
	return false;
}
// 用于调试枪械 的lua逻辑进行热重载 --- by charlesxie
void ClientPlayer::ResetAllStates()
{
	if (m_StateController)
	{
		GetCoreLuaDirector().UnloadLuaFile("sandboxengine/sandboxengine/sandboxplay/playerstate/GunUseState.lua");
		m_StateController->resetAllStates();
	}
}

bool ClientPlayer::isMoving()
{
	LivingLocoMotion* loc = dynamic_cast<LivingLocoMotion*>(getLocoMotion());
	if (loc)
	{
		if (loc->m_MoveForward != 0 || loc->m_MoveStrafing != 0 ||
			(((!IsOnPlatform() && !loc->isSlopeFalling())) && (loc->m_TickPosition.m_LastTickPos.x != loc->m_Position.x || loc->m_TickPosition.m_LastTickPos.z != loc->m_Position.z)))
		{
			return true;
		}
	}
	return false;
}

MOVE_DIR_ANIM_TYPE ClientPlayer::getMoveDirAnimType()
{
	if (m_MoveRight == 0 && m_MoveForward > 0)
		return MOVE_DIR_ANIM_FORWARD;
	else if (m_MoveRight == 0 && m_MoveForward < 0)
		return MOVE_DIR_ANIM_BACKWARD;
	else if (m_MoveRight > 0 && m_MoveForward == 0)
		return MOVE_DIR_ANIM_RIGHT;
	else if (m_MoveRight < 0 && m_MoveForward == 0)
		return MOVE_DIR_ANIM_LEFT;
	else if (m_MoveRight < 0 && m_MoveForward > 0)
		return MOVE_DIR_ANIM_LEFT_FORWARD;
	else if (m_MoveRight > 0 && m_MoveForward > 0)
		return MOVE_DIR_ANIM_RIGHT_FORWARD;
	else if (m_MoveRight > 0 && m_MoveForward < 0)
		return MOVE_DIR_ANIM_RIGHT_BACKWARD;
	else if (m_MoveRight < 0 && m_MoveForward < 0)
		return MOVE_DIR_ANIM_LEFT_BACKWARD;
	return MOVE_DIR_ANIM_TYPE_MAX;
}
